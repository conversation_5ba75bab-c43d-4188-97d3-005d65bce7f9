# 前端Docker忽略文件
# 优化前端多语言站点构建性能

# ===========================================
# 版本控制
# ===========================================
.git
.gitignore

# ===========================================
# 依赖和缓存
# ===========================================
**/node_modules
**/.npm
**/.yarn
npm-debug.log*
yarn-debug.log*
.pnpm-debug.log*

# ===========================================
# 构建输出 (保留源码，排除构建结果)
# ===========================================
**/dist
**/build
**/.astro

# ===========================================
# 开发工具
# ===========================================
.vscode
.idea
**/*.log

# ===========================================
# 环境配置
# ===========================================
.env
.env.*
*.env

# ===========================================
# 测试文件
# ===========================================
**/*.test.ts
**/*.test.js
**/*.spec.ts
**/*.spec.js
**/coverage

# ===========================================
# Docker相关
# ===========================================
Dockerfile*
.dockerignore
docker-compose*.yml

# ===========================================
# 临时文件
# ===========================================
**/*.tmp
**/temp
**/.cache

# ===========================================
# 系统文件
# ===========================================
.DS_Store
Thumbs.db

# ===========================================
# 其他
# ===========================================
README.md
docs