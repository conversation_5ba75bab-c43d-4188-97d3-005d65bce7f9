{"name": "petcare-blog-frontend", "version": "1.0.0", "description": "Pet blog multilingual frontend sites", "private": true, "scripts": {"dev:en": "cd en && npm run dev", "dev:de": "cd de && npm run dev", "dev:ru": "cd ru && npm run dev", "dev:admin": "cd admin && npm run dev", "build:en": "cd en && npm run build", "build:de": "cd de && npm run build", "build:ru": "cd ru && npm run build", "build:admin": "cd admin && npm run build", "build:all": "npm run build:en && npm run build:de && npm run build:ru && npm run build:admin", "preview:en": "cd en && npm run preview", "preview:de": "cd de && npm run preview", "preview:ru": "cd ru && npm run preview", "test": "echo \"Running tests for all sites\"", "test:e2e": "echo \"Running E2E tests\""}, "keywords": ["pet", "blog", "multilingual", "astro", "ssg"], "author": "", "license": "ISC"}