# 宠物博客前端多语言站点 Dockerfile
# 支持 Astro 4.0+ SSG 构建
FROM node:20-alpine as build-stage

# 设置构建参数
ARG SITE_DIR=en
ARG BUILD_ENV=production

# 安装系统依赖
RUN apk add --no-cache git

# 设置工作目录
WORKDIR /app

# 复制根目录package.json
COPY package*.json ./

# 复制特定站点的package.json
COPY ${SITE_DIR}/package*.json ./${SITE_DIR}/

# 安装依赖
RUN cd ${SITE_DIR} && npm ci

# 复制源代码
COPY ${SITE_DIR}/ ./${SITE_DIR}/

# 构建站点
WORKDIR /app/${SITE_DIR}
RUN npm run build

# 生产阶段 - 使用nginx服务静态文件
FROM nginx:alpine as production-stage

# 安装系统依赖
RUN apk add --no-cache \
    dumb-init

# 复制构建文件到nginx目录
COPY --from=build-stage /app/${SITE_DIR}/dist /usr/share/nginx/html

# 复制nginx配置
COPY nginx.conf /etc/nginx/nginx.conf

# 创建nginx运行用户
RUN addgroup -g 1001 -S nginx-app \
    && adduser -S nginx-app -u 1001 -G nginx-app

# 设置文件权限
RUN chown -R nginx-app:nginx-app /usr/share/nginx/html \
    && chown -R nginx-app:nginx-app /var/cache/nginx \
    && chown -R nginx-app:nginx-app /etc/nginx

# 暴露端口
EXPOSE 80

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD wget --no-verbose --tries=1 --spider http://localhost/health || exit 1

# 切换到非root用户并启动nginx
USER nginx-app
ENTRYPOINT ["dumb-init", "--"]
CMD ["nginx", "-g", "daemon off;"]