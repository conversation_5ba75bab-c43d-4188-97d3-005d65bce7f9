{"name": "petcare-blog-de", "version": "1.0.0", "description": "Haustiere Blog - German Site", "type": "module", "scripts": {"dev": "astro dev --port 4322", "start": "astro dev", "build": "astro build", "preview": "astro preview", "astro": "astro", "test": "vitest", "test:ui": "vitest --ui", "test:run": "vitest run", "test:coverage": "vitest run --coverage", "test:watch": "vitest --watch", "lint": "eslint src/**/*.{ts,tsx,astro}", "lint:fix": "eslint src/**/*.{ts,tsx,astro} --fix", "format": "prettier --write \"src/**/*.{ts,tsx,astro,css}\"", "format:check": "prettier --check \"src/**/*.{ts,tsx,astro,css}\"", "typecheck": "astro check"}, "keywords": ["pet", "blog", "german", "deutsch", "astro"], "author": "", "license": "ISC", "engines": {"node": ">=20.0.0"}, "dependencies": {"astro": "^5.6.1", "@astrojs/tailwind": "^6.1.0", "@astrojs/typescript": "^0.7.0", "tailwindcss": "^3.4.0"}, "devDependencies": {"@astrojs/check": "^0.9.0", "@testing-library/dom": "^10.4.0", "@testing-library/jest-dom": "^6.5.0", "@types/node": "^22.7.8", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "@vitest/coverage-v8": "^2.1.6", "@vitest/ui": "^2.1.6", "eslint": "^9.32.0", "eslint-plugin-astro": "^1.3.0", "happy-dom": "^15.11.6", "prettier": "^3.6.2", "prettier-plugin-astro": "^0.15.1", "typescript": "^5.9.2", "vitest": "^2.1.6"}}