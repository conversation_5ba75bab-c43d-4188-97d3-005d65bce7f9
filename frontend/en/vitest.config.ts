/**
 * Vitest配置 - 英文站点
 * 配置Astro组件和工具函数的测试环境
 */

import { defineConfig } from 'vitest/config';
import { getViteConfig } from 'astro/config';

export default defineConfig(
  getViteConfig({
    test: {
      // 测试环境配置
      environment: 'happy-dom',
      
      // 测试文件匹配模式
      include: [
        'src/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
        'tests/**/*.{test,spec}.{js,mjs,cjs,ts,mts,cts,jsx,tsx}',
      ],
      
      // 排除文件
      exclude: [
        'node_modules',
        'dist',
        '.astro',
        'coverage',
      ],
      
      // 全局测试设置
      globals: true,
      
      // 设置文件
      setupFiles: ['./tests/setup.ts'],
      
      // 覆盖率配置
      coverage: {
        provider: 'v8',
        reporter: ['text', 'json', 'html', 'lcov'],
        exclude: [
          'coverage/**',
          'dist/**',
          '.astro/**',
          'node_modules/**',
          'tests/**',
          '**/*.d.ts',
          '**/*.config.*',
          '**/*.test.*',
          '**/*.spec.*',
        ],
        thresholds: {
          global: {
            branches: 70,
            functions: 70,
            lines: 70,
            statements: 70,
          },
        },
      },
      
      // 测试超时
      testTimeout: 10000,
      
      // 并行运行
      pool: 'forks',
      
      // 监听模式配置
      watchExclude: [
        'node_modules/**',
        'dist/**',
        '.astro/**',
        'coverage/**',
      ],
      
      // 报告器
      reporter: ['verbose'],
      
      // 测试环境变量
      env: {
        NODE_ENV: 'test',
        ASTRO_SITE: 'https://www.petcare.test',
        ASTRO_BASE: '/',
      },
    },
    
    // 解析配置
    resolve: {
      alias: {
        '@': '/src',
        '@components': '/src/components',
        '@layouts': '/src/layouts',
        '@pages': '/src/pages',
        '@styles': '/src/styles',
        '@lib': '/src/lib',
        '@utils': '/src/utils',
      },
    },
  })
);