/**
 * 示例测试文件 - 英文站点
 * 演示Vitest测试框架的使用方法和验证配置是否正确
 */

import { describe, it, expect, vi, beforeEach } from 'vitest';
import { testUtils, mockApi } from './setup';

describe('测试框架验证', () => {
  describe('测试工具函数', () => {
    it('应该创建Mock文章数据', () => {
      const article = testUtils.createMockArticle();
      
      expect(article).toHaveProperty('id');
      expect(article).toHaveProperty('title');
      expect(article).toHaveProperty('slug');
      expect(article.title).toBe('Test Article Title');
      expect(article.language_code).toBe('en-US');
    });

    it('应该创建自定义Mock文章数据', () => {
      const customArticle = testUtils.createMockArticle({
        title: 'Custom Article Title',
        view_count: 200,
      });
      
      expect(customArticle.title).toBe('Custom Article Title');
      expect(customArticle.view_count).toBe(200);
      expect(customArticle.slug).toBe('test-article-title'); // 保持默认值
    });

    it('应该创建Mock分类数据', () => {
      const category = testUtils.createMockCategory();
      
      expect(category).toHaveProperty('id');
      expect(category).toHaveProperty('name');
      expect(category).toHaveProperty('slug');
      expect(category.name).toBe('Test Category');
    });

    it('应该创建Mock评论数据', () => {
      const comment = testUtils.createMockComment();
      
      expect(comment).toHaveProperty('id');
      expect(comment).toHaveProperty('content');
      expect(comment).toHaveProperty('author_name');
      expect(comment.status).toBe('approved');
    });
  });

  describe('DOM操作测试', () => {
    beforeEach(() => {
      // 清理DOM
      document.body.innerHTML = '';
    });

    it('应该能查找DOM元素', async () => {
      // 创建测试元素
      const testDiv = document.createElement('div');
      testDiv.id = 'test-element';
      testDiv.textContent = 'Test Content';
      
      setTimeout(() => {
        document.body.appendChild(testDiv);
      }, 100);
      
      // 使用工具函数等待元素出现
      const element = await testUtils.waitForElement('#test-element');
      expect(element).toBeDefined();
      expect(element.textContent).toBe('Test Content');
    });

    it('应该能模拟用户事件', () => {
      // 创建测试按钮
      const button = document.createElement('button');
      button.id = 'test-button';
      document.body.appendChild(button);
      
      // 添加事件监听器
      let clicked = false;
      button.addEventListener('click', () => {
        clicked = true;
      });
      
      // 模拟点击事件
      testUtils.simulateUserEvent(button, 'click');
      
      expect(clicked).toBe(true);
    });

    it('应该处理DOM变化', () => {
      const container = document.createElement('div');
      container.innerHTML = '<p>Original content</p>';
      document.body.appendChild(container);
      
      // 修改内容
      container.innerHTML = '<p>Updated content</p>';
      
      const paragraph = container.querySelector('p');
      expect(paragraph?.textContent).toBe('Updated content');
    });
  });

  describe('Mock API测试', () => {
    beforeEach(() => {
      vi.clearAllMocks();
    });

    it('应该Mock API成功响应', async () => {
      const testData = { id: 1, name: 'Test Data' };
      mockApi.mockFetch('/api/test', testData);
      
      const response = await fetch('/api/test');
      const data = await response.json();
      
      expect(response.ok).toBe(true);
      expect(data.success).toBe(true);
      expect(data.data).toEqual(testData);
      expect(data.meta).toHaveProperty('timestamp');
    });

    it('应该Mock API错误响应', async () => {
      mockApi.mockError('Test error', 'TEST_ERROR', 400);
      
      const response = await fetch('/api/test');
      const data = await response.json();
      
      expect(response.ok).toBe(false);
      expect(response.status).toBe(400);
      expect(data.success).toBe(false);
      expect(data.error.code).toBe('TEST_ERROR');
      expect(data.error.message).toBe('Test error');
    });

    it('应该验证fetch调用', async () => {
      const mockData = { articles: [testUtils.createMockArticle()] };
      mockApi.mockFetch('/api/articles', mockData);
      
      await fetch('/api/articles');
      
      expect(fetch).toHaveBeenCalledTimes(1);
      expect(fetch).toHaveBeenCalledWith('/api/articles');
    });
  });

  describe('环境配置测试', () => {
    it('应该设置正确的环境变量', () => {
      expect(process.env.NODE_ENV).toBe('test');
      expect(process.env.ASTRO_SITE).toBe('https://www.petcare.test');
    });

    it('应该Mock全局对象', () => {
      expect(window.matchMedia).toBeDefined();
      expect(global.IntersectionObserver).toBeDefined();
      expect(global.ResizeObserver).toBeDefined();
      expect(global.fetch).toBeDefined();
    });
  });

  describe('异步操作测试', () => {
    it('应该处理Promise', async () => {
      const result = await Promise.resolve('test result');
      expect(result).toBe('test result');
    });

    it('应该处理异步函数', async () => {
      const asyncFunction = async (value: string) => {
        await new Promise(resolve => setTimeout(resolve, 10));
        return `processed: ${value}`;
      };
      
      const result = await asyncFunction('test');
      expect(result).toBe('processed: test');
    });

    it('应该处理Promise错误', async () => {
      const errorFunction = async () => {
        throw new Error('Test error');
      };
      
      await expect(errorFunction()).rejects.toThrow('Test error');
    });
  });

  describe('工具函数测试示例', () => {
    // 这里可以添加实际的工具函数测试
    it('应该测试URL生成函数', () => {
      const generateSlug = (title: string) => {
        return title.toLowerCase()
                  .replace(/[^a-z0-9\s-]/g, '')
                  .replace(/\s+/g, '-')
                  .replace(/-+/g, '-')
                  .trim('-');
      };
      
      expect(generateSlug('Test Article Title')).toBe('test-article-title');
      expect(generateSlug('Article with Special @#$ Characters')).toBe('article-with-special-characters');
      expect(generateSlug('Multiple    Spaces   Here')).toBe('multiple-spaces-here');
    });

    it('应该测试日期格式化函数', () => {
      const formatDate = (date: Date) => {
        return date.toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'long',
          day: 'numeric',
        });
      };
      
      const testDate = new Date('2025-01-20T10:00:00Z');
      expect(formatDate(testDate)).toBe('January 20, 2025');
    });

    it('应该测试数据验证函数', () => {
      const validateEmail = (email: string) => {
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(email);
      };
      
      expect(validateEmail('<EMAIL>')).toBe(true);
      expect(validateEmail('invalid-email')).toBe(false);
      expect(validateEmail('user@domain')).toBe(false);
      expect(validateEmail('')).toBe(false);
    });
  });

  describe('本地化测试', () => {
    it('应该使用正确的语言代码', () => {
      const currentLang = 'en-US';
      expect(currentLang).toBe('en-US');
      
      // 测试本地化文本
      const texts = {
        'en-US': {
          welcome: 'Welcome to PetCare',
          readMore: 'Read More',
        },
      };
      
      expect(texts[currentLang].welcome).toBe('Welcome to PetCare');
      expect(texts[currentLang].readMore).toBe('Read More');
    });

    it('应该格式化本地化日期', () => {
      const date = new Date('2025-01-20T10:00:00Z');
      const formatted = date.toLocaleDateString('en-US');
      expect(formatted).toMatch(/\d{1,2}\/\d{1,2}\/\d{4}/);
    });
  });
});