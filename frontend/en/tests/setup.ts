/**
 * Vitest测试环境设置 - 英文站点
 * 配置测试工具、Mock和全局设置
 */

import { beforeAll, afterAll, beforeEach, afterEach, vi } from 'vitest';
import '@testing-library/jest-dom/vitest';

// 全局测试配置
beforeAll(() => {
  // 设置测试环境变量
  process.env.NODE_ENV = 'test';
  process.env.ASTRO_SITE = 'https://www.petcare.test';
  
  console.log('🧪 Vitest setup complete for English site');
});

afterAll(() => {
  // 测试清理
  console.log('✅ Vitest cleanup complete');
});

beforeEach(() => {
  // 清除所有mock调用记录
  vi.clearAllMocks();
});

afterEach(() => {
  // 恢复所有mock
  vi.restoreAllMocks();
});

// Mock全局对象
Object.defineProperty(window, 'matchMedia', {
  writable: true,
  value: vi.fn().mockImplementation(query => ({
    matches: false,
    media: query,
    onchange: null,
    addListener: vi.fn(), // deprecated
    removeListener: vi.fn(), // deprecated
    addEventListener: vi.fn(),
    removeEventListener: vi.fn(),
    dispatchEvent: vi.fn(),
  })),
});

// Mock IntersectionObserver
global.IntersectionObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock ResizeObserver
global.ResizeObserver = vi.fn().mockImplementation(() => ({
  observe: vi.fn(),
  unobserve: vi.fn(),
  disconnect: vi.fn(),
}));

// Mock fetch
global.fetch = vi.fn();

// 扩展全局测试工具
declare global {
  namespace Vi {
    interface TestUtils {
      createMockArticle: (overrides?: any) => any;
      createMockCategory: (overrides?: any) => any;
      createMockComment: (overrides?: any) => any;
      waitForElement: (selector: string, timeout?: number) => Promise<Element>;
      simulateUserEvent: (element: Element, event: string, options?: any) => void;
    }
  }
  
  var testUtils: Vi.TestUtils;
}

// 测试工具函数
global.testUtils = {
  /**
   * 创建Mock文章数据
   */
  createMockArticle(overrides = {}) {
    return {
      id: 1,
      title: 'Test Article Title',
      slug: 'test-article-title',
      summary: 'This is a test article summary for testing purposes.',
      content: '<p>This is the main content of the test article.</p>',
      featured_image: '/images/test-article.jpg',
      category: {
        id: 1,
        name: 'Test Category',
        slug: 'test-category',
      },
      author: {
        id: 1,
        username: 'testauthor',
        email: '<EMAIL>',
      },
      meta_title: 'Test Article Meta Title',
      meta_description: 'Test article meta description for SEO.',
      published_at: new Date('2025-01-20T10:00:00Z'),
      view_count: 100,
      comment_count: 5,
      tags: ['test', 'article', 'sample'],
      status: 'published',
      language_code: 'en-US',
      ...overrides,
    };
  },

  /**
   * 创建Mock分类数据
   */
  createMockCategory(overrides = {}) {
    return {
      id: 1,
      name: 'Test Category',
      slug: 'test-category',
      description: 'This is a test category description.',
      parent_id: null,
      children: [],
      article_count: 10,
      sort_order: 1,
      ...overrides,
    };
  },

  /**
   * 创建Mock评论数据
   */
  createMockComment(overrides = {}) {
    return {
      id: 1,
      article_id: 1,
      parent_id: null,
      author_name: 'Test User',
      author_email: '<EMAIL>',
      content: 'This is a test comment content.',
      status: 'approved',
      created_at: new Date('2025-01-20T12:00:00Z'),
      replies: [],
      ...overrides,
    };
  },

  /**
   * 等待元素出现
   */
  async waitForElement(selector: string, timeout = 5000): Promise<Element> {
    return new Promise((resolve, reject) => {
      const element = document.querySelector(selector);
      if (element) {
        resolve(element);
        return;
      }

      const observer = new MutationObserver(() => {
        const element = document.querySelector(selector);
        if (element) {
          observer.disconnect();
          resolve(element);
        }
      });

      observer.observe(document.body, {
        childList: true,
        subtree: true,
      });

      setTimeout(() => {
        observer.disconnect();
        reject(new Error(`Element ${selector} not found within ${timeout}ms`));
      }, timeout);
    });
  },

  /**
   * 模拟用户事件
   */
  simulateUserEvent(element: Element, event: string, options = {}) {
    const eventObj = new Event(event, {
      bubbles: true,
      cancelable: true,
      ...options,
    });
    
    element.dispatchEvent(eventObj);
  },
};

// API Mock工具
export const mockApi = {
  /**
   * Mock API响应
   */
  mockResponse: (data: any, success = true, status = 200) => ({
    ok: success,
    status,
    json: () => Promise.resolve({
      success,
      data: success ? data : undefined,
      error: success ? undefined : data,
      meta: { timestamp: new Date().toISOString() },
    }),
    text: () => Promise.resolve(JSON.stringify(data)),
  }),

  /**
   * Mock fetch请求
   */
  mockFetch: (url: string, response: any) => {
    (global.fetch as any).mockImplementationOnce(() => 
      Promise.resolve(mockApi.mockResponse(response))
    );
  },

  /**
   * Mock API错误
   */
  mockError: (message: string, code = 'API_ERROR', status = 500) => {
    (global.fetch as any).mockImplementationOnce(() => 
      Promise.resolve(mockApi.mockResponse({ code, message }, false, status))
    );
  },
};

// 导出测试工具
export { testUtils, mockApi };