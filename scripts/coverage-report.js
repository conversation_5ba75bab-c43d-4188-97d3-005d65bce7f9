#!/usr/bin/env node
/**
 * 测试覆盖率报告聚合脚本
 * 汇总后端和多个前端站点的覆盖率数据
 */

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// 颜色输出工具
const colors = {
  green: '\x1b[32m',
  red: '\x1b[31m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m',
  bright: '\x1b[1m',
};

// 输出带颜色的日志
function log(color, message) {
  console.log(`${colors[color]}${message}${colors.reset}`);
}

// 执行命令并返回Promise
function execCommand(command, cwd = process.cwd()) {
  return new Promise((resolve, reject) => {
    const [cmd, ...args] = command.split(' ');
    const child = spawn(cmd, args, { 
      cwd, 
      stdio: 'inherit',
      env: { ...process.env, NODE_ENV: 'test' }
    });
    
    child.on('close', (code) => {
      if (code === 0) {
        resolve();
      } else {
        reject(new Error(`Command failed: ${command} (exit code: ${code})`));
      }
    });
  });
}

// 读取覆盖率JSON文件
function readCoverageFile(filePath) {
  try {
    if (fs.existsSync(filePath)) {
      const content = fs.readFileSync(filePath, 'utf8');
      return JSON.parse(content);
    }
    return null;
  } catch (error) {
    log('yellow', `⚠️  无法读取覆盖率文件: ${filePath}`);
    return null;
  }
}

// 计算覆盖率百分比
function calculateCoverage(summary) {
  if (!summary || !summary.total) return 0;
  
  const { covered, total } = summary.total;
  return total > 0 ? Math.round((covered / total) * 100) : 0;
}

// 格式化覆盖率数据
function formatCoverage(name, coverageData) {
  if (!coverageData || !coverageData.total) {
    return { name, lines: 0, branches: 0, functions: 0, statements: 0 };
  }
  
  const { lines, branches, functions, statements } = coverageData.total;
  
  return {
    name,
    lines: calculateCoverage({ total: lines }),
    branches: calculateCoverage({ total: branches }),
    functions: calculateCoverage({ total: functions }),
    statements: calculateCoverage({ total: statements }),
  };
}

// 生成覆盖率HTML报告
function generateHtmlReport(coverageData) {
  const html = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>PetCare Blog - 测试覆盖率报告</title>
  <style>
    body { font-family: Arial, sans-serif; margin: 20px; background: #f5f5f5; }
    .container { max-width: 1200px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
    h1 { color: #333; text-align: center; margin-bottom: 30px; }
    .summary { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 15px; margin-bottom: 30px; }
    .metric { background: #f8f9fa; padding: 15px; border-radius: 6px; text-align: center; border-left: 4px solid #007bff; }
    .metric h3 { margin: 0 0 10px 0; color: #495057; font-size: 14px; text-transform: uppercase; }
    .metric .value { font-size: 24px; font-weight: bold; color: #333; }
    .good { border-left-color: #28a745; }
    .warning { border-left-color: #ffc107; }
    .error { border-left-color: #dc3545; }
    table { width: 100%; border-collapse: collapse; margin-top: 20px; }
    th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }
    th { background-color: #f8f9fa; font-weight: 600; }
    .coverage-bar { width: 100px; height: 20px; background: #e9ecef; border-radius: 10px; overflow: hidden; display: inline-block; }
    .coverage-fill { height: 100%; transition: width 0.3s; }
    .timestamp { text-align: center; color: #6c757d; font-size: 12px; margin-top: 20px; }
  </style>
</head>
<body>
  <div class="container">
    <h1>🧪 PetCare Blog 测试覆盖率报告</h1>
    
    <div class="summary">
      ${coverageData.map(item => {
        const avgCoverage = Math.round((item.lines + item.branches + item.functions + item.statements) / 4);
        const cssClass = avgCoverage >= 80 ? 'good' : avgCoverage >= 60 ? 'warning' : 'error';
        return `
          <div class="metric ${cssClass}">
            <h3>${item.name}</h3>
            <div class="value">${avgCoverage}%</div>
          </div>
        `;
      }).join('')}
    </div>
    
    <table>
      <thead>
        <tr>
          <th>模块</th>
          <th>语句覆盖率</th>
          <th>分支覆盖率</th>
          <th>函数覆盖率</th>
          <th>行覆盖率</th>
        </tr>
      </thead>
      <tbody>
        ${coverageData.map(item => `
          <tr>
            <td><strong>${item.name}</strong></td>
            <td>
              <div class="coverage-bar">
                <div class="coverage-fill" style="width: ${item.statements}%; background-color: ${getCoverageColor(item.statements)};"></div>
              </div>
              ${item.statements}%
            </td>
            <td>
              <div class="coverage-bar">
                <div class="coverage-fill" style="width: ${item.branches}%; background-color: ${getCoverageColor(item.branches)};"></div>
              </div>
              ${item.branches}%
            </td>
            <td>
              <div class="coverage-bar">
                <div class="coverage-fill" style="width: ${item.functions}%; background-color: ${getCoverageColor(item.functions)};"></div>
              </div>
              ${item.functions}%
            </td>
            <td>
              <div class="coverage-bar">
                <div class="coverage-fill" style="width: ${item.lines}%; background-color: ${getCoverageColor(item.lines)};"></div>
              </div>
              ${item.lines}%
            </td>
          </tr>
        `).join('')}
      </tbody>
    </table>
    
    <div class="timestamp">
      报告生成时间: ${new Date().toLocaleString('zh-CN')}
    </div>
  </div>
  
  <script>
    function getCoverageColor(percentage) {
      if (percentage >= 80) return '#28a745';
      if (percentage >= 60) return '#ffc107';
      return '#dc3545';
    }
  </script>
</body>
</html>`;

  return html.replace(/getCoverageColor/g, `
    function getCoverageColor(percentage) {
      if (percentage >= 80) return '#28a745';
      if (percentage >= 60) return '#ffc107';
      return '#dc3545';
    }
    getCoverageColor`);
}

// 主要执行逻辑
async function main() {
  log('blue', '🚀 开始生成测试覆盖率报告...');
  
  try {
    // 1. 运行后端测试覆盖率
    log('blue', '📊 运行后端测试覆盖率...');
    await execCommand('npm test -- --coverage', path.join(__dirname, '../backend'));
    
    // 2. 运行前端测试覆盖率
    const frontendSites = ['en', 'de', 'ru'];
    for (const site of frontendSites) {
      log('blue', `📊 运行 ${site.toUpperCase()} 站点测试覆盖率...`);
      try {
        await execCommand('npm test -- --coverage', path.join(__dirname, `../frontend/${site}`));
      } catch (error) {
        log('yellow', `⚠️  ${site.toUpperCase()} 站点测试覆盖率失败: ${error.message}`);
      }
    }
    
    // 3. 收集覆盖率数据
    log('blue', '📋 收集覆盖率数据...');
    const coverageData = [];
    
    // 后端覆盖率
    const backendCoverage = readCoverageFile(path.join(__dirname, '../backend/coverage/coverage-summary.json'));
    if (backendCoverage) {
      coverageData.push(formatCoverage('Backend API', backendCoverage));
    }
    
    // 前端覆盖率
    frontendSites.forEach(site => {
      const frontendCoverage = readCoverageFile(path.join(__dirname, `../frontend/${site}/coverage/coverage-summary.json`));
      if (frontendCoverage) {
        coverageData.push(formatCoverage(`Frontend ${site.toUpperCase()}`, frontendCoverage));
      }
    });
    
    // 4. 生成报告
    log('blue', '📄 生成聚合报告...');
    
    // 创建coverage目录
    const coverageDir = path.join(__dirname, '../coverage');
    if (!fs.existsSync(coverageDir)) {
      fs.mkdirSync(coverageDir, { recursive: true });
    }
    
    // 生成HTML报告
    const htmlReport = generateHtmlReport(coverageData);
    fs.writeFileSync(path.join(coverageDir, 'index.html'), htmlReport);
    
    // 生成JSON报告
    fs.writeFileSync(
      path.join(coverageDir, 'coverage-summary.json'), 
      JSON.stringify(coverageData, null, 2)
    );
    
    // 5. 输出结果摘要
    log('green', '✅ 覆盖率报告生成完成!');
    console.log();
    log('bright', '📊 覆盖率摘要:');
    console.log();
    
    coverageData.forEach(item => {
      const avg = Math.round((item.lines + item.branches + item.functions + item.statements) / 4);
      const statusColor = avg >= 80 ? 'green' : avg >= 60 ? 'yellow' : 'red';
      const statusIcon = avg >= 80 ? '✅' : avg >= 60 ? '⚠️' : '❌';
      
      log('blue', `${statusIcon} ${item.name}: ${avg}% (语句: ${item.statements}%, 分支: ${item.branches}%, 函数: ${item.functions}%, 行: ${item.lines}%)`);
    });
    
    console.log();
    log('green', `📄 HTML报告: file://${path.join(coverageDir, 'index.html')}`);
    log('green', `📄 JSON报告: ${path.join(coverageDir, 'coverage-summary.json')}`);
    
  } catch (error) {
    log('red', `❌ 覆盖率报告生成失败: ${error.message}`);
    process.exit(1);
  }
}

// 辅助函数：获取覆盖率颜色
function getCoverageColor(percentage) {
  if (percentage >= 80) return '#28a745';
  if (percentage >= 60) return '#ffc107';
  return '#dc3545';
}

// 如果直接运行脚本
if (require.main === module) {
  main().catch(error => {
    log('red', `❌ 脚本执行失败: ${error.message}`);
    process.exit(1);
  });
}

module.exports = { main, formatCoverage, generateHtmlReport };