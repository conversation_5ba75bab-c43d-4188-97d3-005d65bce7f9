#!/usr/bin/env node

/**
 * MySQL 连接测试脚本
 * MySQL connection test script
 */

const mysql = require('mysql2');

// 数据库配置
const config = {
  host: '************',
  user: 'bengtai',
  password: 'weizhen258',
  database: 'bengtai',
  charset: 'utf8mb4',
  connectTimeout: 20000,
  timezone: '+00:00',
};

console.log('测试 MySQL 远程数据库连接...');
console.log('Testing MySQL remote database connection...');
console.log(`Host: ${config.host}`);
console.log(`Database: ${config.database}`);
console.log('');

// 创建连接
const connection = mysql.createConnection(config);

// 测试连接
connection.connect((err) => {
  if (err) {
    console.error('❌ 连接失败 / Connection failed:');
    console.error(err.message);
    process.exit(1);
  }

  console.log('✅ 成功连接到 MySQL 数据库！');
  console.log('✅ Successfully connected to MySQL database!');
  console.log('');

  // 获取数据库版本
  connection.query('SELECT VERSION() as version', (err, results) => {
    if (err) {
      console.error('查询版本失败:', err.message);
    } else {
      console.log(`MySQL 版本 / MySQL Version: ${results[0].version}`);
    }

    // 测试字符集
    connection.query('SHOW VARIABLES LIKE "%character%"', (err, results) => {
      if (!err) {
        console.log('\n字符集配置 / Character Set Configuration:');
        results.forEach((row) => {
          if (
            row.Variable_name.includes('character_set') ||
            row.Variable_name.includes('collation')
          ) {
            console.log(`  ${row.Variable_name}: ${row.Value}`);
          }
        });
      }

      // 关闭连接
      connection.end((err) => {
        if (err) {
          console.error('\n关闭连接时出错:', err.message);
        } else {
          console.log('\n✅ 连接已正常关闭');
          console.log('✅ Connection closed successfully');
        }
      });
    });
  });
});

// 连接错误处理
connection.on('error', (err) => {
  console.error('连接错误:', err);
});
