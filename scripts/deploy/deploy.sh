#!/bin/bash

# ====================================================================
# 宠物博客站群系统 - 自动部署脚本
# ====================================================================
# 功能: 自动部署后端服务和前端站点
# 用途: 被GitHub Actions调用或手动执行
# 作者: PetCare Team
# ====================================================================

set -euo pipefail  # 严格模式：错误时退出，未定义变量报错，管道失败报错

# ====================================================================
# 配置变量
# ====================================================================

# 基础路径配置
readonly DEPLOY_PATH="${DEPLOY_PATH:-/www/wwwroot/petcare}"
readonly BACKUP_PATH="${BACKUP_PATH:-/www/backups}"
readonly LOG_PATH="${LOG_PATH:-/www/wwwlogs}"
readonly SCRIPT_PATH="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"

# 部署配置
readonly APP_NAME="${APP_NAME:-petcare}"
readonly PM2_APP_NAME="${PM2_APP_NAME:-petcare-api}"
readonly GIT_REPO="${GIT_REPO:-https://github.com/yourorg/petcare-blog.git}"
readonly GIT_BRANCH="${GIT_BRANCH:-main}"

# 时间戳
readonly TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
readonly LOG_FILE="${LOG_PATH}/deploy-${TIMESTAMP}.log"
readonly BACKUP_DIR="${BACKUP_PATH}/${APP_NAME}-${TIMESTAMP}"

# 语言列表
readonly LANGUAGES=("en" "de" "ru")

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly NC='\033[0m' # No Color

# ====================================================================
# 工具函数
# ====================================================================

# 日志函数
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")  echo -e "${GREEN}[INFO]${NC}  ${timestamp} - $message" | tee -a "$LOG_FILE" ;;
        "WARN")  echo -e "${YELLOW}[WARN]${NC}  ${timestamp} - $message" | tee -a "$LOG_FILE" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} ${timestamp} - $message" | tee -a "$LOG_FILE" ;;
        "DEBUG") echo -e "${BLUE}[DEBUG]${NC} ${timestamp} - $message" | tee -a "$LOG_FILE" ;;
        *)       echo -e "${timestamp} - $message" | tee -a "$LOG_FILE" ;;
    esac
}

# 错误处理函数
error_exit() {
    log "ERROR" "$1"
    log "INFO" "部署失败，查看日志: $LOG_FILE"
    exit 1
}

# 清理函数
cleanup() {
    log "INFO" "执行清理操作..."
    # 清理临时文件
    rm -f /tmp/petcare-deploy-*
    log "INFO" "清理完成"
}

# 注册清理函数
trap cleanup EXIT
trap 'error_exit "部署被中断"' INT TERM

# 检查先决条件
check_prerequisites() {
    log "INFO" "检查部署先决条件..."
    
    # 检查必要的命令
    local required_commands=("git" "node" "npm" "pm2" "nginx" "redis-cli")
    for cmd in "${required_commands[@]}"; do
        if ! command -v "$cmd" &> /dev/null; then
            error_exit "缺少必要命令: $cmd"
        fi
    done
    
    # 检查Node.js版本
    local node_version=$(node --version | sed 's/v//')
    local required_version="20.0.0"
    if ! [[ "$(printf '%s\n' "$required_version" "$node_version" | sort -V | head -n1)" = "$required_version" ]]; then
        error_exit "Node.js版本不符合要求，需要 >= $required_version，当前版本: $node_version"
    fi
    
    # 检查磁盘空间
    local available_space=$(df "$DEPLOY_PATH" | tail -1 | awk '{print $4}')
    local required_space=1048576  # 1GB in KB
    if [[ $available_space -lt $required_space ]]; then
        error_exit "磁盘空间不足，需要至少1GB可用空间"
    fi
    
    # 检查目录权限
    if [[ ! -w "$DEPLOY_PATH" ]]; then
        error_exit "没有部署目录的写权限: $DEPLOY_PATH"
    fi
    
    log "INFO" "先决条件检查通过"
}

# 创建备份
create_backup() {
    log "INFO" "创建当前版本备份..."
    
    # 创建备份目录
    mkdir -p "$BACKUP_PATH"
    
    # 如果部署目录存在，创建备份
    if [[ -d "$DEPLOY_PATH" ]]; then
        log "INFO" "备份当前版本到: $BACKUP_DIR"
        cp -r "$DEPLOY_PATH" "$BACKUP_DIR"
        
        # 备份PM2进程信息
        pm2 jlist > "$BACKUP_DIR/pm2-processes.json" || log "WARN" "无法备份PM2进程信息"
        
        log "INFO" "备份完成"
    else
        log "INFO" "首次部署，无需备份"
    fi
    
    # 清理旧备份（保留最近5个）
    log "INFO" "清理旧备份..."
    cd "$BACKUP_PATH"
    ls -t | grep "^${APP_NAME}-" | tail -n +6 | xargs -r rm -rf
    log "INFO" "旧备份清理完成"
}

# 停止现有服务
stop_services() {
    log "INFO" "停止现有服务..."
    
    # 停止PM2应用
    if pm2 list | grep -q "$PM2_APP_NAME"; then
        log "INFO" "停止PM2应用: $PM2_APP_NAME"
        pm2 stop "$PM2_APP_NAME" || log "WARN" "停止PM2应用失败"
        pm2 delete "$PM2_APP_NAME" || log "WARN" "删除PM2应用失败"
    else
        log "INFO" "PM2应用不存在，跳过停止"
    fi
    
    log "INFO" "服务停止完成"
}

# 准备部署目录
prepare_deploy_directory() {
    log "INFO" "准备部署目录..."
    
    # 创建部署目录结构
    mkdir -p "$DEPLOY_PATH"/{backend,frontend,logs,temp}
    
    # 为每个语言创建前端目录
    for lang in "${LANGUAGES[@]}"; do
        mkdir -p "$DEPLOY_PATH/frontend/$lang"
    done
    
    # 设置正确的权限
    chown -R deploy:deploy "$DEPLOY_PATH"
    chmod -R 755 "$DEPLOY_PATH"
    
    log "INFO" "部署目录准备完成"
}

# 部署后端
deploy_backend() {
    log "INFO" "开始部署后端服务..."
    
    cd "$DEPLOY_PATH"
    
    # 克隆或更新代码
    if [[ -d .git ]]; then
        log "INFO" "更新现有代码库..."
        git fetch origin
        git reset --hard "origin/$GIT_BRANCH"
        git clean -fd
    else
        log "INFO" "克隆代码库..."
        git clone "$GIT_REPO" .
        git checkout "$GIT_BRANCH"
    fi
    
    # 进入后端目录
    cd "$DEPLOY_PATH/backend"
    
    # 安装依赖
    log "INFO" "安装后端依赖..."
    npm ci --production
    
    # 构建应用
    log "INFO" "构建后端应用..."
    npm run build
    
    # 验证构建结果
    if [[ ! -f "dist/index.js" ]]; then
        error_exit "后端构建失败，找不到入口文件"
    fi
    
    # 创建或更新PM2配置文件
    log "INFO" "配置PM2..."
    cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: '$PM2_APP_NAME',
    script: './dist/index.js',
    instances: 2,
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '$DEPLOY_PATH/logs/pm2-error.log',
    out_file: '$DEPLOY_PATH/logs/pm2-out.log',
    log_date_format: 'YYYY-MM-DD HH:mm:ss',
    merge_logs: true,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
EOF
    
    log "INFO" "后端部署完成"
}

# 部署前端
deploy_frontend() {
    log "INFO" "开始部署前端站点..."
    
    for lang in "${LANGUAGES[@]}"; do
        log "INFO" "部署前端站点: $lang"
        
        local frontend_dir="$DEPLOY_PATH/frontend/$lang"
        cd "$frontend_dir"
        
        # 克隆或更新前端代码
        if [[ -d .git ]]; then
            log "INFO" "更新前端代码 ($lang)..."
            git fetch origin
            git reset --hard "origin/$GIT_BRANCH"
            git clean -fd
        else
            log "INFO" "克隆前端代码 ($lang)..."
            git clone "$GIT_REPO" .
            git checkout "$GIT_BRANCH"
        fi
        
        # 进入前端目录
        cd "$frontend_dir/frontend/$lang"
        
        # 安装依赖
        log "INFO" "安装前端依赖 ($lang)..."
        npm ci
        
        # 构建前端
        log "INFO" "构建前端应用 ($lang)..."
        npm run "build:$lang"
        
        # 验证构建结果
        if [[ ! -d "dist" ]]; then
            error_exit "前端构建失败 ($lang)，找不到dist目录"
        fi
        
        # 移动构建文件到部署目录
        log "INFO" "部署前端文件 ($lang)..."
        cp -r dist/* "$frontend_dir/"
        
        log "INFO" "前端部署完成 ($lang)"
    done
    
    log "INFO" "所有前端站点部署完成"
}

# 清理缓存
clear_cache() {
    log "INFO" "清理应用缓存..."
    
    # 清理Redis缓存
    if command -v redis-cli &> /dev/null; then
        log "INFO" "清理Redis缓存..."
        if [[ -n "${REDIS_PASSWORD:-}" ]]; then
            redis-cli -a "$REDIS_PASSWORD" FLUSHDB || log "WARN" "清理Redis缓存失败"
        else
            redis-cli FLUSHDB || log "WARN" "清理Redis缓存失败"
        fi
    fi
    
    # 清理NPM缓存
    log "INFO" "清理NPM缓存..."
    npm cache clean --force || log "WARN" "清理NPM缓存失败"
    
    log "INFO" "缓存清理完成"
}

# 启动服务
start_services() {
    log "INFO" "启动应用服务..."
    
    # 启动PM2应用
    cd "$DEPLOY_PATH/backend"
    log "INFO" "启动PM2应用..."
    pm2 start ecosystem.config.js
    
    # 保存PM2配置
    pm2 save
    
    # 重载Nginx配置
    log "INFO" "重载Nginx配置..."
    if nginx -t; then
        nginx -s reload
        log "INFO" "Nginx配置重载成功"
    else
        error_exit "Nginx配置验证失败"
    fi
    
    log "INFO" "服务启动完成"
}

# 健康检查
health_check() {
    log "INFO" "执行健康检查..."
    
    local max_attempts=30
    local attempt=1
    local backend_url="http://localhost:3000/api/v1/health"
    
    # 等待服务启动
    log "INFO" "等待后端服务启动..."
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s "$backend_url" > /dev/null 2>&1; then
            log "INFO" "后端服务健康检查通过"
            break
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            error_exit "后端服务健康检查失败，超过最大尝试次数"
        fi
        
        log "INFO" "健康检查尝试 $attempt/$max_attempts，等待5秒..."
        sleep 5
        ((attempt++))
    done
    
    # 检查前端站点
    log "INFO" "检查前端站点..."
    local frontend_urls=(
        "http://localhost/en"
        "http://localhost/de" 
        "http://localhost/ru"
    )
    
    for url in "${frontend_urls[@]}"; do
        if curl -f -s "$url" > /dev/null 2>&1; then
            log "INFO" "前端站点检查通过: $url"
        else
            log "WARN" "前端站点检查失败: $url"
        fi
    done
    
    log "INFO" "健康检查完成"
}

# 部署状态报告
deployment_report() {
    log "INFO" "生成部署报告..."
    
    cat > "$LOG_PATH/deployment-report-$TIMESTAMP.txt" << EOF
====================================================================
宠物博客站群系统 - 部署报告
====================================================================
部署时间: $(date)
Git提交: $(cd "$DEPLOY_PATH" && git rev-parse HEAD)
Git分支: $GIT_BRANCH
部署路径: $DEPLOY_PATH
备份路径: $BACKUP_DIR
日志文件: $LOG_FILE

====================================================================
服务状态
====================================================================
PM2进程状态:
$(pm2 list)

Nginx状态:
$(nginx -t 2>&1)

磁盘使用情况:
$(df -h "$DEPLOY_PATH")

====================================================================
应用信息
====================================================================
Node.js版本: $(node --version)
NPM版本: $(npm --version)
PM2版本: $(pm2 --version)

====================================================================
部署结果
====================================================================
✅ 部署成功完成
📝 详细日志: $LOG_FILE
🔄 可使用以下命令回滚: 
   $SCRIPT_PATH/rollback.sh $BACKUP_DIR

====================================================================
EOF
    
    log "INFO" "部署报告已生成: $LOG_PATH/deployment-report-$TIMESTAMP.txt"
}

# ====================================================================
# 主函数
# ====================================================================

main() {
    log "INFO" "======================================================================"
    log "INFO" "开始部署宠物博客站群系统"
    log "INFO" "======================================================================"
    log "INFO" "部署时间: $(date)"
    log "INFO" "部署路径: $DEPLOY_PATH"
    log "INFO" "Git分支: $GIT_BRANCH"
    log "INFO" "日志文件: $LOG_FILE"
    log "INFO" "======================================================================"
    
    # 创建日志目录
    mkdir -p "$LOG_PATH"
    
    # 执行部署步骤
    check_prerequisites
    create_backup
    stop_services
    prepare_deploy_directory
    deploy_backend
    deploy_frontend
    clear_cache
    start_services
    health_check
    deployment_report
    
    log "INFO" "======================================================================"
    log "INFO" "部署成功完成！"
    log "INFO" "======================================================================"
    log "INFO" "📝 详细日志: $LOG_FILE"
    log "INFO" "📊 部署报告: $LOG_PATH/deployment-report-$TIMESTAMP.txt"
    log "INFO" "🔄 回滚命令: $SCRIPT_PATH/rollback.sh $BACKUP_DIR"
    log "INFO" "======================================================================"
}

# ====================================================================
# 脚本入口
# ====================================================================

# 检查是否以root权限运行
if [[ $EUID -ne 0 ]] && [[ $(id -u) -ne 0 ]]; then
    log "WARN" "建议以管理员权限运行此脚本"
fi

# 显示帮助信息
if [[ "${1:-}" == "--help" ]] || [[ "${1:-}" == "-h" ]]; then
    cat << EOF
宠物博客站群系统 - 自动部署脚本

用法:
  $0 [选项]

选项:
  -h, --help     显示此帮助信息
  --dry-run      模拟运行（不执行实际部署）

环境变量:
  DEPLOY_PATH    部署路径 (默认: /www/wwwroot/petcare)
  BACKUP_PATH    备份路径 (默认: /www/backups)
  LOG_PATH       日志路径 (默认: /www/wwwlogs)
  GIT_REPO       Git仓库地址
  GIT_BRANCH     Git分支 (默认: main)
  REDIS_PASSWORD Redis密码

示例:
  # 标准部署
  $0
  
  # 自定义部署路径
  DEPLOY_PATH=/custom/path $0
  
  # 部署指定分支
  GIT_BRANCH=develop $0

EOF
    exit 0
fi

# 模拟运行模式
if [[ "${1:-}" == "--dry-run" ]]; then
    log "INFO" "模拟运行模式 - 不会执行实际部署操作"
    log "INFO" "将要执行的操作:"
    log "INFO" "1. 检查先决条件"
    log "INFO" "2. 创建备份: $BACKUP_DIR"
    log "INFO" "3. 停止服务: $PM2_APP_NAME"
    log "INFO" "4. 部署后端到: $DEPLOY_PATH/backend"
    log "INFO" "5. 部署前端到: $DEPLOY_PATH/frontend/{en,de,ru}"
    log "INFO" "6. 清理缓存"
    log "INFO" "7. 启动服务"
    log "INFO" "8. 健康检查"
    log "INFO" "模拟运行完成"
    exit 0
fi

# 执行主函数
main "$@"