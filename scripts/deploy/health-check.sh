#!/bin/bash

# ====================================================================
# 宠物博客站群系统 - 健康检查脚本
# ====================================================================
# 功能: 检查应用服务的健康状态
# 用途: CI/CD流程和运维监控
# 作者: PetCare Team
# ====================================================================

set -euo pipefail

# ====================================================================
# 配置变量
# ====================================================================

readonly SCRIPT_NAME=$(basename "$0")
readonly TIMESTAMP=$(date +"%Y-%m-%d %H:%M:%S")

# 服务配置
readonly BACKEND_HOST="${BACKEND_HOST:-localhost}"
readonly BACKEND_PORT="${BACKEND_PORT:-3000}"
readonly HEALTH_ENDPOINT="${HEALTH_ENDPOINT:-/api/v1/health}"

# 前端站点配置
readonly FRONTEND_DOMAINS=(
    "${EN_DOMAIN:-www.petcare.com}"
    "${DE_DOMAIN:-www.haustiere.de}" 
    "${RU_DOMAIN:-www.xn----7sbabaabeha4afwqgqf8d.xn--p1ai}"
)

# 检查配置
readonly MAX_RESPONSE_TIME="${MAX_RESPONSE_TIME:-5}"  # 最大响应时间(秒)
readonly RETRY_COUNT="${RETRY_COUNT:-3}"              # 重试次数
readonly RETRY_DELAY="${RETRY_DELAY:-5}"              # 重试间隔(秒)

# 状态码
readonly EXIT_SUCCESS=0
readonly EXIT_WARNING=1
readonly EXIT_CRITICAL=2
readonly EXIT_UNKNOWN=3

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly NC='\033[0m'

# ====================================================================
# 工具函数
# ====================================================================

# 日志函数
log() {
    local level=$1
    shift
    local message="$*"
    
    case $level in
        "SUCCESS") echo -e "${GREEN}✅ [SUCCESS]${NC} $message" ;;
        "WARNING") echo -e "${YELLOW}⚠️  [WARNING]${NC} $message" ;;
        "ERROR")   echo -e "${RED}❌ [ERROR]${NC} $message" ;;
        "INFO")    echo -e "${BLUE}ℹ️  [INFO]${NC} $message" ;;
        "DEBUG")   echo -e "${PURPLE}🔍 [DEBUG]${NC} $message" ;;
        *)         echo -e "$message" ;;
    esac
}

# 获取HTTP响应时间
get_response_time() {
    local url="$1"
    curl -o /dev/null -s -w "%{time_total}" "$url" 2>/dev/null || echo "0"
}

# 获取HTTP状态码
get_http_status() {
    local url="$1"
    curl -o /dev/null -s -w "%{http_code}" "$url" 2>/dev/null || echo "000"
}

# 检查URL可达性
check_url_with_retry() {
    local url="$1"
    local name="$2"
    local max_time="$3"
    
    local attempt=1
    
    while [[ $attempt -le $RETRY_COUNT ]]; do
        log "DEBUG" "检查 $name - 尝试 $attempt/$RETRY_COUNT"
        
        local start_time=$(date +%s.%N)
        local status_code=$(get_http_status "$url")
        local end_time=$(date +%s.%N)
        local response_time=$(echo "$end_time - $start_time" | bc -l)
        
        if [[ "$status_code" =~ ^[2][0-9][0-9]$ ]]; then
            if (( $(echo "$response_time <= $max_time" | bc -l) )); then
                log "SUCCESS" "$name 健康 - 状态码: $status_code, 响应时间: ${response_time}s"
                return $EXIT_SUCCESS
            else
                log "WARNING" "$name 响应慢 - 状态码: $status_code, 响应时间: ${response_time}s (超过 ${max_time}s)"
                return $EXIT_WARNING
            fi
        fi
        
        log "WARNING" "$name 检查失败 - 状态码: $status_code, 响应时间: ${response_time}s"
        
        if [[ $attempt -lt $RETRY_COUNT ]]; then
            log "INFO" "等待 ${RETRY_DELAY}s 后重试..."
            sleep "$RETRY_DELAY"
        fi
        
        ((attempt++))
    done
    
    log "ERROR" "$name 检查失败 - 已达最大重试次数"
    return $EXIT_CRITICAL
}

# ====================================================================
# 健康检查函数
# ====================================================================

# 检查后端API健康状态
check_backend_health() {
    log "INFO" "开始检查后端API健康状态..."
    
    local backend_url="http://${BACKEND_HOST}:${BACKEND_PORT}${HEALTH_ENDPOINT}"
    local result=$EXIT_SUCCESS
    
    # 检查健康端点
    if ! check_url_with_retry "$backend_url" "后端健康端点" "$MAX_RESPONSE_TIME"; then
        result=$EXIT_CRITICAL
    fi
    
    # 检查API根路径
    local api_root="http://${BACKEND_HOST}:${BACKEND_PORT}/api/v1"
    if ! check_url_with_retry "$api_root" "API根路径" "$MAX_RESPONSE_TIME"; then
        if [[ $result -eq $EXIT_SUCCESS ]]; then
            result=$EXIT_WARNING
        fi
    fi
    
    return $result
}

# 检查前端站点健康状态
check_frontend_health() {
    log "INFO" "开始检查前端站点健康状态..."
    
    local result=$EXIT_SUCCESS
    
    for domain in "${FRONTEND_DOMAINS[@]}"; do
        local https_url="https://$domain"
        local http_url="http://$domain"
        
        log "INFO" "检查站点: $domain"
        
        # 优先检查HTTPS
        if check_url_with_retry "$https_url" "$domain (HTTPS)" "$MAX_RESPONSE_TIME"; then
            continue
        fi
        
        # 如果HTTPS失败，检查HTTP
        if check_url_with_retry "$http_url" "$domain (HTTP)" "$MAX_RESPONSE_TIME"; then
            log "WARNING" "$domain 仅HTTP可用，HTTPS不可用"
            if [[ $result -eq $EXIT_SUCCESS ]]; then
                result=$EXIT_WARNING
            fi
        else
            log "ERROR" "$domain 完全不可用"
            result=$EXIT_CRITICAL
        fi
    done
    
    return $result
}

# 检查数据库连接
check_database_health() {
    log "INFO" "开始检查数据库连接健康状态..."
    
    # 通过API端点检查数据库连接
    local db_check_url="http://${BACKEND_HOST}:${BACKEND_PORT}/api/v1/health/db"
    
    if check_url_with_retry "$db_check_url" "数据库连接" "$MAX_RESPONSE_TIME"; then
        return $EXIT_SUCCESS
    else
        log "WARNING" "无法通过API检查数据库健康状态"
        return $EXIT_WARNING
    fi
}

# 检查Redis缓存
check_redis_health() {
    log "INFO" "开始检查Redis缓存健康状态..."
    
    if command -v redis-cli &> /dev/null; then
        local redis_host="${REDIS_HOST:-localhost}"
        local redis_port="${REDIS_PORT:-6379}"
        
        if [[ -n "${REDIS_PASSWORD:-}" ]]; then
            if redis-cli -h "$redis_host" -p "$redis_port" -a "$REDIS_PASSWORD" ping | grep -q "PONG"; then
                log "SUCCESS" "Redis连接正常"
                return $EXIT_SUCCESS
            fi
        else
            if redis-cli -h "$redis_host" -p "$redis_port" ping | grep -q "PONG"; then
                log "SUCCESS" "Redis连接正常"
                return $EXIT_SUCCESS
            fi
        fi
        
        log "ERROR" "Redis连接失败"
        return $EXIT_CRITICAL
    else
        # 通过API端点检查Redis
        local redis_check_url="http://${BACKEND_HOST}:${BACKEND_PORT}/api/v1/health/redis"
        
        if check_url_with_retry "$redis_check_url" "Redis缓存" "$MAX_RESPONSE_TIME"; then
            return $EXIT_SUCCESS
        else
            log "WARNING" "无法检查Redis健康状态"
            return $EXIT_WARNING
        fi
    fi
}

# 检查PM2进程状态
check_pm2_health() {
    log "INFO" "开始检查PM2进程健康状态..."
    
    if command -v pm2 &> /dev/null; then
        local pm2_status
        pm2_status=$(pm2 jlist 2>/dev/null)
        
        if echo "$pm2_status" | jq -e '.[] | select(.name=="petcare-api" and .pm2_env.status=="online")' > /dev/null 2>&1; then
            local memory_usage
            memory_usage=$(echo "$pm2_status" | jq -r '.[] | select(.name=="petcare-api") | .memory')
            local memory_mb=$((memory_usage / 1024 / 1024))
            
            log "SUCCESS" "PM2进程运行正常 - 内存使用: ${memory_mb}MB"
            
            # 检查内存使用是否过高
            if [[ $memory_mb -gt 1024 ]]; then
                log "WARNING" "PM2进程内存使用较高: ${memory_mb}MB"
                return $EXIT_WARNING
            fi
            
            return $EXIT_SUCCESS
        else
            log "ERROR" "PM2进程状态异常"
            return $EXIT_CRITICAL
        fi
    else
        log "WARNING" "PM2命令不可用，跳过进程检查"
        return $EXIT_WARNING
    fi
}

# 检查系统资源
check_system_resources() {
    log "INFO" "开始检查系统资源状态..."
    
    local result=$EXIT_SUCCESS
    
    # 检查磁盘使用率
    local disk_usage
    disk_usage=$(df / | tail -1 | awk '{print $5}' | sed 's/%//')
    
    if [[ $disk_usage -gt 90 ]]; then
        log "ERROR" "磁盘使用率过高: ${disk_usage}%"
        result=$EXIT_CRITICAL
    elif [[ $disk_usage -gt 80 ]]; then
        log "WARNING" "磁盘使用率较高: ${disk_usage}%"
        if [[ $result -eq $EXIT_SUCCESS ]]; then
            result=$EXIT_WARNING
        fi
    else
        log "SUCCESS" "磁盘使用率正常: ${disk_usage}%"
    fi
    
    # 检查内存使用率
    if command -v free &> /dev/null; then
        local memory_usage
        memory_usage=$(free | grep '^Mem:' | awk '{printf "%.0f", ($3/$2) * 100}')
        
        if [[ $memory_usage -gt 90 ]]; then
            log "ERROR" "内存使用率过高: ${memory_usage}%"
            result=$EXIT_CRITICAL
        elif [[ $memory_usage -gt 80 ]]; then
            log "WARNING" "内存使用率较高: ${memory_usage}%"
            if [[ $result -eq $EXIT_SUCCESS ]]; then
                result=$EXIT_WARNING
            fi
        else
            log "SUCCESS" "内存使用率正常: ${memory_usage}%"
        fi
    fi
    
    # 检查负载平均值
    if command -v uptime &> /dev/null; then
        local load_avg
        load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
        local cpu_cores
        cpu_cores=$(nproc)
        
        if (( $(echo "$load_avg > $cpu_cores * 2" | bc -l) )); then
            log "ERROR" "系统负载过高: $load_avg (CPU核心数: $cpu_cores)"
            result=$EXIT_CRITICAL
        elif (( $(echo "$load_avg > $cpu_cores" | bc -l) )); then
            log "WARNING" "系统负载较高: $load_avg (CPU核心数: $cpu_cores)"
            if [[ $result -eq $EXIT_SUCCESS ]]; then
                result=$EXIT_WARNING
            fi
        else
            log "SUCCESS" "系统负载正常: $load_avg (CPU核心数: $cpu_cores)"
        fi
    fi
    
    return $result
}

# ====================================================================
# 主函数
# ====================================================================

# 执行全面健康检查
run_full_health_check() {
    log "INFO" "======================================================================"
    log "INFO" "宠物博客站群系统 - 全面健康检查"
    log "INFO" "======================================================================"
    log "INFO" "检查时间: $TIMESTAMP"
    log "INFO" "检查配置:"
    log "INFO" "  后端地址: ${BACKEND_HOST}:${BACKEND_PORT}"
    log "INFO" "  最大响应时间: ${MAX_RESPONSE_TIME}s"
    log "INFO" "  重试次数: $RETRY_COUNT"
    log "INFO" "  重试间隔: ${RETRY_DELAY}s"
    log "INFO" "======================================================================"
    
    local overall_status=$EXIT_SUCCESS
    
    # 执行各项健康检查
    local checks=(
        "check_backend_health:后端API"
        "check_frontend_health:前端站点"
        "check_database_health:数据库"
        "check_redis_health:Redis缓存"
        "check_pm2_health:PM2进程"
        "check_system_resources:系统资源"
    )
    
    for check in "${checks[@]}"; do
        local func_name="${check%%:*}"
        local check_name="${check##*:}"
        
        log "INFO" "======================================================================"
        
        if $func_name; then
            local status=$?
            if [[ $status -gt $overall_status ]]; then
                overall_status=$status
            fi
        else
            log "ERROR" "$check_name 检查执行失败"
            overall_status=$EXIT_CRITICAL
        fi
    done
    
    log "INFO" "======================================================================"
    
    # 生成检查报告
    case $overall_status in
        $EXIT_SUCCESS)
            log "SUCCESS" "🎉 所有健康检查通过！系统运行正常"
            ;;
        $EXIT_WARNING)
            log "WARNING" "⚠️ 健康检查发现警告，系统基本正常但需要关注"
            ;;
        $EXIT_CRITICAL)
            log "ERROR" "🚨 健康检查发现严重问题，系统可能无法正常服务"
            ;;
        *)
            log "ERROR" "❓ 健康检查状态未知"
            ;;
    esac
    
    log "INFO" "======================================================================"
    
    return $overall_status
}

# 显示帮助信息
show_help() {
    cat << EOF
宠物博客站群系统 - 健康检查脚本

用法:
  $SCRIPT_NAME [选项] [检查类型]

选项:
  -h, --help          显示此帮助信息
  -q, --quiet         静默模式，只显示错误
  -v, --verbose       详细模式，显示调试信息
  --retry-count N     设置重试次数 (默认: $RETRY_COUNT)
  --retry-delay N     设置重试间隔秒数 (默认: $RETRY_DELAY)
  --max-time N        设置最大响应时间 (默认: $MAX_RESPONSE_TIME)

检查类型:
  all                 执行全面健康检查 (默认)
  backend             仅检查后端API
  frontend            仅检查前端站点
  database            仅检查数据库连接
  redis               仅检查Redis缓存
  pm2                 仅检查PM2进程
  system              仅检查系统资源

环境变量:
  BACKEND_HOST        后端主机地址 (默认: localhost)
  BACKEND_PORT        后端端口 (默认: 3000)
  EN_DOMAIN           英文站点域名
  DE_DOMAIN           德文站点域名
  RU_DOMAIN           俄文站点域名
  REDIS_HOST          Redis主机地址
  REDIS_PORT          Redis端口
  REDIS_PASSWORD      Redis密码

返回码:
  0                   所有检查通过
  1                   检查通过但有警告
  2                   检查失败，有严重问题
  3                   检查状态未知

示例:
  # 执行全面健康检查
  $SCRIPT_NAME
  
  # 仅检查后端API
  $SCRIPT_NAME backend
  
  # 自定义配置检查
  BACKEND_HOST=************* $SCRIPT_NAME --retry-count 5
  
  # 详细模式检查
  $SCRIPT_NAME --verbose all

EOF
}

# ====================================================================
# 脚本入口
# ====================================================================

main() {
    local check_type="all"
    local quiet_mode=false
    local verbose_mode=false
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -q|--quiet)
                quiet_mode=true
                shift
                ;;
            -v|--verbose)
                verbose_mode=true
                shift
                ;;
            --retry-count)
                RETRY_COUNT="$2"
                shift 2
                ;;
            --retry-delay)
                RETRY_DELAY="$2"
                shift 2
                ;;
            --max-time)
                MAX_RESPONSE_TIME="$2"
                shift 2
                ;;
            all|backend|frontend|database|redis|pm2|system)
                check_type="$1"
                shift
                ;;
            *)
                log "ERROR" "未知参数: $1"
                show_help
                exit $EXIT_UNKNOWN
                ;;
        esac
    done
    
    # 根据检查类型执行相应函数
    case $check_type in
        all)
            run_full_health_check
            ;;
        backend)
            check_backend_health
            ;;
        frontend)
            check_frontend_health
            ;;
        database)
            check_database_health
            ;;
        redis)
            check_redis_health
            ;;
        pm2)
            check_pm2_health
            ;;
        system)
            check_system_resources
            ;;
        *)
            log "ERROR" "不支持的检查类型: $check_type"
            exit $EXIT_UNKNOWN
            ;;
    esac
}

# 执行主函数
main "$@"