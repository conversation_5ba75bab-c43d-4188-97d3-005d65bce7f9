#!/bin/bash

# ====================================================================
# 宠物博客站群系统 - 回滚脚本
# ====================================================================
# 功能: 回滚到指定的备份版本
# 用途: 部署失败时快速恢复服务
# 作者: PetCare Team
# ====================================================================

set -euo pipefail

# ====================================================================
# 配置变量
# ====================================================================

readonly SCRIPT_NAME=$(basename "$0")
readonly DEPLOY_PATH="${DEPLOY_PATH:-/www/wwwroot/petcare}"
readonly BACKUP_PATH="${BACKUP_PATH:-/www/backups}"
readonly LOG_PATH="${LOG_PATH:-/www/wwwlogs}"
readonly PM2_APP_NAME="${PM2_APP_NAME:-petcare-api}"

# 时间戳
readonly TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
readonly LOG_FILE="${LOG_PATH}/rollback-${TIMESTAMP}.log"

# 颜色定义
readonly RED='\033[0;31m'
readonly GREEN='\033[0;32m'
readonly YELLOW='\033[1;33m'
readonly BLUE='\033[0;34m'
readonly PURPLE='\033[0;35m'
readonly NC='\033[0m'

# ====================================================================
# 工具函数
# ====================================================================

# 日志函数
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        "INFO")  echo -e "${GREEN}[INFO]${NC}  ${timestamp} - $message" | tee -a "$LOG_FILE" ;;
        "WARN")  echo -e "${YELLOW}[WARN]${NC}  ${timestamp} - $message" | tee -a "$LOG_FILE" ;;
        "ERROR") echo -e "${RED}[ERROR]${NC} ${timestamp} - $message" | tee -a "$LOG_FILE" ;;
        "DEBUG") echo -e "${BLUE}[DEBUG]${NC} ${timestamp} - $message" | tee -a "$LOG_FILE" ;;
        *)       echo -e "${timestamp} - $message" | tee -a "$LOG_FILE" ;;
    esac
}

# 错误处理函数
error_exit() {
    log "ERROR" "$1"
    log "INFO" "回滚失败，查看日志: $LOG_FILE"
    exit 1
}

# 显示帮助信息
show_help() {
    cat << EOF
宠物博客站群系统 - 回滚脚本

用法:
  $SCRIPT_NAME [选项] [备份目录]

选项:
  -h, --help          显示此帮助信息
  -l, --list          列出可用的备份
  -f, --force         强制回滚，跳过确认
  --auto              自动选择最新备份
  --dry-run           模拟运行，不执行实际操作

参数:
  备份目录            要回滚到的备份目录路径

示例:
  # 列出可用备份
  $SCRIPT_NAME --list
  
  # 回滚到指定备份
  $SCRIPT_NAME /www/backups/petcare-20240101_120000
  
  # 自动回滚到最新备份
  $SCRIPT_NAME --auto
  
  # 强制回滚（跳过确认）
  $SCRIPT_NAME --force /www/backups/petcare-20240101_120000

环境变量:
  DEPLOY_PATH         当前部署路径 (默认: /www/wwwroot/petcare)
  BACKUP_PATH         备份存储路径 (默认: /www/backups)
  LOG_PATH            日志存储路径 (默认: /www/wwwlogs)
  PM2_APP_NAME        PM2应用名称 (默认: petcare-api)

EOF
}

# 列出可用备份
list_backups() {
    log "INFO" "列出可用的备份..."
    
    if [[ ! -d "$BACKUP_PATH" ]]; then
        log "ERROR" "备份目录不存在: $BACKUP_PATH"
        return 1
    fi
    
    local backups
    backups=$(find "$BACKUP_PATH" -maxdepth 1 -name "petcare-*" -type d | sort -r)
    
    if [[ -z "$backups" ]]; then
        log "INFO" "没有找到可用的备份"
        return 0
    fi
    
    echo
    echo "可用备份列表:"
    echo "=============================================================="
    printf "%-5s %-30s %-20s %-15s\n" "序号" "备份目录" "创建时间" "大小"
    echo "=============================================================="
    
    local index=1
    while IFS= read -r backup_dir; do
        if [[ -n "$backup_dir" ]]; then
            local backup_name=$(basename "$backup_dir")
            local backup_time=""
            local backup_size=""
            
            # 解析时间戳
            if [[ "$backup_name" =~ petcare-([0-9]{8}_[0-9]{6}) ]]; then
                local timestamp="${BASH_REMATCH[1]}"
                backup_time=$(date -d "${timestamp:0:8} ${timestamp:9:2}:${timestamp:11:2}:${timestamp:13:2}" "+%Y-%m-%d %H:%M:%S" 2>/dev/null || echo "未知")
            fi
            
            # 获取大小
            if [[ -d "$backup_dir" ]]; then
                backup_size=$(du -sh "$backup_dir" 2>/dev/null | cut -f1 || echo "未知")
            fi
            
            printf "%-5s %-30s %-20s %-15s\n" "$index" "$backup_name" "$backup_time" "$backup_size"
            ((index++))
        fi
    done <<< "$backups"
    
    echo "=============================================================="
    echo
}

# 验证备份完整性
validate_backup() {
    local backup_dir="$1"
    
    log "INFO" "验证备份完整性: $(basename "$backup_dir")"
    
    # 检查备份目录是否存在
    if [[ ! -d "$backup_dir" ]]; then
        error_exit "备份目录不存在: $backup_dir"
    fi
    
    # 检查关键目录和文件
    local required_paths=(
        "$backup_dir/backend"
        "$backup_dir/frontend"
    )
    
    for path in "${required_paths[@]}"; do
        if [[ ! -d "$path" ]]; then
            error_exit "备份不完整，缺少目录: $(basename "$path")"
        fi
    done
    
    # 检查后端关键文件
    if [[ ! -f "$backup_dir/backend/package.json" ]]; then
        error_exit "备份不完整，缺少后端package.json"
    fi
    
    # 检查前端目录
    local languages=("en" "de" "ru")
    for lang in "${languages[@]}"; do
        if [[ ! -d "$backup_dir/frontend/$lang" ]]; then
            log "WARN" "备份中缺少前端语言目录: $lang"
        fi
    done
    
    # 检查备份大小（至少应该有一些内容）
    local backup_size
    backup_size=$(du -s "$backup_dir" 2>/dev/null | cut -f1 || echo "0")
    
    if [[ $backup_size -lt 1024 ]]; then  # 小于1MB
        error_exit "备份文件过小，可能不完整"
    fi
    
    log "INFO" "备份验证通过"
}

# 创建当前版本快照
create_snapshot() {
    log "INFO" "创建当前版本快照..."
    
    local snapshot_dir="${BACKUP_PATH}/pre-rollback-${TIMESTAMP}"
    
    if [[ -d "$DEPLOY_PATH" ]]; then
        mkdir -p "$BACKUP_PATH"
        cp -r "$DEPLOY_PATH" "$snapshot_dir"
        
        # 备份PM2进程信息
        if command -v pm2 &> /dev/null; then
            pm2 jlist > "$snapshot_dir/pm2-processes.json" 2>/dev/null || log "WARN" "无法备份PM2进程信息"
        fi
        
        log "INFO" "当前版本快照已保存到: $snapshot_dir"
    else
        log "WARN" "当前部署目录不存在，跳过快照创建"
    fi
}

# 停止服务
stop_services() {
    log "INFO" "停止应用服务..."
    
    # 停止PM2应用
    if command -v pm2 &> /dev/null; then
        if pm2 list | grep -q "$PM2_APP_NAME"; then
            log "INFO" "停止PM2应用: $PM2_APP_NAME"
            pm2 stop "$PM2_APP_NAME" || log "WARN" "停止PM2应用失败"
            pm2 delete "$PM2_APP_NAME" || log "WARN" "删除PM2应用失败"
        else
            log "INFO" "PM2应用不存在: $PM2_APP_NAME"
        fi
    else
        log "WARN" "PM2命令不可用"
    fi
}

# 执行回滚
perform_rollback() {
    local backup_dir="$1"
    
    log "INFO" "开始执行回滚操作..."
    log "INFO" "源备份: $backup_dir"
    log "INFO" "目标路径: $DEPLOY_PATH"
    
    # 移除当前部署
    if [[ -d "$DEPLOY_PATH" ]]; then
        log "INFO" "移除当前部署..."
        rm -rf "$DEPLOY_PATH"
    fi
    
    # 恢复备份
    log "INFO" "恢复备份文件..."
    mkdir -p "$(dirname "$DEPLOY_PATH")"
    cp -r "$backup_dir" "$DEPLOY_PATH"
    
    # 设置正确的权限
    log "INFO" "设置文件权限..."
    chown -R deploy:deploy "$DEPLOY_PATH" || log "WARN" "设置文件所有者失败"
    chmod -R 755 "$DEPLOY_PATH" || log "WARN" "设置文件权限失败"
    
    # 恢复PM2进程
    if [[ -f "$backup_dir/pm2-processes.json" ]]; then
        log "INFO" "恢复PM2进程配置..."
        cd "$DEPLOY_PATH/backend"
        
        # 如果有ecosystem配置文件，直接使用
        if [[ -f "ecosystem.config.js" ]]; then
            pm2 start ecosystem.config.js
        else
            # 尝试从备份的进程信息中恢复
            log "WARN" "未找到ecosystem配置，尝试手动启动..."
            pm2 start dist/index.js --name "$PM2_APP_NAME"
        fi
    else
        log "INFO" "手动启动PM2应用..."
        cd "$DEPLOY_PATH/backend"
        
        if [[ -f "ecosystem.config.js" ]]; then
            pm2 start ecosystem.config.js
        elif [[ -f "dist/index.js" ]]; then
            pm2 start dist/index.js --name "$PM2_APP_NAME"
        elif [[ -f "package.json" ]]; then
            npm start &
        else
            error_exit "无法找到启动文件"
        fi
    fi
    
    # 保存PM2配置
    if command -v pm2 &> /dev/null; then
        pm2 save
    fi
    
    log "INFO" "回滚操作完成"
}

# 重载配置
reload_configs() {
    log "INFO" "重载服务配置..."
    
    # 重载Nginx配置
    if command -v nginx &> /dev/null; then
        log "INFO" "测试Nginx配置..."
        if nginx -t; then
            log "INFO" "重载Nginx配置..."
            nginx -s reload
            log "INFO" "Nginx配置重载完成"
        else
            log "ERROR" "Nginx配置测试失败"
            return 1
        fi
    else
        log "WARN" "Nginx命令不可用"
    fi
    
    # 清理缓存
    log "INFO" "清理应用缓存..."
    if command -v redis-cli &> /dev/null; then
        if [[ -n "${REDIS_PASSWORD:-}" ]]; then
            redis-cli -a "$REDIS_PASSWORD" FLUSHDB || log "WARN" "清理Redis缓存失败"
        else
            redis-cli FLUSHDB || log "WARN" "清理Redis缓存失败"
        fi
    fi
}

# 验证回滚结果
verify_rollback() {
    log "INFO" "验证回滚结果..."
    
    local max_attempts=30
    local attempt=1
    
    # 等待服务启动
    log "INFO" "等待服务启动..."
    sleep 5
    
    # 检查后端服务
    local backend_url="http://localhost:3000/api/v1/health"
    
    while [[ $attempt -le $max_attempts ]]; do
        if curl -f -s "$backend_url" > /dev/null 2>&1; then
            log "INFO" "后端服务验证通过"
            break
        fi
        
        if [[ $attempt -eq $max_attempts ]]; then
            error_exit "后端服务验证失败，回滚可能不成功"
        fi
        
        log "INFO" "验证尝试 $attempt/$max_attempts，等待5秒..."
        sleep 5
        ((attempt++))
    done
    
    # 检查PM2进程
    if command -v pm2 &> /dev/null; then
        if pm2 list | grep -q "$PM2_APP_NAME.*online"; then
            log "INFO" "PM2进程验证通过"
        else
            log "ERROR" "PM2进程验证失败"
            return 1
        fi
    fi
    
    log "INFO" "回滚验证完成"
}

# 生成回滚报告
generate_report() {
    local backup_dir="$1"
    local report_file="${LOG_PATH}/rollback-report-${TIMESTAMP}.txt"
    
    log "INFO" "生成回滚报告..."
    
    cat > "$report_file" << EOF
====================================================================
宠物博客站群系统 - 回滚报告
====================================================================
回滚时间: $(date)
源备份: $backup_dir
目标路径: $DEPLOY_PATH
日志文件: $LOG_FILE

====================================================================
备份信息
====================================================================
备份创建时间: $(stat -c %y "$backup_dir" 2>/dev/null || echo "未知")
备份大小: $(du -sh "$backup_dir" 2>/dev/null | cut -f1 || echo "未知")

====================================================================
回滚后服务状态
====================================================================
PM2进程状态:
$(pm2 list 2>/dev/null || echo "PM2不可用")

系统资源:
$(df -h "$DEPLOY_PATH" 2>/dev/null || echo "磁盘信息获取失败")

====================================================================
验证结果
====================================================================
后端API健康检查: $(curl -f -s http://localhost:3000/api/v1/health > /dev/null 2>&1 && echo "✅ 通过" || echo "❌ 失败")

====================================================================
回滚结果
====================================================================
✅ 回滚成功完成
📝 详细日志: $LOG_FILE
🔄 如需再次回滚，可使用快照: /www/backups/pre-rollback-${TIMESTAMP}

====================================================================
EOF
    
    log "INFO" "回滚报告已生成: $report_file"
}

# ====================================================================
# 主函数
# ====================================================================

main() {
    local backup_dir=""
    local force_mode=false
    local auto_mode=false
    local dry_run=false
    local list_only=false
    
    # 创建日志目录
    mkdir -p "$LOG_PATH"
    
    # 解析参数
    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -l|--list)
                list_only=true
                shift
                ;;
            -f|--force)
                force_mode=true
                shift
                ;;
            --auto)
                auto_mode=true
                shift
                ;;
            --dry-run)
                dry_run=true
                shift
                ;;
            -*)
                error_exit "未知选项: $1"
                ;;
            *)
                backup_dir="$1"
                shift
                ;;
        esac
    done
    
    # 如果只是列出备份
    if [[ "$list_only" == true ]]; then
        list_backups
        exit 0
    fi
    
    # 自动模式：选择最新备份
    if [[ "$auto_mode" == true ]]; then
        backup_dir=$(find "$BACKUP_PATH" -maxdepth 1 -name "petcare-*" -type d | sort -r | head -n1)
        if [[ -z "$backup_dir" ]]; then
            error_exit "没有找到可用的备份"
        fi
        log "INFO" "自动选择最新备份: $(basename "$backup_dir")"
    fi
    
    # 检查备份目录参数
    if [[ -z "$backup_dir" ]]; then
        log "ERROR" "请指定要回滚的备份目录"
        echo
        list_backups
        echo
        show_help
        exit 1
    fi
    
    # 开始回滚流程
    log "INFO" "======================================================================"
    log "INFO" "开始执行回滚操作"
    log "INFO" "======================================================================"
    log "INFO" "回滚时间: $(date)"
    log "INFO" "备份目录: $backup_dir"
    log "INFO" "部署目录: $DEPLOY_PATH"
    log "INFO" "日志文件: $LOG_FILE"
    log "INFO" "======================================================================"
    
    # 模拟运行模式
    if [[ "$dry_run" == true ]]; then
        log "INFO" "模拟运行模式 - 将要执行的操作:"
        log "INFO" "1. 验证备份: $backup_dir"
        log "INFO" "2. 创建当前版本快照"
        log "INFO" "3. 停止服务: $PM2_APP_NAME"
        log "INFO" "4. 回滚到指定备份"
        log "INFO" "5. 启动服务"
        log "INFO" "6. 验证回滚结果"
        log "INFO" "模拟运行完成"
        exit 0
    fi
    
    # 确认操作
    if [[ "$force_mode" != true ]]; then
        echo
        log "WARN" "⚠️  即将执行回滚操作，这将："
        log "WARN" "   - 停止当前运行的应用服务"
        log "WARN" "   - 用备份覆盖当前部署目录"
        log "WARN" "   - 重启应用服务"
        echo
        read -p "确认继续？(y/N): " -r
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log "INFO" "回滚操作已取消"
            exit 0
        fi
    fi
    
    # 执行回滚步骤
    validate_backup "$backup_dir"
    create_snapshot
    stop_services
    perform_rollback "$backup_dir"
    reload_configs
    verify_rollback
    generate_report "$backup_dir"
    
    log "INFO" "======================================================================"
    log "INFO" "🎉 回滚操作成功完成！"
    log "INFO" "======================================================================"
    log "INFO" "📝 详细日志: $LOG_FILE"
    log "INFO" "📊 回滚报告: ${LOG_PATH}/rollback-report-${TIMESTAMP}.txt"
    log "INFO" "💾 回滚前快照: /www/backups/pre-rollback-${TIMESTAMP}"
    log "INFO" "======================================================================"
}

# 执行主函数
main "$@"