/**
 * Playwright配置 - E2E测试
 * 配置跨浏览器端到端测试环境
 */

import { defineConfig, devices } from '@playwright/test';
import * as path from 'path';

/**
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
  // 测试目录
  testDir: './e2e',
  
  // 并行运行测试
  fullyParallel: true,
  
  // 在CI环境中禁止使用.only
  forbidOnly: !!process.env.CI,
  
  // 失败重试次数
  retries: process.env.CI ? 2 : 0,
  
  // 并行工作进程数
  workers: process.env.CI ? 1 : undefined,
  
  // 报告器配置
  reporter: [
    ['html', { outputFolder: 'test-results/html-report' }],
    ['json', { outputFile: 'test-results/results.json' }],
    ['junit', { outputFile: 'test-results/junit.xml' }],
    process.env.CI ? ['github'] : ['list'],
  ],
  
  // 全局测试设置
  use: {
    // 基础URL（根据环境变量动态设置）
    baseURL: process.env.PLAYWRIGHT_BASE_URL || 'http://localhost:3000',
    
    // 浏览器上下文选项
    viewport: { width: 1280, height: 720 },
    
    // 是否忽略HTTPS错误
    ignoreHTTPSErrors: true,
    
    // 截图设置
    screenshot: 'only-on-failure',
    
    // 录制视频
    video: 'retain-on-failure',
    
    // 追踪
    trace: 'retain-on-failure',
    
    // 等待策略
    actionTimeout: 30000,
    navigationTimeout: 30000,
  },
  
  // 测试项目配置
  projects: [
    // Desktop browsers
    {
      name: 'chromium',
      use: { 
        ...devices['Desktop Chrome'],
        // 自定义用户代理
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 PetCare-Test',
      },
    },
    {
      name: 'firefox',
      use: { 
        ...devices['Desktop Firefox'],
        userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) PetCare-Test',
      },
    },
    {
      name: 'webkit',
      use: { 
        ...devices['Desktop Safari'],
        userAgent: 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) PetCare-Test',
      },
    },
    
    // Mobile devices
    {
      name: 'Mobile Chrome',
      use: { 
        ...devices['Pixel 5'],
        userAgent: devices['Pixel 5'].userAgent + ' PetCare-Test',
      },
    },
    {
      name: 'Mobile Safari',
      use: { 
        ...devices['iPhone 12'],
        userAgent: devices['iPhone 12'].userAgent + ' PetCare-Test',
      },
    },
    
    // Tablet
    {
      name: 'Tablet Chrome',
      use: { 
        ...devices['iPad Pro'],
        userAgent: devices['iPad Pro'].userAgent + ' PetCare-Test',
      },
    },
  ],
  
  // Web服务器配置（用于测试）
  webServer: [
    {
      // 后端API服务器
      command: 'cd backend && npm run dev',
      port: 3000,
      reuseExistingServer: !process.env.CI,
      timeout: 60000,
      env: {
        NODE_ENV: 'test',
        PORT: '3000',
      },
    },
    {
      // 英文前端站点
      command: 'cd frontend/en && npm run dev',
      port: 4321,
      reuseExistingServer: !process.env.CI,
      timeout: 60000,
    },
    {
      // 德文前端站点
      command: 'cd frontend/de && npm run dev',
      port: 4322,
      reuseExistingServer: !process.env.CI,
      timeout: 60000,
    },
    {
      // 俄文前端站点
      command: 'cd frontend/ru && npm run dev',
      port: 4323,
      reuseExistingServer: !process.env.CI,
      timeout: 60000,
    },
  ],
  
  // 全局超时设置
  timeout: 60000,
  expect: {
    // 断言超时
    timeout: 10000,
    
    // 截图比较阈值
    toHaveScreenshot: { threshold: 0.2 },
    toMatchScreenshot: { threshold: 0.2 },
  },
  
  // 输出目录
  outputDir: 'test-results',
  
  // 测试模式
  testIgnore: [
    '**/*.skip.spec.ts',
    '**/*.wip.spec.ts',
  ],
  
  // 全局设置
  globalSetup: path.join(__dirname, 'e2e/global-setup.ts'),
  globalTeardown: path.join(__dirname, 'e2e/global-teardown.ts'),
});