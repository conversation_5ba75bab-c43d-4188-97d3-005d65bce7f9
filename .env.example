# 宠物博客站群系统 - 环境变量配置模板
# 复制此文件为 .env 并填入实际配置值

# ===========================================
# 基础环境配置
# ===========================================
NODE_ENV=development
API_PREFIX=/api/v1

# ===========================================
# 数据库配置 (远程MySQL)
# ===========================================
DB_HOST=************
DB_PORT=3306
DB_NAME=bengtai
DB_USER=bengtai
DB_PASSWORD=weizhen258
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci

# 数据库连接池配置
DB_CONNECTION_LIMIT=10
DB_ACQUIRETIMEOUT=10000
DB_TIMEOUT=5000

# ===========================================
# Redis 缓存配置
# ===========================================
REDIS_HOST=127.0.0.1
REDIS_PORT=6379
REDIS_PASSWORD=your_redis_password_here
REDIS_DB=0

# Redis 连接配置
REDIS_CONNECT_TIMEOUT=5000
REDIS_COMMAND_TIMEOUT=2000
REDIS_RETRY_DELAY=100

# ===========================================
# JWT 认证配置
# ===========================================
JWT_SECRET=your_jwt_secret_key_minimum_32_characters
JWT_EXPIRES_IN=7d
JWT_REFRESH_EXPIRES_IN=30d
JWT_ISSUER=petcare-blog
JWT_AUDIENCE=petcare-users

# ===========================================
# AI 翻译服务配置 (Gemini)
# ===========================================
GEMINI_API_ENDPOINT=https://ai.wanderintree.top
GEMINI_API_KEY=sk-SMXjycC5GnJRswpJB8Ef6f632d794bBa9a1bAbB828E7Ee9d
GEMINI_MODEL=gemini-2.5-pro
GEMINI_MAX_TOKENS=8192
GEMINI_TEMPERATURE=0.7
GEMINI_TIMEOUT=30000

# ===========================================
# 文件上传配置
# ===========================================
UPLOAD_DIR=./uploads
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=jpg,jpeg,png,gif,webp,pdf
UPLOAD_URL_PREFIX=/uploads

# CDN配置 (可选)
CDN_ENABLED=false
CDN_BASE_URL=https://cdn.example.com
CDN_BUCKET=petcare-assets

# ===========================================
# 邮件服务配置 (可选)
# ===========================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password
MAIL_FROM_NAME=PetCare Blog
MAIL_FROM_ADDRESS=<EMAIL>

# ===========================================
# 安全配置
# ===========================================
# CORS配置
CORS_ORIGIN=http://localhost:4321,http://localhost:4322,http://localhost:4323,http://localhost:4324
CORS_CREDENTIALS=true

# 限流配置
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_SKIP_SUCCESS_REQUESTS=false

# 会话配置
SESSION_SECRET=your_session_secret_key_here
SESSION_MAX_AGE=86400000

# ===========================================
# 日志配置
# ===========================================
LOG_LEVEL=info
LOG_FILE=./logs/app.log
LOG_MAX_SIZE=10m
LOG_MAX_FILES=5
LOG_DATE_PATTERN=YYYY-MM-DD

# ===========================================
# 站点配置
# ===========================================
# 英文站点
SITE_EN_DOMAIN=localhost:4321
SITE_EN_NAME=PetCare Blog
SITE_EN_DESCRIPTION=Your trusted source for pet care information

# 德文站点
SITE_DE_DOMAIN=localhost:4322
SITE_DE_NAME=Haustier Pflege
SITE_DE_DESCRIPTION=Ihre vertrauenswürdige Quelle für Haustierpflege

# 俄文站点
SITE_RU_DOMAIN=localhost:4323
SITE_RU_NAME=Уход за Домашними Животными
SITE_RU_DESCRIPTION=Надежный источник информации об уходе за домашними животными

# 管理后台
ADMIN_DOMAIN=localhost:4324
ADMIN_NAME=PetCare Admin
ADMIN_SECRET_KEY=your_admin_secret_key_here

# ===========================================
# SEO 配置
# ===========================================
SITEMAP_BASE_URL=https://www.petcare.com
ROBOTS_ALLOW_ALL=true
META_DEFAULT_KEYWORDS=pet,care,blog,animals,health
ANALYTICS_ID=G-XXXXXXXXXX

# ===========================================
# 第三方服务配置
# ===========================================
# Google Analytics
GA_TRACKING_ID=G-XXXXXXXXXX
GA_DEBUG=false

# Sentry 错误监控 (可选)
SENTRY_DSN=https://<EMAIL>/project-id
SENTRY_ENVIRONMENT=development

# Cloudflare (可选)
CLOUDFLARE_ZONE_ID=your_zone_id
CLOUDFLARE_API_TOKEN=your_api_token

# ===========================================
# 开发环境特定配置
# ===========================================
# 是否启用调试模式
DEBUG=true
DEBUG_NAMESPACE=petcare:*

# 是否启用热重载
HOT_RELOAD=true

# API文档配置
API_DOCS_ENABLED=true
API_DOCS_PATH=/api-docs

# 测试数据库 (可选)
TEST_DB_NAME=petcare_test
TEST_DB_USER=test_user
TEST_DB_PASSWORD=test_password