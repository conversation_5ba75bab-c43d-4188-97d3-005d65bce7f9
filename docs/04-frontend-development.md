# 宠物博客站群系统 - 前端开发文档

## 目录

- [1. Astro项目配置](#1-astro项目配置)
- [2. 项目结构设计](#2-项目结构设计)
- [3. 组件架构设计](#3-组件架构设计)
- [4. SEO实施方案](#4-seo实施方案)
- [5. 性能优化策略](#5-性能优化策略)
- [6. 多语言实现方案](#6-多语言实现方案)
- [7. 样式系统设计](#7-样式系统设计)
- [8. 开发规范与最佳实践](#8-开发规范与最佳实践)

## 1. Astro项目配置

### 1.1 初始化项目

```bash
# 创建新的Astro项目
npm create astro@latest petcare-frontend -- --template minimal

# 安装依赖
cd petcare-frontend
npm install

# 安装必要的包
npm install @astrojs/tailwind @astrojs/sitemap @astrojs/image
npm install @astrojs/prefetch @astrojs/compress
npm install axios dayjs slugify
```

### 1.2 核心配置文件 (astro.config.mjs)

```javascript
import { defineConfig } from 'astro/config';
import tailwind from '@astrojs/tailwind';
import sitemap from '@astrojs/sitemap';
import compress from '@astrojs/compress';
import prefetch from '@astrojs/prefetch';

// 根据环境变量确定站点配置
const SITE_CONFIG = {
  'en-US': {
    site: 'https://www.petcare.com',
    base: '/',
  },
  'de-DE': {
    site: 'https://www.haustiere.de',
    base: '/',
  },
  'ru-RU': {
    site: 'https://www.домашние-животные.рф',
    base: '/',
  },
};

const currentLang = process.env.SITE_LANG || 'en-US';
const config = SITE_CONFIG[currentLang];

export default defineConfig({
  site: config.site,
  base: config.base,

  integrations: [
    tailwind({
      config: { applyBaseStyles: false },
    }),
    sitemap({
      filter: (page) => !page.includes('/admin/'),
      customPages: [],
      serialize: (item) => {
        item.changefreq = 'weekly';
        item.priority = item.url.includes('/articles/') ? 0.8 : 0.5;
        return item;
      },
    }),
    compress({
      css: true,
      html: {
        removeAttributeQuotes: false,
      },
      img: false, // 使用专门的图片优化
      js: true,
      svg: true,
    }),
    prefetch({
      selector: "a[href^='/']",
      throttle: 3,
    }),
  ],

  output: 'static',

  build: {
    format: 'directory',
    assets: '_assets',
  },

  vite: {
    build: {
      cssCodeSplit: true,
      rollupOptions: {
        output: {
          assetFileNames: '_assets/[name].[hash][extname]',
          chunkFileNames: '_assets/[name].[hash].js',
          entryFileNames: '_assets/[name].[hash].js',
        },
      },
    },
    ssr: {
      external: ['node:fs', 'node:path', 'node:url'],
    },
  },

  markdown: {
    shikiConfig: {
      theme: 'github-light',
      wrap: true,
    },
  },

  experimental: {
    assets: true,
    viewTransitions: true,
  },
});
```

### 1.3 环境变量配置 (.env)

```bash
# API配置
PUBLIC_API_URL=https://api.petcare.com/api/v1
PUBLIC_CDN_URL=https://cdn.petcare.com

# 站点语言
SITE_LANG=en-US

# SEO配置
PUBLIC_SITE_NAME=PetCare
PUBLIC_DEFAULT_OG_IMAGE=https://cdn.petcare.com/og-default.jpg

# 分析工具
PUBLIC_GA_ID=G-XXXXXXXXXX
PUBLIC_GTM_ID=GTM-XXXXXXX

# 功能开关
PUBLIC_ENABLE_COMMENTS=true
PUBLIC_ENABLE_ADS=true
```

### 1.4 TypeScript配置 (tsconfig.json)

```json
{
  "extends": "astro/tsconfigs/strict",
  "compilerOptions": {
    "baseUrl": ".",
    "paths": {
      "@components/*": ["src/components/*"],
      "@layouts/*": ["src/layouts/*"],
      "@utils/*": ["src/utils/*"],
      "@api/*": ["src/api/*"],
      "@styles/*": ["src/styles/*"],
      "@types/*": ["src/types/*"],
      "@config/*": ["src/config/*"]
    }
  }
}
```

## 2. 项目结构设计

### 2.1 目录结构

```
petcare-frontend/
├── public/
│   ├── robots.txt
│   ├── favicon.ico
│   └── images/
├── src/
│   ├── api/
│   │   ├── client.ts
│   │   ├── articles.ts
│   │   ├── categories.ts
│   │   └── comments.ts
│   ├── components/
│   │   ├── common/
│   │   │   ├── Header.astro
│   │   │   ├── Footer.astro
│   │   │   ├── Navigation.astro
│   │   │   └── Breadcrumb.astro
│   │   ├── article/
│   │   │   ├── ArticleCard.astro
│   │   │   ├── ArticleList.astro
│   │   │   ├── ArticleContent.astro
│   │   │   └── RelatedArticles.astro
│   │   ├── seo/
│   │   │   ├── SEOHead.astro
│   │   │   ├── StructuredData.astro
│   │   │   └── OpenGraph.astro
│   │   └── ui/
│   │       ├── Button.astro
│   │       ├── Card.astro
│   │       ├── Pagination.astro
│   │       └── SearchBox.astro
│   ├── config/
│   │   ├── site.ts
│   │   ├── seo.ts
│   │   └── languages.ts
│   ├── layouts/
│   │   ├── BaseLayout.astro
│   │   ├── ArticleLayout.astro
│   │   └── CategoryLayout.astro
│   ├── pages/
│   │   ├── index.astro
│   │   ├── [...slug].astro
│   │   ├── category/
│   │   │   └── [...category].astro
│   │   ├── search.astro
│   │   └── 404.astro
│   ├── styles/
│   │   ├── global.css
│   │   └── tailwind.css
│   ├── types/
│   │   ├── api.ts
│   │   └── content.ts
│   └── utils/
│       ├── seo.ts
│       ├── format.ts
│       ├── api.ts
│       └── images.ts
├── astro.config.mjs
├── tailwind.config.cjs
├── package.json
└── tsconfig.json
```

## 3. 组件架构设计

### 3.1 基础布局组件 (BaseLayout.astro)

```astro
---
import SEOHead from '@components/seo/SEOHead.astro';
import Header from '@components/common/Header.astro';
import Footer from '@components/common/Footer.astro';
import Analytics from '@components/Analytics.astro';
import { ViewTransitions } from 'astro:transitions';

export interface Props {
  title: string;
  description?: string;
  keywords?: string;
  ogImage?: string;
  structuredData?: any;
  noindex?: boolean;
}

const {
  title,
  description,
  keywords,
  ogImage,
  structuredData,
  noindex = false
} = Astro.props;
---

<!DOCTYPE html>
<html lang={import.meta.env.SITE_LANG || 'en-US'}>
  <head>
    <SEOHead
      title={title}
      description={description}
      keywords={keywords}
      ogImage={ogImage}
      noindex={noindex}
    />
    {structuredData && (
      <script type="application/ld+json" set:html={JSON.stringify(structuredData)} />
    )}
    <ViewTransitions />
  </head>
  <body>
    <Header />
    <main id="main-content">
      <slot />
    </main>
    <Footer />
    <Analytics />
  </body>
</html>

<style>
  body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  main {
    flex: 1;
  }
</style>
```

### 3.2 SEO头部组件 (SEOHead.astro)

```astro
---
import { SITE_CONFIG, DEFAULT_OG_IMAGE } from '@config/site';

export interface Props {
  title: string;
  description?: string;
  keywords?: string;
  ogImage?: string;
  noindex?: boolean;
  canonical?: string;
}

const {
  title,
  description = SITE_CONFIG.description,
  keywords,
  ogImage = DEFAULT_OG_IMAGE,
  noindex = false,
  canonical = Astro.url.href
} = Astro.props;

const fullTitle = `${title} | ${SITE_CONFIG.name}`;
---

<!-- 基础元标签 -->
<meta charset="UTF-8" />
<meta name="viewport" content="width=device-width, initial-scale=1.0" />
<title>{fullTitle}</title>
<meta name="description" content={description} />
{keywords && <meta name="keywords" content={keywords} />}
{noindex && <meta name="robots" content="noindex, nofollow" />}

<!-- Canonical URL -->
<link rel="canonical" href={canonical} />

<!-- Open Graph -->
<meta property="og:type" content="website" />
<meta property="og:title" content={fullTitle} />
<meta property="og:description" content={description} />
<meta property="og:image" content={ogImage} />
<meta property="og:url" content={canonical} />
<meta property="og:site_name" content={SITE_CONFIG.name} />
<meta property="og:locale" content={SITE_CONFIG.locale} />

<!-- Twitter Card -->
<meta name="twitter:card" content="summary_large_image" />
<meta name="twitter:title" content={fullTitle} />
<meta name="twitter:description" content={description} />
<meta name="twitter:image" content={ogImage} />

<!-- Favicon -->
<link rel="icon" type="image/x-icon" href="/favicon.ico" />
<link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
<link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
<link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />

<!-- Preconnect -->
<link rel="preconnect" href={import.meta.env.PUBLIC_API_URL} />
<link rel="preconnect" href={import.meta.env.PUBLIC_CDN_URL} />
<link rel="preconnect" href="https://fonts.googleapis.com" />
<link rel="preconnect" href="https://www.google-analytics.com" />

<!-- RSS Feed -->
<link rel="alternate" type="application/rss+xml" title={`${SITE_CONFIG.name} RSS Feed`} href="/rss.xml" />
```

### 3.3 文章卡片组件 (ArticleCard.astro)

```astro
---
import { Image } from 'astro:assets';
import { formatDate } from '@utils/format';
import { generateImageUrl } from '@utils/images';

export interface Props {
  article: {
    id: number;
    title: string;
    slug: string;
    summary: string;
    featured_image?: string;
    category: {
      name: string;
      slug: string;
    };
    published_at: string;
    view_count: number;
    comment_count: number;
  };
  lazy?: boolean;
}

const { article, lazy = true } = Astro.props;
const articleUrl = `/${article.slug}`;
const categoryUrl = `/category/${article.category.slug}`;
const imageUrl = generateImageUrl(article.featured_image, { width: 400, height: 300 });
---

<article class="article-card group">
  <a href={articleUrl} class="block overflow-hidden">
    <div class="aspect-w-4 aspect-h-3 bg-gray-100">
      <Image
        src={imageUrl}
        alt={article.title}
        width={400}
        height={300}
        loading={lazy ? 'lazy' : 'eager'}
        class="object-cover w-full h-full group-hover:scale-105 transition-transform duration-300"
      />
    </div>
  </a>

  <div class="p-6">
    <div class="flex items-center gap-4 text-sm text-gray-500 mb-3">
      <a href={categoryUrl} class="hover:text-primary">
        {article.category.name}
      </a>
      <time datetime={article.published_at}>
        {formatDate(article.published_at)}
      </time>
    </div>

    <h3 class="mb-3">
      <a href={articleUrl} class="text-xl font-bold text-gray-900 hover:text-primary line-clamp-2">
        {article.title}
      </a>
    </h3>

    <p class="text-gray-600 line-clamp-3 mb-4">
      {article.summary}
    </p>

    <div class="flex items-center gap-4 text-sm text-gray-500">
      <span class="flex items-center gap-1">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
        </svg>
        {article.view_count}
      </span>
      <span class="flex items-center gap-1">
        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 12h.01M12 12h.01M16 12h.01M21 12c0 4.418-4.03 8-9 8a9.863 9.863 0 01-4.255-.949L3 20l1.395-3.72C3.512 15.042 3 13.574 3 12c0-4.418 4.03-8 9-8s9 3.582 9 8z" />
        </svg>
        {article.comment_count}
      </span>
    </div>
  </div>
</article>

<style>
  .article-card {
    @apply bg-white rounded-lg shadow-md hover:shadow-lg transition-shadow duration-300;
  }
</style>
```

### 3.4 结构化数据组件 (StructuredData.astro)

```astro
---
export interface Props {
  type: 'Article' | 'BreadcrumbList' | 'Organization' | 'WebSite';
  data: any;
}

const { type, data } = Astro.props;

function generateArticleSchema(article: any) {
  return {
    "@context": "https://schema.org",
    "@type": "Article",
    "headline": article.title,
    "description": article.summary,
    "image": article.featured_image,
    "datePublished": article.published_at,
    "dateModified": article.updated_at,
    "author": {
      "@type": "Person",
      "name": article.author.name
    },
    "publisher": {
      "@type": "Organization",
      "name": import.meta.env.PUBLIC_SITE_NAME,
      "logo": {
        "@type": "ImageObject",
        "url": `${import.meta.env.PUBLIC_CDN_URL}/logo.png`
      }
    },
    "mainEntityOfPage": {
      "@type": "WebPage",
      "@id": article.url
    }
  };
}

function generateBreadcrumbSchema(items: any[]) {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": items.map((item, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": item.name,
      "item": item.url
    }))
  };
}

function generateOrganizationSchema(org: any) {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": org.name,
    "url": org.url,
    "logo": org.logo,
    "sameAs": org.socialLinks || []
  };
}

function generateWebSiteSchema(site: any) {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": site.name,
    "url": site.url,
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${site.url}/search?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    }
  };
}

let schema;
switch (type) {
  case 'Article':
    schema = generateArticleSchema(data);
    break;
  case 'BreadcrumbList':
    schema = generateBreadcrumbSchema(data);
    break;
  case 'Organization':
    schema = generateOrganizationSchema(data);
    break;
  case 'WebSite':
    schema = generateWebSiteSchema(data);
    break;
}
---

{schema && (
  <script type="application/ld+json" set:html={JSON.stringify(schema)} />
)}
```

## 4. SEO实施方案

### 4.1 URL结构优化

```typescript
// src/utils/seo.ts
export function generateSlug(title: string, lang: string): string {
  const slugify = (str: string) => {
    return str
      .toLowerCase()
      .trim()
      .replace(/[^\w\s-]/g, '')
      .replace(/[\s_-]+/g, '-')
      .replace(/^-+|-+$/g, '');
  };

  // 不同语言的URL处理
  switch (lang) {
    case 'en-US':
      return slugify(title);
    case 'de-DE':
      // 德语特殊字符处理
      return slugify(
        title.replace(/ä/g, 'ae').replace(/ö/g, 'oe').replace(/ü/g, 'ue').replace(/ß/g, 'ss'),
      );
    case 'ru-RU':
      // 俄语转写
      return transliterate(title);
    default:
      return slugify(title);
  }
}

export function generateCanonicalUrl(slug: string, lang: string): string {
  const siteUrl = getSiteUrl(lang);
  return `${siteUrl}/${slug}`;
}
```

### 4.2 图片优化策略

```typescript
// src/utils/images.ts
interface ImageOptions {
  width?: number;
  height?: number;
  format?: 'webp' | 'jpg' | 'png';
  quality?: number;
}

export function generateImageUrl(src: string | undefined, options: ImageOptions = {}): string {
  if (!src) return '/images/placeholder.jpg';

  const { width = 800, height = 600, format = 'webp', quality = 85 } = options;

  // 如果是CDN URL，添加转换参数
  if (src.startsWith(import.meta.env.PUBLIC_CDN_URL)) {
    const params = new URLSearchParams({
      w: width.toString(),
      h: height.toString(),
      fm: format,
      q: quality.toString(),
      fit: 'cover',
    });
    return `${src}?${params.toString()}`;
  }

  return src;
}

export function generateResponsiveImageSizes(aspectRatio: string) {
  const sizes = {
    '16:9': [
      { width: 320, height: 180 },
      { width: 640, height: 360 },
      { width: 1280, height: 720 },
      { width: 1920, height: 1080 },
    ],
    '4:3': [
      { width: 320, height: 240 },
      { width: 640, height: 480 },
      { width: 1280, height: 960 },
    ],
    '1:1': [
      { width: 320, height: 320 },
      { width: 640, height: 640 },
      { width: 1280, height: 1280 },
    ],
  };

  return sizes[aspectRatio] || sizes['16:9'];
}
```

### 4.3 站点地图生成

```typescript
// src/pages/sitemap.xml.ts
import type { APIRoute } from 'astro';
import { fetchAllArticles, fetchAllCategories } from '@api/content';

export const get: APIRoute = async ({ site }) => {
  const articles = await fetchAllArticles();
  const categories = await fetchAllCategories();

  const staticPages = [
    { url: '/', changefreq: 'daily', priority: 1.0 },
    { url: '/search', changefreq: 'weekly', priority: 0.8 },
  ];

  const articlePages = articles.map((article) => ({
    url: `/${article.slug}`,
    changefreq: 'weekly',
    priority: 0.7,
    lastmod: article.updated_at,
  }));

  const categoryPages = categories.map((category) => ({
    url: `/category/${category.slug}`,
    changefreq: 'weekly',
    priority: 0.6,
  }));

  const allPages = [...staticPages, ...articlePages, ...categoryPages];

  const sitemap = `<?xml version="1.0" encoding="UTF-8"?>
    <urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
      ${allPages
        .map(
          (page) => `
        <url>
          <loc>${site}${page.url}</loc>
          <changefreq>${page.changefreq}</changefreq>
          <priority>${page.priority}</priority>
          ${page.lastmod ? `<lastmod>${page.lastmod}</lastmod>` : ''}
        </url>
      `,
        )
        .join('')}
    </urlset>`;

  return new Response(sitemap, {
    headers: {
      'Content-Type': 'application/xml',
      'Cache-Control': 'public, max-age=3600',
    },
  });
};
```

### 4.4 robots.txt配置

```text
# public/robots.txt
User-agent: *
Allow: /
Disallow: /admin/
Disallow: /api/
Disallow: /_assets/

# 特定爬虫规则
User-agent: Googlebot
Crawl-delay: 0

User-agent: Bingbot
Crawl-delay: 1

# 站点地图
Sitemap: https://www.petcare.com/sitemap.xml
```

## 5. 性能优化策略

### 5.1 资源加载优化

```astro
---
// src/components/common/ResourceHints.astro
---

<!-- DNS预解析 -->
<link rel="dns-prefetch" href="https://api.petcare.com" />
<link rel="dns-prefetch" href="https://cdn.petcare.com" />
<link rel="dns-prefetch" href="https://www.google-analytics.com" />

<!-- 预连接 -->
<link rel="preconnect" href="https://api.petcare.com" crossorigin />
<link rel="preconnect" href="https://cdn.petcare.com" crossorigin />

<!-- 预加载关键资源 -->
<link rel="preload" href="/fonts/inter-var.woff2" as="font" type="font/woff2" crossorigin />
<link rel="preload" href="/_assets/critical.css" as="style" />

<!-- 预获取下一页资源 -->
{Astro.props.nextPageUrl && (
  <link rel="prefetch" href={Astro.props.nextPageUrl} />
)}
```

### 5.2 懒加载实现

```astro
---
// src/components/ui/LazyImage.astro
export interface Props {
  src: string;
  alt: string;
  width: number;
  height: number;
  class?: string;
}

const { src, alt, width, height, class: className } = Astro.props;
const placeholder = `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 ${width} ${height}'%3E%3Crect width='100%25' height='100%25' fill='%23f3f4f6'/%3E%3C/svg%3E`;
---

<div class={`lazy-image-wrapper ${className}`} data-lazy-image>
  <img
    src={placeholder}
    data-src={src}
    alt={alt}
    width={width}
    height={height}
    loading="lazy"
    class="lazy-image"
  />
  <noscript>
    <img src={src} alt={alt} width={width} height={height} />
  </noscript>
</div>

<script>
  // 使用Intersection Observer实现懒加载
  if ('IntersectionObserver' in window) {
    const imageObserver = new IntersectionObserver((entries, observer) => {
      entries.forEach(entry => {
        if (entry.isIntersecting) {
          const img = entry.target.querySelector('img');
          if (img && img.dataset.src) {
            img.src = img.dataset.src;
            img.removeAttribute('data-src');
            img.classList.add('loaded');
            observer.unobserve(entry.target);
          }
        }
      });
    }, {
      rootMargin: '50px 0px',
      threshold: 0.01
    });

    document.querySelectorAll('[data-lazy-image]').forEach(wrapper => {
      imageObserver.observe(wrapper);
    });
  }
</script>

<style>
  .lazy-image-wrapper {
    position: relative;
    overflow: hidden;
  }

  .lazy-image {
    transition: opacity 0.3s;
    opacity: 0;
  }

  .lazy-image.loaded {
    opacity: 1;
  }
</style>
```

### 5.3 关键CSS内联

```astro
---
// src/components/CriticalCSS.astro
const criticalCSS = `
  /* 关键CSS - 首屏渲染所需的最小样式 */
  *, ::before, ::after {
    box-sizing: border-box;
  }

  html {
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  }

  body {
    margin: 0;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
  }

  main {
    flex: 1;
  }

  /* 防止内容跳动 */
  .header {
    height: 80px;
  }

  /* 隐藏未加载的内容 */
  [data-lazy] {
    visibility: hidden;
  }
`;
---

<style set:html={criticalCSS}></style>
```

## 6. 多语言实现方案

### 6.1 语言配置

```typescript
// src/config/languages.ts
export const LANGUAGES = {
  'en-US': {
    code: 'en-US',
    name: 'English',
    locale: 'en_US',
    dateFormat: 'MMM DD, YYYY',
    currency: 'USD',
    dir: 'ltr',
  },
  'de-DE': {
    code: 'de-DE',
    name: 'Deutsch',
    locale: 'de_DE',
    dateFormat: 'DD.MM.YYYY',
    currency: 'EUR',
    dir: 'ltr',
  },
  'ru-RU': {
    code: 'ru-RU',
    name: 'Русский',
    locale: 'ru_RU',
    dateFormat: 'DD.MM.YYYY',
    currency: 'RUB',
    dir: 'ltr',
  },
};

export function getCurrentLanguage(): string {
  return import.meta.env.SITE_LANG || 'en-US';
}

export function getLanguageConfig(lang: string) {
  return LANGUAGES[lang] || LANGUAGES['en-US'];
}
```

### 6.2 构建脚本

```json
// package.json
{
  "scripts": {
    "dev": "astro dev",
    "build": "npm run build:all",
    "build:all": "npm run build:en && npm run build:de && npm run build:ru",
    "build:en": "SITE_LANG=en-US astro build --outDir dist/en",
    "build:de": "SITE_LANG=de-DE astro build --outDir dist/de",
    "build:ru": "SITE_LANG=ru-RU astro build --outDir dist/ru",
    "preview": "astro preview"
  }
}
```

### 6.3 部署配置

```nginx
# nginx配置示例
server {
    server_name www.petcare.com;
    root /var/www/petcare/dist/en;

    location / {
        try_files $uri $uri/ /index.html;
    }
}

server {
    server_name www.haustiere.de;
    root /var/www/petcare/dist/de;

    location / {
        try_files $uri $uri/ /index.html;
    }
}

server {
    server_name www.домашние-животные.рф;
    root /var/www/petcare/dist/ru;

    location / {
        try_files $uri $uri/ /index.html;
    }
}
```

## 7. 样式系统设计

### 7.1 Tailwind配置 (tailwind.config.cjs)

```javascript
/** @type {import('tailwindcss').Config} */
module.exports = {
  content: ['./src/**/*.{astro,html,js,jsx,md,mdx,svelte,ts,tsx,vue}'],
  theme: {
    extend: {
      colors: {
        primary: {
          50: '#f0f9ff',
          100: '#e0f2fe',
          200: '#bae6fd',
          300: '#7dd3fc',
          400: '#38bdf8',
          500: '#0ea5e9',
          600: '#0284c7',
          700: '#0369a1',
          800: '#075985',
          900: '#0c4a6e',
        },
        gray: {
          50: '#f9fafb',
          100: '#f3f4f6',
          200: '#e5e7eb',
          300: '#d1d5db',
          400: '#9ca3af',
          500: '#6b7280',
          600: '#4b5563',
          700: '#374151',
          800: '#1f2937',
          900: '#111827',
        },
      },
      fontFamily: {
        sans: ['Inter var', 'system-ui', '-apple-system', 'sans-serif'],
        serif: ['Georgia', 'serif'],
      },
      typography: {
        DEFAULT: {
          css: {
            maxWidth: 'none',
            color: '#374151',
            a: {
              color: '#0ea5e9',
              '&:hover': {
                color: '#0284c7',
              },
            },
            'h1, h2, h3, h4': {
              color: '#111827',
            },
          },
        },
      },
      animation: {
        'fade-in': 'fadeIn 0.5s ease-in-out',
        'slide-up': 'slideUp 0.3s ease-out',
      },
      keyframes: {
        fadeIn: {
          '0%': { opacity: '0' },
          '100%': { opacity: '1' },
        },
        slideUp: {
          '0%': { transform: 'translateY(10px)', opacity: '0' },
          '100%': { transform: 'translateY(0)', opacity: '1' },
        },
      },
    },
  },
  plugins: [
    require('@tailwindcss/typography'),
    require('@tailwindcss/forms'),
    require('@tailwindcss/aspect-ratio'),
    require('@tailwindcss/line-clamp'),
  ],
};
```

### 7.2 全局样式 (global.css)

```css
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* 自定义字体 */
@font-face {
  font-family: 'Inter var';
  font-style: normal;
  font-weight: 100 900;
  font-display: swap;
  src: url('/fonts/inter-var.woff2') format('woff2');
}

/* 基础样式 */
@layer base {
  html {
    scroll-behavior: smooth;
  }

  body {
    @apply text-gray-700 antialiased;
  }

  /* 焦点样式 - 可访问性 */
  :focus {
    @apply outline-none ring-2 ring-primary-500 ring-offset-2;
  }

  /* 选择文本样式 */
  ::selection {
    @apply bg-primary-200 text-primary-900;
  }
}

/* 组件样式 */
@layer components {
  /* 容器 */
  .container {
    @apply mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* 标题样式 */
  .heading-1 {
    @apply text-4xl md:text-5xl font-bold text-gray-900 leading-tight;
  }

  .heading-2 {
    @apply text-3xl md:text-4xl font-bold text-gray-900 leading-tight;
  }

  .heading-3 {
    @apply text-2xl md:text-3xl font-semibold text-gray-900;
  }

  /* 按钮样式 */
  .btn {
    @apply inline-flex items-center justify-center px-6 py-3 border border-transparent text-base font-medium rounded-md transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-offset-2;
  }

  .btn-primary {
    @apply btn bg-primary-600 text-white hover:bg-primary-700 focus:ring-primary-500;
  }

  .btn-secondary {
    @apply btn bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500;
  }

  /* 卡片样式 */
  .card {
    @apply bg-white rounded-lg shadow-md overflow-hidden;
  }

  .card-hover {
    @apply card hover:shadow-lg transition-shadow duration-300;
  }
}

/* 工具类 */
@layer utilities {
  /* 文本截断 */
  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* 渐变背景 */
  .gradient-primary {
    @apply bg-gradient-to-r from-primary-500 to-primary-600;
  }

  /* 滚动条样式 */
  .scrollbar-thin {
    scrollbar-width: thin;
    scrollbar-color: theme('colors.gray.400') theme('colors.gray.100');
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    @apply bg-gray-100;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    @apply bg-gray-400 rounded;
  }
}

/* 动画 */
@keyframes skeleton-loading {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.skeleton {
  display: inline-block;
  height: 1em;
  position: relative;
  overflow: hidden;
  background-color: #eee;
  background-image: linear-gradient(90deg, #eee, #f5f5f5, #eee);
  background-size: 200px 100%;
  background-repeat: no-repeat;
  animation: skeleton-loading 1.2s ease-in-out infinite;
}
```

## 8. 开发规范与最佳实践

### 8.1 代码规范

```typescript
// .eslintrc.js
module.exports = {
  extends: [
    'eslint:recommended',
    'plugin:@typescript-eslint/recommended',
    'plugin:astro/recommended',
  ],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaVersion: 'latest',
    sourceType: 'module',
  },
  rules: {
    'no-console': ['warn', { allow: ['warn', 'error'] }],
    '@typescript-eslint/no-unused-vars': ['error', { argsIgnorePattern: '^_' }],
    '@typescript-eslint/explicit-function-return-type': 'off',
    'astro/no-set-html-directive': 'error',
  },
  overrides: [
    {
      files: ['*.astro'],
      parser: 'astro-eslint-parser',
      parserOptions: {
        parser: '@typescript-eslint/parser',
        extraFileExtensions: ['.astro'],
      },
    },
  ],
};
```

### 8.2 Git提交规范

```bash
# 提交信息格式
# type(scope): subject

# type类型
feat: 新功能
fix: 修复bug
docs: 文档更新
style: 代码格式调整
refactor: 重构
perf: 性能优化
test: 测试相关
chore: 构建过程或辅助工具的变动

# 示例
feat(article): add comment system
fix(seo): correct canonical URL generation
perf(images): implement lazy loading
```

### 8.3 组件开发规范

```astro
---
// 组件模板示例
// src/components/ExampleComponent.astro

// 1. 导入声明
import BaseLayout from '@layouts/BaseLayout.astro';
import { formatDate } from '@utils/format';

// 2. 类型定义
export interface Props {
  title: string;
  description?: string;
  items: Array<{
    id: number;
    name: string;
  }>;
}

// 3. Props解构和默认值
const {
  title,
  description = 'Default description',
  items = []
} = Astro.props;

// 4. 数据获取和处理
const processedItems = items.map(item => ({
  ...item,
  url: `/items/${item.id}`
}));

// 5. 条件判断
const showDescription = description && description.length > 0;
---

<!-- 6. 模板结构 -->
<section class="example-component">
  <h2>{title}</h2>

  {showDescription && (
    <p class="description">{description}</p>
  )}

  <ul class="items-list">
    {processedItems.map((item) => (
      <li key={item.id}>
        <a href={item.url}>{item.name}</a>
      </li>
    ))}
  </ul>

  <!-- 7. 插槽使用 -->
  <slot name="footer" />
</section>

<!-- 8. 样式定义 -->
<style>
  .example-component {
    @apply py-8;
  }

  .description {
    @apply text-gray-600 mb-4;
  }

  .items-list {
    @apply space-y-2;
  }
</style>

<!-- 9. 客户端脚本（如需要） -->
<script>
  // 只在需要客户端交互时添加
  document.querySelectorAll('.items-list a').forEach(link => {
    link.addEventListener('click', (e) => {
      // 处理点击事件
    });
  });
</script>
```

### 8.4 性能检查清单

```yaml
开发阶段:
  - [ ] 图片使用WebP格式
  - [ ] 实现图片懒加载
  - [ ] 关键CSS内联
  - [ ] 非关键CSS异步加载
  - [ ] JavaScript延迟加载
  - [ ] 使用字体子集
  - [ ] 启用Gzip/Brotli压缩

构建阶段:
  - [ ] 代码分割优化
  - [ ] Tree shaking
  - [ ] CSS/JS压缩
  - [ ] 图片自动优化
  - [ ] 生成响应式图片
  - [ ] 预加载关键资源

部署阶段:
  - [ ] CDN配置
  - [ ] 缓存策略设置
  - [ ] HTTP/2启用
  - [ ] SSL证书配置
  - [ ] 安全头部设置
```

## 总结

本前端开发文档详细介绍了基于Astro框架的宠物博客站群系统前端实现方案。通过合理的架构设计、组件开发、SEO优化和性能优化策略，确保系统能够提供优秀的用户体验和搜索引擎排名。

### 关键技术点

1. **Astro SSG**：利用静态生成获得最佳性能
2. **组件化开发**：可复用的组件体系
3. **SEO优化**：全面的搜索引擎优化实施
4. **性能优先**：多层次的性能优化策略
5. **多语言架构**：独立构建的多语言支持

### 下一步计划

1. 搭建开发环境
2. 实现核心组件
3. 集成API服务
4. 性能测试优化
5. 部署上线
