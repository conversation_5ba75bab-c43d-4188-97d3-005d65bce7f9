# 宠物博客站群系统 - 测试文档

## 目录

- [1. 测试策略概述](#1-测试策略概述)
- [2. 单元测试](#2-单元测试)
- [3. 集成测试](#3-集成测试)
- [4. E2E测试](#4-e2e测试)
- [5. 性能测试](#5-性能测试)
- [6. SEO测试](#6-seo测试)
- [7. 安全测试](#7-安全测试)
- [8. 多语言测试](#8-多语言测试)

## 1. 测试策略概述

### 1.1 测试金字塔

```yaml
测试层级:
  单元测试 (70%):
    - 覆盖所有业务逻辑
    - 独立、快速、可重复
    - 目标覆盖率: ≥80%

  集成测试 (20%):
    - API端点测试
    - 数据库交互测试
    - 第三方服务集成
    - 目标覆盖率: ≥70%

  E2E测试 (10%):
    - 关键用户流程
    - 跨浏览器兼容性
    - 真实环境验证
```

### 1.2 测试工具栈

```yaml
后端测试:
  - Jest: 单元测试框架
  - Supertest: API测试
  - TypeORM: 数据库测试

前端测试:
  - Vitest: 单元测试
  - Testing Library: 组件测试
  - Playwright: E2E测试

性能测试:
  - Lighthouse: 页面性能
  - Artillery: 负载测试
  - WebPageTest: 真实用户体验

其他工具:
  - ESLint: 代码质量
  - Prettier: 代码格式
  - Husky: Git钩子
```

### 1.3 持续集成配置

```yaml
# .github/workflows/test.yml
name: Test Suite

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest

    services:
      mysql:
        image: mysql:9.0
        env:
          MYSQL_ROOT_PASSWORD: test
          MYSQL_DATABASE: petcare_test
        options: >-
          --health-cmd="mysqladmin ping" --health-interval=10s --health-timeout=5s
          --health-retries=5

      redis:
        image: redis:7.2
        options: >-
          --health-cmd="redis-cli ping" --health-interval=10s --health-timeout=5s --health-retries=5

    steps:
      - uses: actions/checkout@v3

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run linter
        run: npm run lint

      - name: Run unit tests
        run: npm run test:unit

      - name: Run integration tests
        run: npm run test:integration

      - name: Run E2E tests
        run: npm run test:e2e

      - name: Upload coverage
        uses: codecov/codecov-action@v3
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
```

## 2. 单元测试

### 2.1 后端单元测试

#### 2.1.1 测试配置 (jest.config.js)

```javascript
module.exports = {
  preset: 'ts-jest',
  testEnvironment: 'node',
  roots: ['<rootDir>/src'],
  testMatch: ['**/__tests__/**/*.ts', '**/*.test.ts'],
  collectCoverageFrom: ['src/**/*.ts', '!src/**/*.d.ts', '!src/types/**', '!src/migrations/**'],
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80,
    },
  },
  setupFilesAfterEnv: ['<rootDir>/src/test/setup.ts'],
  moduleNameMapper: {
    '@/(.*)': '<rootDir>/src/$1',
  },
};
```

#### 2.1.2 文章服务测试示例

```typescript
// src/services/__tests__/article.service.test.ts
import { ArticleService } from '../article.service';
import { Article } from '@/entities/article.entity';
import { mockRepository } from '@/test/mocks';

describe('ArticleService', () => {
  let articleService: ArticleService;
  let articleRepository: any;

  beforeEach(() => {
    articleRepository = mockRepository();
    articleService = new ArticleService(articleRepository);
  });

  describe('createArticle', () => {
    it('should create a new article', async () => {
      const articleData = {
        title: '10 Tips for Cat Care',
        content: 'Article content...',
        category_id: 1,
        author_id: 1,
      };

      const expectedArticle = {
        id: 123,
        ...articleData,
        slug: '10-tips-for-cat-care',
        status: 'draft',
        created_at: new Date(),
      };

      articleRepository.save.mockResolvedValue(expectedArticle);

      const result = await articleService.createArticle(articleData);

      expect(result).toEqual(expectedArticle);
      expect(articleRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          title: articleData.title,
          slug: '10-tips-for-cat-care',
        }),
      );
    });

    it('should handle duplicate slug by appending number', async () => {
      const articleData = {
        title: 'Cat Care Guide',
        content: 'Content...',
        category_id: 1,
        author_id: 1,
      };

      articleRepository.findOne
        .mockResolvedValueOnce({ slug: 'cat-care-guide' })
        .mockResolvedValueOnce({ slug: 'cat-care-guide-1' })
        .mockResolvedValueOnce(null);

      await articleService.createArticle(articleData);

      expect(articleRepository.save).toHaveBeenCalledWith(
        expect.objectContaining({
          slug: 'cat-care-guide-2',
        }),
      );
    });

    it('should throw error for invalid category', async () => {
      const articleData = {
        title: 'Test Article',
        content: 'Content...',
        category_id: 999,
        author_id: 1,
      };

      await expect(articleService.createArticle(articleData)).rejects.toThrow('Invalid category');
    });
  });

  describe('publishArticle', () => {
    it('should publish article and update timestamps', async () => {
      const article = {
        id: 123,
        status: 'draft',
        published_at: null,
      };

      articleRepository.findOne.mockResolvedValue(article);
      articleRepository.save.mockResolvedValue({
        ...article,
        status: 'published',
        published_at: expect.any(Date),
      });

      const result = await articleService.publishArticle(123);

      expect(result.status).toBe('published');
      expect(result.published_at).toBeInstanceOf(Date);
    });

    it('should not republish already published article', async () => {
      const article = {
        id: 123,
        status: 'published',
        published_at: new Date('2025-01-01'),
      };

      articleRepository.findOne.mockResolvedValue(article);

      await expect(articleService.publishArticle(123)).rejects.toThrow(
        'Article is already published',
      );
    });
  });
});
```

#### 2.1.3 翻译服务测试

```typescript
// src/services/__tests__/translation.service.test.ts
import { TranslationService } from '../translation.service';
import { AITranslationClient } from '@/lib/ai-translation';

jest.mock('@/lib/ai-translation');

describe('TranslationService', () => {
  let translationService: TranslationService;
  let aiClient: jest.Mocked<AITranslationClient>;

  beforeEach(() => {
    aiClient = new AITranslationClient() as jest.Mocked<AITranslationClient>;
    translationService = new TranslationService(aiClient);
  });

  describe('translateArticle', () => {
    it('should translate article to multiple languages', async () => {
      const article = {
        id: 123,
        title: '猫咪护理指南',
        content: '详细内容...',
      };

      const languages = ['en-US', 'de-DE'];

      aiClient.translate.mockImplementation((text, targetLang) => {
        const translations = {
          'en-US': {
            title: 'Cat Care Guide',
            content: 'Detailed content...',
          },
          'de-DE': {
            title: 'Katzenpflege-Leitfaden',
            content: 'Detaillierter Inhalt...',
          },
        };
        return Promise.resolve(translations[targetLang]);
      });

      const results = await translationService.translateArticle(article, languages);

      expect(results).toHaveLength(2);
      expect(results[0]).toEqual({
        article_id: 123,
        language_code: 'en-US',
        title: 'Cat Care Guide',
        content: 'Detailed content...',
        translation_status: 'ai_translated',
      });

      expect(aiClient.translate).toHaveBeenCalledTimes(4); // title + content for each language
    });

    it('should handle translation API errors gracefully', async () => {
      const article = {
        id: 123,
        title: 'Test',
        content: 'Test content',
      };

      aiClient.translate.mockRejectedValue(new Error('API Error'));

      const results = await translationService.translateArticle(article, ['en-US']);

      expect(results[0]).toEqual({
        article_id: 123,
        language_code: 'en-US',
        translation_status: 'failed',
        error: 'Translation failed: API Error',
      });
    });
  });
});
```

### 2.2 前端单元测试

#### 2.2.1 组件测试示例

```typescript
// src/components/__tests__/ArticleCard.test.ts
import { experimental_AstroContainer as AstroContainer } from 'astro/container';
import { expect, test } from 'vitest';
import ArticleCard from '../ArticleCard.astro';

test('ArticleCard renders article information correctly', async () => {
  const container = await AstroContainer.create();
  const article = {
    id: 123,
    title: 'Test Article',
    slug: 'test-article',
    summary: 'This is a test summary',
    featured_image: '/images/test.jpg',
    category: {
      name: 'Test Category',
      slug: 'test-category',
    },
    published_at: '2025-01-20T10:00:00Z',
    view_count: 100,
    comment_count: 5,
  };

  const result = await container.renderToString(ArticleCard, {
    props: { article },
  });

  expect(result).toContain('Test Article');
  expect(result).toContain('This is a test summary');
  expect(result).toContain('Test Category');
  expect(result).toContain('100'); // view count
  expect(result).toContain('5'); // comment count
});

test('ArticleCard generates correct URLs', async () => {
  const container = await AstroContainer.create();
  const article = {
    id: 123,
    title: 'URL Test',
    slug: 'url-test',
    summary: 'Summary',
    category: {
      name: 'Category',
      slug: 'test-cat',
    },
    published_at: '2025-01-20T10:00:00Z',
    view_count: 0,
    comment_count: 0,
  };

  const result = await container.renderToString(ArticleCard, {
    props: { article },
  });

  expect(result).toContain('href="/url-test"');
  expect(result).toContain('href="/category/test-cat"');
});
```

#### 2.2.2 工具函数测试

```typescript
// src/utils/__tests__/seo.test.ts
import { describe, it, expect } from 'vitest';
import { generateSlug, generateCanonicalUrl } from '../seo';

describe('SEO Utils', () => {
  describe('generateSlug', () => {
    it('should generate English slugs correctly', () => {
      expect(generateSlug('10 Tips for Cat Care', 'en-US')).toBe('10-tips-for-cat-care');

      expect(generateSlug('Cat & Dog Care!', 'en-US')).toBe('cat-dog-care');
    });

    it('should handle German special characters', () => {
      expect(generateSlug('Süße Kätzchen Pflege', 'de-DE')).toBe('suesse-kaetzchen-pflege');

      expect(generateSlug('Große Hunde', 'de-DE')).toBe('grosse-hunde');
    });

    it('should transliterate Russian text', () => {
      expect(generateSlug('Уход за кошками', 'ru-RU')).toBe('uhod-za-koshkami');
    });
  });

  describe('generateCanonicalUrl', () => {
    it('should generate correct canonical URLs', () => {
      expect(generateCanonicalUrl('cat-care', 'en-US')).toBe('https://www.petcare.com/cat-care');

      expect(generateCanonicalUrl('katzenpflege', 'de-DE')).toBe(
        'https://www.haustiere.de/katzenpflege',
      );
    });
  });
});
```

## 3. 集成测试

### 3.1 API集成测试

#### 3.1.1 认证API测试

```typescript
// src/__tests__/integration/auth.test.ts
import request from 'supertest';
import { app } from '@/app';
import { setupTestDatabase, clearTestDatabase } from '@/test/database';

describe('Auth API Integration Tests', () => {
  beforeAll(async () => {
    await setupTestDatabase();
  });

  afterAll(async () => {
    await clearTestDatabase();
  });

  describe('POST /api/v1/auth/login', () => {
    it('should login with valid credentials', async () => {
      const response = await request(app).post('/api/v1/auth/login').send({
        email: '<EMAIL>',
        password: 'correct_password',
      });

      expect(response.status).toBe(200);
      expect(response.body).toHaveProperty('success', true);
      expect(response.body.data).toHaveProperty('access_token');
      expect(response.body.data).toHaveProperty('refresh_token');
      expect(response.body.data.user).toEqual({
        id: expect.any(Number),
        username: 'admin',
        email: '<EMAIL>',
        role: 'admin',
      });
    });

    it('should reject invalid credentials', async () => {
      const response = await request(app).post('/api/v1/auth/login').send({
        email: '<EMAIL>',
        password: 'wrong_password',
      });

      expect(response.status).toBe(401);
      expect(response.body).toHaveProperty('success', false);
      expect(response.body.error.code).toBe('AUTH_FAILED');
    });

    it('should handle rate limiting', async () => {
      // Make 5 failed login attempts
      for (let i = 0; i < 5; i++) {
        await request(app).post('/api/v1/auth/login').send({
          email: '<EMAIL>',
          password: 'wrong',
        });
      }

      // 6th attempt should be rate limited
      const response = await request(app).post('/api/v1/auth/login').send({
        email: '<EMAIL>',
        password: 'wrong',
      });

      expect(response.status).toBe(429);
      expect(response.body.error.code).toBe('RATE_LIMIT_EXCEEDED');
    });
  });

  describe('POST /api/v1/auth/refresh', () => {
    let refreshToken: string;

    beforeEach(async () => {
      const loginResponse = await request(app).post('/api/v1/auth/login').send({
        email: '<EMAIL>',
        password: 'correct_password',
      });

      refreshToken = loginResponse.body.data.refresh_token;
    });

    it('should refresh access token with valid refresh token', async () => {
      const response = await request(app)
        .post('/api/v1/auth/refresh')
        .send({ refresh_token: refreshToken });

      expect(response.status).toBe(200);
      expect(response.body.data).toHaveProperty('access_token');
      expect(response.body.data.access_token).not.toBe(refreshToken);
    });

    it('should reject invalid refresh token', async () => {
      const response = await request(app)
        .post('/api/v1/auth/refresh')
        .send({ refresh_token: 'invalid_token' });

      expect(response.status).toBe(401);
      expect(response.body.error.code).toBe('TOKEN_INVALID');
    });
  });
});
```

#### 3.1.2 文章API测试

```typescript
// src/__tests__/integration/articles.test.ts
import request from 'supertest';
import { app } from '@/app';
import { getAuthToken } from '@/test/auth';

describe('Articles API Integration Tests', () => {
  let authToken: string;

  beforeAll(async () => {
    authToken = await getAuthToken('admin');
  });

  describe('GET /api/v1/articles', () => {
    it('should return paginated article list', async () => {
      const response = await request(app)
        .get('/api/v1/articles')
        .query({
          page: 1,
          per_page: 10,
          status: 'published',
          language_code: 'en-US',
        })
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      expect(response.body.data).toBeInstanceOf(Array);
      expect(response.body.pagination).toEqual({
        total: expect.any(Number),
        per_page: 10,
        current_page: 1,
        total_pages: expect.any(Number),
        has_next: expect.any(Boolean),
        has_prev: false,
      });

      // Verify article structure
      if (response.body.data.length > 0) {
        const article = response.body.data[0];
        expect(article).toHaveProperty('id');
        expect(article).toHaveProperty('title');
        expect(article).toHaveProperty('slug');
        expect(article).toHaveProperty('category');
        expect(article).toHaveProperty('published_at');
      }
    });

    it('should filter articles by category', async () => {
      const response = await request(app)
        .get('/api/v1/articles')
        .query({
          category_id: 3, // Cat Care category
          status: 'published',
        })
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      response.body.data.forEach((article: any) => {
        expect(article.category.id).toBe(3);
      });
    });

    it('should search articles by keyword', async () => {
      const response = await request(app)
        .get('/api/v1/articles')
        .query({
          search: 'cat',
          status: 'published',
        })
        .set('Authorization', `Bearer ${authToken}`);

      expect(response.status).toBe(200);
      response.body.data.forEach((article: any) => {
        const hasKeyword =
          article.title.toLowerCase().includes('cat') ||
          article.summary.toLowerCase().includes('cat');
        expect(hasKeyword).toBe(true);
      });
    });
  });

  describe('POST /api/v1/articles', () => {
    it('should create new article', async () => {
      const articleData = {
        category_id: 3,
        title: 'Integration Test Article',
        content: '<p>This is test content</p>',
        summary: 'Test summary',
        meta_title: 'Test Meta Title',
        meta_description: 'Test meta description',
        tags: ['test', 'integration'],
        status: 'draft',
        language_code: 'zh-CN',
      };

      const response = await request(app)
        .post('/api/v1/articles')
        .set('Authorization', `Bearer ${authToken}`)
        .send(articleData);

      expect(response.status).toBe(201);
      expect(response.body.data).toMatchObject({
        title: articleData.title,
        slug: 'integration-test-article',
        status: 'draft',
      });
      expect(response.body.data.id).toBeDefined();
    });

    it('should validate required fields', async () => {
      const response = await request(app)
        .post('/api/v1/articles')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          // Missing required fields
          summary: 'Only summary',
        });

      expect(response.status).toBe(422);
      expect(response.body.error.code).toBe('VALIDATION_ERROR');
      expect(response.body.error.details).toContainEqual(
        expect.objectContaining({
          field: 'title',
          code: 'required',
        }),
      );
    });
  });

  describe('PUT /api/v1/articles/:id', () => {
    let articleId: number;

    beforeEach(async () => {
      const createResponse = await request(app)
        .post('/api/v1/articles')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          category_id: 3,
          title: 'Article to Update',
          content: 'Original content',
          status: 'draft',
        });

      articleId = createResponse.body.data.id;
    });

    it('should update article successfully', async () => {
      const response = await request(app)
        .put(`/api/v1/articles/${articleId}`)
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          title: 'Updated Title',
          content: 'Updated content',
          status: 'published',
        });

      expect(response.status).toBe(200);
      expect(response.body.data).toMatchObject({
        id: articleId,
        title: 'Updated Title',
        status: 'published',
      });
    });

    it('should handle non-existent article', async () => {
      const response = await request(app)
        .put('/api/v1/articles/99999')
        .set('Authorization', `Bearer ${authToken}`)
        .send({
          title: 'Updated Title',
        });

      expect(response.status).toBe(404);
      expect(response.body.error.code).toBe('RESOURCE_NOT_FOUND');
    });
  });
});
```

### 3.2 数据库集成测试

```typescript
// src/__tests__/integration/database.test.ts
import { DataSource } from 'typeorm';
import { Article } from '@/entities/article.entity';
import { Category } from '@/entities/category.entity';
import { Comment } from '@/entities/comment.entity';
import { createTestDataSource } from '@/test/database';

describe('Database Integration Tests', () => {
  let dataSource: DataSource;

  beforeAll(async () => {
    dataSource = await createTestDataSource();
  });

  afterAll(async () => {
    await dataSource.destroy();
  });

  describe('Article-Category Relationship', () => {
    it('should maintain referential integrity', async () => {
      const categoryRepo = dataSource.getRepository(Category);
      const articleRepo = dataSource.getRepository(Article);

      // Create category
      const category = await categoryRepo.save({
        slug: 'test-category',
        sort_order: 1,
      });

      // Create article with category
      const article = await articleRepo.save({
        title: 'Test Article',
        slug: 'test-article',
        category_id: category.id,
        author_id: 1,
        status: 'draft',
      });

      // Load article with category
      const loadedArticle = await articleRepo.findOne({
        where: { id: article.id },
        relations: ['category'],
      });

      expect(loadedArticle?.category.id).toBe(category.id);

      // Test cascade delete prevention
      await expect(categoryRepo.delete(category.id)).rejects.toThrow(/foreign key constraint/);
    });
  });

  describe('Comment Nesting', () => {
    it('should support nested comment structure', async () => {
      const commentRepo = dataSource.getRepository(Comment);

      // Create parent comment
      const parentComment = await commentRepo.save({
        article_id: 1,
        language_code: 'en-US',
        author_name: 'John',
        author_email: '<EMAIL>',
        content: 'Parent comment',
        status: 'approved',
        ip_address: '127.0.0.1',
      });

      // Create child comments
      const childComment1 = await commentRepo.save({
        article_id: 1,
        parent_id: parentComment.id,
        language_code: 'en-US',
        author_name: 'Jane',
        author_email: '<EMAIL>',
        content: 'First reply',
        status: 'approved',
        ip_address: '127.0.0.1',
      });

      const childComment2 = await commentRepo.save({
        article_id: 1,
        parent_id: parentComment.id,
        language_code: 'en-US',
        author_name: 'Bob',
        author_email: '<EMAIL>',
        content: 'Second reply',
        status: 'approved',
        ip_address: '127.0.0.1',
      });

      // Query comment tree
      const comments = await commentRepo
        .createQueryBuilder('comment')
        .where('comment.article_id = :articleId', { articleId: 1 })
        .andWhere('comment.parent_id IS NULL')
        .leftJoinAndSelect('comment.replies', 'reply')
        .getMany();

      expect(comments).toHaveLength(1);
      expect(comments[0].replies).toHaveLength(2);
      expect(comments[0].replies.map((r) => r.author_name)).toEqual(['Jane', 'Bob']);
    });
  });
});
```

## 4. E2E测试

### 4.1 Playwright配置

```typescript
// playwright.config.ts
import { defineConfig, devices } from '@playwright/test';

export default defineConfig({
  testDir: './e2e',
  timeout: 30 * 1000,
  expect: {
    timeout: 5000,
  },
  fullyParallel: true,
  forbidOnly: !!process.env.CI,
  retries: process.env.CI ? 2 : 0,
  workers: process.env.CI ? 1 : undefined,
  reporter: 'html',
  use: {
    baseURL: 'http://localhost:3000',
    trace: 'on-first-retry',
    screenshot: 'only-on-failure',
  },

  projects: [
    {
      name: 'chromium',
      use: { ...devices['Desktop Chrome'] },
    },
    {
      name: 'firefox',
      use: { ...devices['Desktop Firefox'] },
    },
    {
      name: 'webkit',
      use: { ...devices['Desktop Safari'] },
    },
    {
      name: 'Mobile Chrome',
      use: { ...devices['Pixel 5'] },
    },
    {
      name: 'Mobile Safari',
      use: { ...devices['iPhone 12'] },
    },
  ],

  webServer: {
    command: 'npm run dev',
    port: 3000,
    reuseExistingServer: !process.env.CI,
  },
});
```

### 4.2 E2E测试用例

#### 4.2.1 用户浏览文章流程

```typescript
// e2e/browse-articles.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Browse Articles Flow', () => {
  test('should navigate from homepage to article detail', async ({ page }) => {
    // 访问首页
    await page.goto('/');

    // 验证首页加载
    await expect(page).toHaveTitle(/PetCare/);
    await expect(page.locator('h1')).toContainText('Welcome to PetCare');

    // 点击第一篇文章
    const firstArticle = page.locator('.article-card').first();
    const articleTitle = await firstArticle.locator('h3').textContent();
    await firstArticle.click();

    // 验证文章详情页
    await expect(page).toHaveURL(/\/[\w-]+$/);
    await expect(page.locator('h1')).toContainText(articleTitle!);

    // 验证文章元素
    await expect(page.locator('.article-content')).toBeVisible();
    await expect(page.locator('.article-meta')).toBeVisible();
    await expect(page.locator('.related-articles')).toBeVisible();

    // 验证面包屑导航
    const breadcrumb = page.locator('.breadcrumb');
    await expect(breadcrumb).toContainText('Home');
    await expect(breadcrumb).toContainText('Cat Care');
  });

  test('should filter articles by category', async ({ page }) => {
    await page.goto('/');

    // 点击分类导航
    await page.locator('nav').getByText('Cats').click();

    // 验证URL和标题
    await expect(page).toHaveURL('/category/cats');
    await expect(page.locator('h1')).toContainText('Cats');

    // 验证所有文章都属于该分类
    const articles = page.locator('.article-card');
    const count = await articles.count();

    for (let i = 0; i < count; i++) {
      const categoryTag = articles.nth(i).locator('.category-tag');
      await expect(categoryTag).toContainText(/Cat/);
    }

    // 测试子分类
    await page.getByText('Cat Care').click();
    await expect(page).toHaveURL('/category/cat-care');
    await expect(page.locator('h1')).toContainText('Cat Care');
  });

  test('should handle pagination', async ({ page }) => {
    await page.goto('/category/cats');

    // 检查分页组件
    const pagination = page.locator('.pagination');
    await expect(pagination).toBeVisible();

    // 如果有下一页，测试分页
    const nextButton = pagination.getByText('Next');
    if (await nextButton.isVisible()) {
      await nextButton.click();
      await expect(page).toHaveURL(/page=2/);

      // 验证新文章加载
      await expect(page.locator('.article-card').first()).toBeVisible();

      // 测试返回上一页
      await pagination.getByText('Previous').click();
      await expect(page).toHaveURL(/page=1|category\/cats$/);
    }
  });
});
```

#### 4.2.2 评论提交流程

```typescript
// e2e/submit-comment.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Comment Submission', () => {
  test('should submit and display comment after approval', async ({ page }) => {
    // 导航到文章页
    await page.goto('/10-tips-for-cat-care');

    // 滚动到评论区
    await page.locator('#comments').scrollIntoViewIfNeeded();

    // 填写评论表单
    await page.fill('input[name="author_name"]', 'Test User');
    await page.fill('input[name="author_email"]', '<EMAIL>');
    await page.fill('textarea[name="content"]', 'This is a great article! Very helpful tips.');

    // 提交评论
    await page.click('button[type="submit"]');

    // 验证提交成功消息
    await expect(page.locator('.alert-success')).toContainText(
      'Your comment has been submitted and is awaiting moderation',
    );

    // 验证表单被清空
    await expect(page.locator('textarea[name="content"]')).toHaveValue('');

    // 模拟管理员审核（在实际测试中，这可能需要API调用）
    // await approveLatestComment(page);

    // 刷新页面查看评论
    // await page.reload();
    // await expect(page.locator('.comment-content'))
    //   .toContainText('This is a great article! Very helpful tips.');
  });

  test('should validate comment form', async ({ page }) => {
    await page.goto('/10-tips-for-cat-care');
    await page.locator('#comments').scrollIntoViewIfNeeded();

    // 尝试提交空表单
    await page.click('button[type="submit"]');

    // 验证错误消息
    await expect(page.locator('.field-error')).toContainText('Name is required');
    await expect(page.locator('.field-error')).toContainText('Email is required');
    await expect(page.locator('.field-error')).toContainText('Comment is required');

    // 测试邮箱格式验证
    await page.fill('input[name="author_name"]', 'Test');
    await page.fill('input[name="author_email"]', 'invalid-email');
    await page.fill('textarea[name="content"]', 'Test comment');
    await page.click('button[type="submit"]');

    await expect(page.locator('.field-error')).toContainText('Please enter a valid email address');
  });

  test('should handle nested comments', async ({ page }) => {
    await page.goto('/10-tips-for-cat-care');

    // 找到一个已存在的评论
    const firstComment = page.locator('.comment').first();

    // 点击回复按钮
    await firstComment.getByText('Reply').click();

    // 验证回复表单显示
    const replyForm = firstComment.locator('.reply-form');
    await expect(replyForm).toBeVisible();

    // 填写回复
    await replyForm.fill('input[name="author_name"]', 'Reply User');
    await replyForm.fill('input[name="author_email"]', '<EMAIL>');
    await replyForm.fill('textarea[name="content"]', 'Thanks for your comment!');

    // 提交回复
    await replyForm.locator('button[type="submit"]').click();

    // 验证成功消息
    await expect(page.locator('.alert-success')).toContainText('Your reply has been submitted');
  });
});
```

#### 4.2.3 搜索功能测试

```typescript
// e2e/search.spec.ts
import { test, expect } from '@playwright/test';

test.describe('Search Functionality', () => {
  test('should search articles successfully', async ({ page }) => {
    await page.goto('/');

    // 使用搜索框
    const searchBox = page.locator('input[type="search"]');
    await searchBox.fill('cat feeding');
    await searchBox.press('Enter');

    // 验证搜索结果页
    await expect(page).toHaveURL('/search?q=cat+feeding');
    await expect(page.locator('h1')).toContainText('Search Results');
    await expect(page.locator('.search-query')).toContainText('cat feeding');

    // 验证搜索结果
    const results = page.locator('.article-card');
    const count = await results.count();

    if (count > 0) {
      // 验证结果相关性
      for (let i = 0; i < Math.min(count, 3); i++) {
        const article = results.nth(i);
        const title = await article.locator('h3').textContent();
        const summary = await article.locator('.summary').textContent();

        const hasKeyword =
          title?.toLowerCase().includes('cat') ||
          title?.toLowerCase().includes('feeding') ||
          summary?.toLowerCase().includes('cat') ||
          summary?.toLowerCase().includes('feeding');

        expect(hasKeyword).toBeTruthy();
      }
    } else {
      // 验证无结果消息
      await expect(page.locator('.no-results')).toContainText('No articles found');
    }
  });

  test('should show search suggestions', async ({ page }) => {
    await page.goto('/');

    const searchBox = page.locator('input[type="search"]');
    await searchBox.click();
    await searchBox.fill('cat');

    // 等待建议出现
    await page.waitForSelector('.search-suggestions');

    const suggestions = page.locator('.search-suggestion');
    expect(await suggestions.count()).toBeGreaterThan(0);

    // 点击建议
    await suggestions.first().click();

    // 验证导航到相应页面
    await expect(page).toHaveURL(/\/([\w-]+|search\?q=)/);
  });
});
```

## 5. 性能测试

### 5.1 Lighthouse测试脚本

```javascript
// scripts/lighthouse-test.js
const lighthouse = require('lighthouse');
const chromeLauncher = require('chrome-launcher');
const fs = require('fs');

async function runLighthouse(url, options = {}) {
  const chrome = await chromeLauncher.launch({ chromeFlags: ['--headless'] });

  const defaultOptions = {
    logLevel: 'info',
    output: 'json',
    port: chrome.port,
    onlyCategories: ['performance', 'accessibility', 'best-practices', 'seo'],
  };

  const runnerResult = await lighthouse(url, { ...defaultOptions, ...options });

  await chrome.kill();

  return runnerResult;
}

async function testAllPages() {
  const urls = [
    'http://localhost:3000/',
    'http://localhost:3000/category/cats',
    'http://localhost:3000/10-tips-for-cat-care',
    'http://localhost:3000/search?q=cat',
  ];

  const results = {};

  for (const url of urls) {
    console.log(`Testing ${url}...`);
    const result = await runLighthouse(url);

    const { categories } = result.lhr;
    results[url] = {
      performance: categories.performance.score * 100,
      accessibility: categories.accessibility.score * 100,
      bestPractices: categories['best-practices'].score * 100,
      seo: categories.seo.score * 100,
    };

    // 检查是否达到目标分数
    if (categories.performance.score < 0.9) {
      console.warn(`⚠️  Performance score below 90: ${categories.performance.score * 100}`);
    }
  }

  // 保存结果
  fs.writeFileSync('lighthouse-results.json', JSON.stringify(results, null, 2));

  console.log('Lighthouse tests completed!');
  console.table(results);
}

testAllPages().catch(console.error);
```

### 5.2 负载测试配置

```yaml
# artillery-config.yml
config:
  target: 'http://localhost:3000'
  phases:
    - duration: 60
      arrivalRate: 10
      name: 'Warm up'
    - duration: 300
      arrivalRate: 50
      name: 'Sustained load'
    - duration: 60
      arrivalRate: 100
      name: 'Peak load'
  processor: './artillery-processor.js'

scenarios:
  - name: 'Browse articles'
    weight: 60
    flow:
      - get:
          url: '/'
      - think: 3
      - get:
          url: '/category/cats'
      - think: 2
      - get:
          url: '/{{ randomArticleSlug }}'

  - name: 'Search articles'
    weight: 30
    flow:
      - get:
          url: '/search?q={{ randomSearchTerm }}'
      - think: 2

  - name: 'Submit comment'
    weight: 10
    flow:
      - get:
          url: '/{{ randomArticleSlug }}'
      - think: 5
      - post:
          url: '/api/v1/articles/{{ articleId }}/comments'
          json:
            author_name: '{{ $randomString() }}'
            author_email: '{{ $randomEmail() }}'
            content: '{{ $randomWords(20) }}'
```

### 5.3 性能基准测试

```javascript
// benchmarks/api-performance.js
const autocannon = require('autocannon');

async function runBenchmark() {
  const scenarios = [
    {
      name: 'GET /api/v1/articles',
      url: 'http://localhost:3000/api/v1/articles?page=1&per_page=20',
      connections: 100,
      duration: 30,
    },
    {
      name: 'GET /api/v1/articles/:id',
      url: 'http://localhost:3000/api/v1/articles/123',
      connections: 100,
      duration: 30,
    },
    {
      name: 'POST /api/v1/auth/login',
      url: 'http://localhost:3000/api/v1/auth/login',
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        email: '<EMAIL>',
        password: 'password123',
      }),
      connections: 50,
      duration: 30,
    },
  ];

  for (const scenario of scenarios) {
    console.log(`\nRunning benchmark: ${scenario.name}`);

    const result = await autocannon(scenario);

    console.log(`Requests/sec: ${result.requests.average}`);
    console.log(`Latency (ms): ${result.latency.average}`);
    console.log(`Throughput (MB/s): ${(result.throughput.average / 1024 / 1024).toFixed(2)}`);

    // 验证性能基准
    if (result.latency.average > 200) {
      console.warn('⚠️  Average latency exceeds 200ms');
    }

    if (result.requests.average < 1000) {
      console.warn('⚠️  Requests/sec below 1000');
    }
  }
}

runBenchmark().catch(console.error);
```

## 6. SEO测试

### 6.1 SEO验证脚本

```javascript
// scripts/seo-test.js
const puppeteer = require('puppeteer');
const { XMLParser } = require('fast-xml-parser');
const axios = require('axios');

async function testSEO() {
  const browser = await puppeteer.launch();
  const page = await browser.newPage();

  const tests = {
    homepage: 'http://localhost:3000/',
    article: 'http://localhost:3000/10-tips-for-cat-care',
    category: 'http://localhost:3000/category/cats',
  };

  const results = {};

  for (const [name, url] of Object.entries(tests)) {
    await page.goto(url);

    const seoData = await page.evaluate(() => {
      const getMetaContent = (name) => {
        const el = document.querySelector(`meta[name="${name}"], meta[property="${name}"]`);
        return el ? el.content : null;
      };

      return {
        title: document.title,
        description: getMetaContent('description'),
        keywords: getMetaContent('keywords'),
        canonical: document.querySelector('link[rel="canonical"]')?.href,
        ogTitle: getMetaContent('og:title'),
        ogDescription: getMetaContent('og:description'),
        ogImage: getMetaContent('og:image'),
        ogUrl: getMetaContent('og:url'),
        twitterCard: getMetaContent('twitter:card'),
        robots: getMetaContent('robots'),
        structuredData: Array.from(
          document.querySelectorAll('script[type="application/ld+json"]'),
        ).map((el) => JSON.parse(el.textContent)),
        h1: document.querySelector('h1')?.textContent,
        altTags: Array.from(document.querySelectorAll('img:not([alt])')).length,
      };
    });

    results[name] = {
      ...seoData,
      issues: [],
    };

    // 验证SEO要求
    if (!seoData.title || seoData.title.length > 60) {
      results[name].issues.push('Title missing or too long');
    }

    if (!seoData.description || seoData.description.length > 160) {
      results[name].issues.push('Description missing or too long');
    }

    if (!seoData.canonical) {
      results[name].issues.push('Canonical URL missing');
    }

    if (!seoData.ogImage) {
      results[name].issues.push('OG image missing');
    }

    if (seoData.altTags > 0) {
      results[name].issues.push(`${seoData.altTags} images missing alt tags`);
    }

    if (!seoData.structuredData || seoData.structuredData.length === 0) {
      results[name].issues.push('No structured data found');
    }
  }

  // 测试sitemap
  try {
    const sitemapResponse = await axios.get('http://localhost:3000/sitemap.xml');
    const parser = new XMLParser();
    const sitemap = parser.parse(sitemapResponse.data);

    results.sitemap = {
      urlCount: sitemap.urlset.url.length,
      valid: true,
    };
  } catch (error) {
    results.sitemap = {
      valid: false,
      error: error.message,
    };
  }

  // 测试robots.txt
  try {
    const robotsResponse = await axios.get('http://localhost:3000/robots.txt');
    results.robots = {
      valid: robotsResponse.status === 200,
      content: robotsResponse.data.substring(0, 200),
    };
  } catch (error) {
    results.robots = {
      valid: false,
      error: error.message,
    };
  }

  await browser.close();

  // 输出结果
  console.log('SEO Test Results:');
  console.log(JSON.stringify(results, null, 2));

  // 检查是否有严重问题
  let hasIssues = false;
  for (const [page, data] of Object.entries(results)) {
    if (data.issues && data.issues.length > 0) {
      console.error(`\n❌ ${page} has SEO issues:`);
      data.issues.forEach((issue) => console.error(`   - ${issue}`));
      hasIssues = true;
    }
  }

  if (!hasIssues) {
    console.log('\n✅ All SEO tests passed!');
  }

  return results;
}

testSEO().catch(console.error);
```

### 6.2 Core Web Vitals测试

```javascript
// scripts/core-web-vitals.js
const { chromium } = require('playwright');

async function measureCoreWebVitals(url) {
  const browser = await chromium.launch();
  const page = await browser.newPage();

  // 注入Web Vitals库
  await page.addInitScript({
    path: require.resolve('web-vitals/dist/web-vitals.iife.js'),
  });

  const vitals = {};

  // 监听Web Vitals事件
  await page.exposeFunction('onVitals', (metric) => {
    vitals[metric.name] = {
      value: metric.value,
      rating: metric.rating,
    };
  });

  await page.evaluate(() => {
    webVitals.getCLS(onVitals);
    webVitals.getFID(onVitals);
    webVitals.getLCP(onVitals);
    webVitals.getFCP(onVitals);
    webVitals.getTTFB(onVitals);
  });

  // 访问页面并等待加载
  await page.goto(url, { waitUntil: 'networkidle' });

  // 模拟用户交互以获取FID
  await page.click('body');

  // 等待所有指标收集
  await page.waitForTimeout(5000);

  await browser.close();

  return vitals;
}

async function testAllPages() {
  const urls = [
    'http://localhost:3000/',
    'http://localhost:3000/category/cats',
    'http://localhost:3000/10-tips-for-cat-care',
  ];

  console.log('Core Web Vitals Test Results:\n');

  for (const url of urls) {
    console.log(`Testing: ${url}`);
    const vitals = await measureCoreWebVitals(url);

    console.table(vitals);

    // 验证性能目标
    if (vitals.LCP && vitals.LCP.value > 2500) {
      console.warn('⚠️  LCP exceeds 2.5s');
    }

    if (vitals.FID && vitals.FID.value > 100) {
      console.warn('⚠️  FID exceeds 100ms');
    }

    if (vitals.CLS && vitals.CLS.value > 0.1) {
      console.warn('⚠️  CLS exceeds 0.1');
    }

    console.log('\n');
  }
}

testAllPages().catch(console.error);
```

## 7. 安全测试

### 7.1 安全测试套件

```javascript
// security/security-tests.js
const axios = require('axios');
const { JSDOM } = require('jsdom');

class SecurityTester {
  constructor(baseURL) {
    this.baseURL = baseURL;
    this.results = [];
  }

  async runAllTests() {
    console.log('Running security tests...\n');

    await this.testSQLInjection();
    await this.testXSS();
    await this.testCSRF();
    await this.testSecurityHeaders();
    await this.testAuthentication();
    await this.testRateLimiting();

    this.printResults();
  }

  async testSQLInjection() {
    console.log('Testing SQL Injection...');

    const payloads = [
      "' OR '1'='1",
      '1; DROP TABLE users--',
      "1' UNION SELECT * FROM users--",
      "admin'--",
    ];

    for (const payload of payloads) {
      try {
        // 测试搜索接口
        const response = await axios.get(`${this.baseURL}/api/v1/articles`, {
          params: { search: payload },
          validateStatus: () => true,
        });

        if (response.status !== 400 && response.status !== 422) {
          this.results.push({
            test: 'SQL Injection',
            status: 'FAIL',
            message: `Potential vulnerability with payload: ${payload}`,
          });
        }
      } catch (error) {
        // Expected behavior
      }
    }

    this.results.push({
      test: 'SQL Injection',
      status: 'PASS',
      message: 'No SQL injection vulnerabilities detected',
    });
  }

  async testXSS() {
    console.log('Testing XSS...');

    const xssPayloads = [
      '<script>alert("XSS")</script>',
      '<img src=x onerror=alert("XSS")>',
      'javascript:alert("XSS")',
      '<svg onload=alert("XSS")>',
    ];

    let vulnerabilityFound = false;

    for (const payload of xssPayloads) {
      try {
        // 测试评论提交
        const response = await axios.post(
          `${this.baseURL}/api/v1/articles/1/comments`,
          {
            author_name: payload,
            author_email: '<EMAIL>',
            content: payload,
          },
          { validateStatus: () => true },
        );

        // 检查响应中是否包含未转义的payload
        if (response.data && JSON.stringify(response.data).includes(payload)) {
          vulnerabilityFound = true;
          this.results.push({
            test: 'XSS',
            status: 'FAIL',
            message: `Potential XSS vulnerability with payload: ${payload}`,
          });
        }
      } catch (error) {
        // Expected
      }
    }

    if (!vulnerabilityFound) {
      this.results.push({
        test: 'XSS',
        status: 'PASS',
        message: 'No XSS vulnerabilities detected',
      });
    }
  }

  async testCSRF() {
    console.log('Testing CSRF protection...');

    try {
      // 尝试不带CSRF token的请求
      const response = await axios.post(
        `${this.baseURL}/api/v1/articles`,
        { title: 'Test', content: 'Test' },
        {
          headers: {
            Origin: 'http://evil.com',
          },
          validateStatus: () => true,
        },
      );

      if (response.status === 201) {
        this.results.push({
          test: 'CSRF',
          status: 'FAIL',
          message: 'API accepts cross-origin requests without CSRF protection',
        });
      } else {
        this.results.push({
          test: 'CSRF',
          status: 'PASS',
          message: 'CSRF protection is active',
        });
      }
    } catch (error) {
      this.results.push({
        test: 'CSRF',
        status: 'PASS',
        message: 'CSRF protection is active',
      });
    }
  }

  async testSecurityHeaders() {
    console.log('Testing security headers...');

    try {
      const response = await axios.get(this.baseURL);
      const headers = response.headers;

      const requiredHeaders = [
        'strict-transport-security',
        'x-content-type-options',
        'x-frame-options',
        'x-xss-protection',
      ];

      const missingHeaders = requiredHeaders.filter((header) => !headers[header]);

      if (missingHeaders.length > 0) {
        this.results.push({
          test: 'Security Headers',
          status: 'FAIL',
          message: `Missing headers: ${missingHeaders.join(', ')}`,
        });
      } else {
        this.results.push({
          test: 'Security Headers',
          status: 'PASS',
          message: 'All required security headers present',
        });
      }
    } catch (error) {
      this.results.push({
        test: 'Security Headers',
        status: 'ERROR',
        message: error.message,
      });
    }
  }

  async testAuthentication() {
    console.log('Testing authentication...');

    // 测试未授权访问
    try {
      const response = await axios.get(`${this.baseURL}/api/v1/admin/users`, {
        validateStatus: () => true,
      });

      if (response.status !== 401) {
        this.results.push({
          test: 'Authentication',
          status: 'FAIL',
          message: 'Admin endpoints accessible without authentication',
        });
      } else {
        this.results.push({
          test: 'Authentication',
          status: 'PASS',
          message: 'Authentication properly enforced',
        });
      }
    } catch (error) {
      this.results.push({
        test: 'Authentication',
        status: 'ERROR',
        message: error.message,
      });
    }
  }

  async testRateLimiting() {
    console.log('Testing rate limiting...');

    const requests = [];
    for (let i = 0; i < 10; i++) {
      requests.push(
        axios.post(
          `${this.baseURL}/api/v1/auth/login`,
          { email: '<EMAIL>', password: 'wrong' },
          { validateStatus: () => true },
        ),
      );
    }

    const responses = await Promise.all(requests);
    const rateLimited = responses.some((r) => r.status === 429);

    if (rateLimited) {
      this.results.push({
        test: 'Rate Limiting',
        status: 'PASS',
        message: 'Rate limiting is active',
      });
    } else {
      this.results.push({
        test: 'Rate Limiting',
        status: 'FAIL',
        message: 'No rate limiting detected on sensitive endpoints',
      });
    }
  }

  printResults() {
    console.log('\n=== Security Test Results ===\n');

    const passed = this.results.filter((r) => r.status === 'PASS').length;
    const failed = this.results.filter((r) => r.status === 'FAIL').length;

    this.results.forEach((result) => {
      const icon = result.status === 'PASS' ? '✅' : '❌';
      console.log(`${icon} ${result.test}: ${result.message}`);
    });

    console.log(`\nSummary: ${passed} passed, ${failed} failed`);

    if (failed > 0) {
      process.exit(1);
    }
  }
}

// 运行测试
const tester = new SecurityTester('http://localhost:3000');
tester.runAllTests().catch(console.error);
```

## 8. 多语言测试

### 8.1 多语言内容测试

```typescript
// e2e/multilanguage.spec.ts
import { test, expect } from '@playwright/test';

const languages = [
  { code: 'en-US', url: 'http://www.petcare.com', title: 'PetCare' },
  { code: 'de-DE', url: 'http://www.haustiere.de', title: 'Haustiere' },
  { code: 'ru-RU', url: 'http://www.домашние-животные.рф', title: 'Домашние животные' },
];

test.describe('Multilanguage Support', () => {
  languages.forEach(({ code, url, title }) => {
    test(`should display content in ${code}`, async ({ page }) => {
      await page.goto(url);

      // 验证页面标题
      await expect(page).toHaveTitle(new RegExp(title));

      // 验证语言属性
      const htmlLang = await page.getAttribute('html', 'lang');
      expect(htmlLang).toBe(code);

      // 验证内容语言
      const heading = await page.locator('h1').textContent();

      switch (code) {
        case 'en-US':
          expect(heading).toMatch(/Welcome|PetCare/);
          break;
        case 'de-DE':
          expect(heading).toMatch(/Willkommen|Haustiere/);
          break;
        case 'ru-RU':
          expect(heading).toMatch(/Добро пожаловать|Домашние животные/);
          break;
      }

      // 验证URL结构
      const articleLink = await page.locator('.article-card a').first().getAttribute('href');

      switch (code) {
        case 'en-US':
          expect(articleLink).toMatch(/^\/[\w-]+$/);
          break;
        case 'de-DE':
          expect(articleLink).toMatch(/^\/[\w-]+$/);
          break;
        case 'ru-RU':
          expect(articleLink).toMatch(/^\/[\w-]+$/);
          break;
      }
    });

    test(`should have proper SEO tags for ${code}`, async ({ page }) => {
      await page.goto(url);

      // 验证meta标签
      const ogLocale = await page.getAttribute('meta[property="og:locale"]', 'content');
      expect(ogLocale).toBe(code.replace('-', '_'));

      // 验证alternate标签
      const alternateLinks = await page.$$eval('link[rel="alternate"]', (links) =>
        links.map((link) => ({
          hreflang: link.getAttribute('hreflang'),
          href: link.getAttribute('href'),
        })),
      );

      expect(alternateLinks).toContainEqual(
        expect.objectContaining({
          hreflang: expect.any(String),
          href: expect.any(String),
        }),
      );
    });
  });

  test('should maintain language consistency in navigation', async ({ page }) => {
    await page.goto('http://www.petcare.com');

    // 导航到分类页
    await page.click('nav >> text=Cats');
    await expect(page).toHaveURL(/\/category\/cats/);

    // 验证面包屑使用正确的语言
    const breadcrumb = await page.locator('.breadcrumb').textContent();
    expect(breadcrumb).toContain('Home');
    expect(breadcrumb).toContain('Cats');

    // 导航到文章页
    await page.click('.article-card >> nth=0');

    // 验证所有UI元素使用一致的语言
    const relatedTitle = await page.locator('h2:has-text("Related Articles")').textContent();
    expect(relatedTitle).toBe('Related Articles');

    const commentTitle = await page.locator('h2:has-text("Comments")').textContent();
    expect(commentTitle).toBe('Comments');
  });
});
```

## 测试报告模板

```markdown
# 测试报告

## 执行摘要

- **测试日期**: 2025-01-20
- **版本**: 1.0.0
- **环境**: Development

## 测试覆盖率

| 测试类型 | 覆盖率 | 通过率 |
| -------- | ------ | ------ |
| 单元测试 | 85.3%  | 98.5%  |
| 集成测试 | 78.2%  | 96.3%  |
| E2E测试  | -      | 94.7%  |

## 性能测试结果

| 页面   | LCP  | FID  | CLS  | 总分 |
| ------ | ---- | ---- | ---- | ---- |
| 首页   | 2.1s | 45ms | 0.05 | 93   |
| 文章页 | 2.3s | 52ms | 0.08 | 91   |
| 分类页 | 1.9s | 38ms | 0.03 | 95   |

## 问题列表

1. **高优先级**
   - 文章页LCP略高于目标值
   - 移动端图片加载优化需要改进
2. **中优先级**
   - 部分API端点缺少速率限制
   - 搜索功能需要优化索引

## 建议

1. 实施图片预加载策略
2. 优化数据库查询
3. 增加缓存层覆盖范围
```

## 总结

本测试文档提供了全面的测试策略和实施方案，覆盖了从单元测试到性能测试的各个方面。通过严格执行这些测试，可以确保系统的质量、性能和安全性达到预期标准。

### 关键要点

1. **测试金字塔**：70%单元测试、20%集成测试、10%E2E测试
2. **自动化优先**：所有测试都应该可以自动化执行
3. **持续集成**：每次代码提交都触发测试
4. **性能基准**：明确的性能指标和验证
5. **安全测试**：主动发现和修复安全漏洞

### 下一步行动

1. 搭建测试环境
2. 编写测试用例
3. 集成CI/CD流程
4. 建立测试报告机制
5. 持续优化测试覆盖率
