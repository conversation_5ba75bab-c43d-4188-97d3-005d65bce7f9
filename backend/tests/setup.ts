/**
 * Jest测试环境设置
 * 配置测试数据库、Mock和全局测试工具
 */

import { config } from '../src/config/index';

// 设置测试环境变量
process.env.NODE_ENV = 'test';
process.env.DB_NAME = process.env.DB_NAME_TEST || 'petcare_test';
process.env.LOG_LEVEL = 'error'; // 减少测试时的日志输出

// 增加测试超时时间
jest.setTimeout(30000);

// 配置全局测试设置
beforeAll(async () => {
  // 初始化配置
  await config.init();
});

afterAll(async () => {
  // 清理测试环境
  // 这里可以添加清理数据库连接等操作
});

// 每个测试前的设置
beforeEach(() => {
  // 清除所有mock调用记录
  jest.clearAllMocks();
});

// 每个测试后的清理
afterEach(() => {
  // 恢复所有mock
  jest.restoreAllMocks();
});

// Mock外部依赖
// 注意：如果需要使用邮件功能，请先安装nodemailer包
// jest.mock('nodemailer', () => ({
//   createTransporter: () => ({
//     sendMail: jest.fn().mockResolvedValue({ messageId: 'test-message-id' }),
//   }),
// }));

// Mock Redis客户端
// 注意：如果需要使用Redis，请先安装redis包
// jest.mock('redis', () => ({
//   createClient: () => ({
//     connect: jest.fn().mockResolvedValue(true),
//     get: jest.fn(),
//     set: jest.fn(),
//     del: jest.fn(),
//     exists: jest.fn(),
//     expire: jest.fn(),
//     flushall: jest.fn(),
//     quit: jest.fn(),
//   }),
// }));

// Mock Gemini AI客户端
// 注意：当实现AI翻译服务后，取消注释此mock
// jest.mock('../src/services/ai-translation', () => ({
//   translateText: jest.fn().mockResolvedValue('Translated text'),
//   detectLanguage: jest.fn().mockResolvedValue('en-US'),
// }));

// 全局测试工具
global.testUtils = {
  // 等待函数
  wait: (ms: number): Promise<void> => {
    return new Promise(resolve => setTimeout(resolve, ms));
  },
  
  // 生成随机字符串
  randomString: (length: number = 10): string => {
    const chars = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    for (let i = 0; i < length; i++) {
      result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
  },
  
  // 生成随机邮箱
  randomEmail: (): string => {
    const username = global.testUtils.randomString(8);
    return `${username}@test.example.com`;
  },
  
  // 生成测试JWT Token
  generateTestToken: (payload: any = { userId: 1 }): string => {
    const jwt = require('jsonwebtoken');
    return jwt.sign(payload, process.env.JWT_SECRET || 'test-secret', {
      expiresIn: '1h',
    });
  },
};

// 扩展全局类型声明
declare global {
  namespace NodeJS {
    interface Global {
      testUtils: {
        wait: (ms: number) => Promise<void>;
        randomString: (length?: number) => string;
        randomEmail: () => string;
        generateTestToken: (payload?: any) => string;
      };
    }
  }
  
  var testUtils: {
    wait: (ms: number) => Promise<void>;
    randomString: (length?: number) => string;
    randomEmail: () => string;
    generateTestToken: (payload?: any) => string;
  };
}

// 控制台提示
console.log('📋 Test environment setup complete');