/**
 * 测试数据库配置和管理
 * 提供测试数据库连接、数据清理和种子数据功能
 */

import { config } from '../src/config/index';

export interface TestDatabaseConfig {
  host: string;
  port: number;
  user: string;
  password: string;
  name: string;
  charset: string;
  collation: string;
}

/**
 * 获取测试数据库配置
 */
export function getTestDatabaseConfig(): TestDatabaseConfig {
  // 确保使用测试环境配置
  const dbConfig = config.db();
  
  return {
    host: process.env.DB_HOST_TEST || dbConfig.host,
    port: parseInt(process.env.DB_PORT_TEST || dbConfig.port.toString()),
    user: process.env.DB_USER_TEST || dbConfig.user,
    password: process.env.DB_PASSWORD_TEST || dbConfig.password,
    name: process.env.DB_NAME_TEST || 'petcare_test',
    charset: dbConfig.charset,
    collation: dbConfig.collation,
  };
}

/**
 * 测试数据库连接管理器
 */
export class TestDatabaseManager {
  private static instance: TestDatabaseManager;
  private connection: any = null;
  private isConnected: boolean = false;

  private constructor() {}

  public static getInstance(): TestDatabaseManager {
    if (!TestDatabaseManager.instance) {
      TestDatabaseManager.instance = new TestDatabaseManager();
    }
    return TestDatabaseManager.instance;
  }

  /**
   * 连接测试数据库
   */
  async connect(): Promise<void> {
    if (this.isConnected) {
      return;
    }

    try {
      const testConfig = getTestDatabaseConfig();
      
      // 在实际实现中，这里会创建真实的数据库连接
      // 目前使用Mock连接
      this.connection = {
        host: testConfig.host,
        port: testConfig.port,
        database: testConfig.name,
        user: testConfig.user,
        isConnected: true,
        
        // Mock方法
        query: jest.fn(),
        end: jest.fn(),
        beginTransaction: jest.fn(),
        commit: jest.fn(),
        rollback: jest.fn(),
      };
      
      this.isConnected = true;
      console.log(`📊 Connected to test database: ${testConfig.name}`);
    } catch (error) {
      console.error('❌ Failed to connect to test database:', error);
      throw error;
    }
  }

  /**
   * 断开数据库连接
   */
  async disconnect(): Promise<void> {
    if (!this.isConnected || !this.connection) {
      return;
    }

    try {
      await this.connection.end();
      this.connection = null;
      this.isConnected = false;
      console.log('📊 Disconnected from test database');
    } catch (error) {
      console.error('❌ Failed to disconnect from test database:', error);
      throw error;
    }
  }

  /**
   * 获取数据库连接
   */
  getConnection(): any {
    if (!this.isConnected || !this.connection) {
      throw new Error('Database not connected. Call connect() first.');
    }
    return this.connection;
  }

  /**
   * 执行SQL查询
   */
  async query(sql: string, params?: any[]): Promise<any> {
    const connection = this.getConnection();
    return connection.query(sql, params);
  }

  /**
   * 开始事务
   */
  async beginTransaction(): Promise<void> {
    const connection = this.getConnection();
    return connection.beginTransaction();
  }

  /**
   * 提交事务
   */
  async commit(): Promise<void> {
    const connection = this.getConnection();
    return connection.commit();
  }

  /**
   * 回滚事务
   */
  async rollback(): Promise<void> {
    const connection = this.getConnection();
    return connection.rollback();
  }
}

/**
 * 测试数据清理工具
 */
export class TestDataCleaner {
  private dbManager: TestDatabaseManager;

  constructor() {
    this.dbManager = TestDatabaseManager.getInstance();
  }

  /**
   * 清理所有表数据（保留表结构）
   */
  async clearAllTables(): Promise<void> {
    const tables = [
      'article_translations',
      'comments',
      'articles',
      'categories',
      'media_files',
      'users',
      'sites',
      'site_configs',
    ];

    try {
      // 禁用外键约束
      await this.dbManager.query('SET FOREIGN_KEY_CHECKS = 0');

      // 清理所有表
      for (const table of tables) {
        await this.dbManager.query(`TRUNCATE TABLE ${table}`);
      }

      // 重新启用外键约束
      await this.dbManager.query('SET FOREIGN_KEY_CHECKS = 1');

      console.log('🧹 All test tables cleared');
    } catch (error) {
      console.error('❌ Failed to clear tables:', error);
      throw error;
    }
  }

  /**
   * 清理特定表数据
   */
  async clearTable(tableName: string): Promise<void> {
    try {
      await this.dbManager.query(`DELETE FROM ${tableName}`);
      console.log(`🧹 Table ${tableName} cleared`);
    } catch (error) {
      console.error(`❌ Failed to clear table ${tableName}:`, error);
      throw error;
    }
  }

  /**
   * 重置自增ID
   */
  async resetAutoIncrement(tableName: string): Promise<void> {
    try {
      await this.dbManager.query(`ALTER TABLE ${tableName} AUTO_INCREMENT = 1`);
      console.log(`🔄 Auto increment reset for table ${tableName}`);
    } catch (error) {
      console.error(`❌ Failed to reset auto increment for ${tableName}:`, error);
      throw error;
    }
  }
}

/**
 * 测试数据种子工具
 */
export class TestDataSeeder {
  private dbManager: TestDatabaseManager;

  constructor() {
    this.dbManager = TestDatabaseManager.getInstance();
  }

  /**
   * 插入基础测试数据
   */
  async seedBasicData(): Promise<void> {
    try {
      // 插入测试用户
      await this.seedUsers();
      
      // 插入测试分类
      await this.seedCategories();
      
      // 插入测试站点配置
      await this.seedSites();
      
      console.log('🌱 Basic test data seeded');
    } catch (error) {
      console.error('❌ Failed to seed basic data:', error);
      throw error;
    }
  }

  /**
   * 插入测试用户
   */
  private async seedUsers(): Promise<void> {
    const users = [
      {
        id: 1,
        username: 'admin',
        email: '<EMAIL>',
        password: '$2b$10$hashedpassword',
        role: 'admin',
        status: 'active',
      },
      {
        id: 2,
        username: 'editor',
        email: '<EMAIL>',
        password: '$2b$10$hashedpassword',
        role: 'editor',
        status: 'active',
      },
      {
        id: 3,
        username: 'testuser',
        email: '<EMAIL>',
        password: '$2b$10$hashedpassword',
        role: 'user',
        status: 'active',
      },
    ];

    for (const user of users) {
      await this.dbManager.query(
        'INSERT INTO users (id, username, email, password, role, status) VALUES (?, ?, ?, ?, ?, ?)',
        [user.id, user.username, user.email, user.password, user.role, user.status]
      );
    }
  }

  /**
   * 插入测试分类
   */
  private async seedCategories(): Promise<void> {
    const categories = [
      { id: 1, slug: 'cats', sort_order: 1, parent_id: null },
      { id: 2, slug: 'dogs', sort_order: 2, parent_id: null },
      { id: 3, slug: 'cat-care', sort_order: 1, parent_id: 1 },
      { id: 4, slug: 'dog-care', sort_order: 1, parent_id: 2 },
      { id: 5, slug: 'pet-health', sort_order: 3, parent_id: null },
    ];

    for (const category of categories) {
      await this.dbManager.query(
        'INSERT INTO categories (id, slug, sort_order, parent_id) VALUES (?, ?, ?, ?)',
        [category.id, category.slug, category.sort_order, category.parent_id]
      );
    }
  }

  /**
   * 插入测试站点配置
   */
  private async seedSites(): Promise<void> {
    const sites = [
      {
        id: 1,
        language_code: 'en-US',
        name: 'PetCare English',
        domain: 'www.petcare.test',
        is_default: 1,
        status: 'active',
      },
      {
        id: 2,
        language_code: 'de-DE',
        name: 'PetCare Deutsch',
        domain: 'www.haustiere.test',
        is_default: 0,
        status: 'active',
      },
      {
        id: 3,
        language_code: 'ru-RU',
        name: 'PetCare Русский',
        domain: 'www.petcare-ru.test',
        is_default: 0,
        status: 'active',
      },
    ];

    for (const site of sites) {
      await this.dbManager.query(
        'INSERT INTO sites (id, language_code, name, domain, is_default, status) VALUES (?, ?, ?, ?, ?, ?)',
        [site.id, site.language_code, site.name, site.domain, site.is_default, site.status]
      );
    }
  }
}

/**
 * 测试数据库工具函数
 */
export const testDb = {
  manager: TestDatabaseManager.getInstance(),
  cleaner: new TestDataCleaner(),
  seeder: new TestDataSeeder(),

  /**
   * 初始化测试数据库
   */
  async setup(): Promise<void> {
    await this.manager.connect();
    await this.cleaner.clearAllTables();
    await this.seeder.seedBasicData();
  },

  /**
   * 清理测试数据库
   */
  async teardown(): Promise<void> {
    await this.cleaner.clearAllTables();
    await this.manager.disconnect();
  },

  /**
   * 重置测试数据库（清理后重新插入种子数据）
   */
  async reset(): Promise<void> {
    await this.cleaner.clearAllTables();
    await this.seeder.seedBasicData();
  },
};