/**
 * 示例测试文件
 * 演示测试框架的使用方法和验证配置是否正确
 */

import { createMockRequest, createMockResponse, createMockNext, ApiTestUtils } from './utils/http-mock';
import { createMockRepository, TestDataFactory, testDbUtils } from './utils/database-mock';
import { AppError, ValidationError, AuthError } from '../src/utils/AppError';
import { ErrorCode } from '../src/types/errors';
import './setup'; // 导入setup文件以确保全局变量被定义

describe('测试框架验证', () => {
  describe('测试工具函数', () => {
    it('应该生成随机字符串', () => {
      const randomString = testUtils.randomString(10);
      expect(randomString).toHaveLength(10);
      expect(typeof randomString).toBe('string');
    });

    it('应该生成随机邮箱', () => {
      const email = testUtils.randomEmail();
      expect(email).toMatch(/^[\w-\.]+@test\.example\.com$/);
    });

    it('应该生成测试JWT Token', () => {
      const token = testUtils.generateTestToken({ userId: 123 });
      expect(typeof token).toBe('string');
      expect(token.split('.').length).toBe(3); // JWT格式验证
    });

    it('应该提供等待函数', async () => {
      const start = Date.now();
      await testUtils.wait(100);
      const end = Date.now();
      expect(end - start).toBeGreaterThanOrEqual(100);
    });
  });

  describe('HTTP Mock工具', () => {
    it('应该创建Mock Request对象', () => {
      const req = createMockRequest({
        method: 'POST',
        url: '/api/test',
        body: { test: 'data' },
      });

      expect(req.method).toBe('POST');
      expect(req.url).toBe('/api/test');
      expect(req.body).toEqual({ test: 'data' });
      expect(req.get).toBeDefined();
      expect(req.get!('user-agent')).toBe('Test Agent');
    });

    it('应该创建Mock Response对象', () => {
      const res = createMockResponse();

      expect(res.status).toBeDefined();
      expect(res.json).toBeDefined();
      expect(res.send).toBeDefined();
      expect(res.set).toBeDefined();

      // 测试链式调用
      res.status!(200).json!({ success: true });
      expect(res.status).toHaveBeenCalledWith(200);
      expect(res.json).toHaveBeenCalledWith({ success: true });
    });

    it('应该创建Mock Next函数', () => {
      const next = createMockNext();
      expect(typeof next).toBe('function');
      
      next();
      expect(next).toHaveBeenCalledTimes(1);
    });
  });

  describe('API测试工具', () => {
    it('应该创建认证请求头', () => {
      const headers = ApiTestUtils.createAuthHeaders();
      expect(headers).toHaveProperty('Authorization');
      expect(headers.Authorization).toMatch(/^Bearer /);
      expect(headers['Content-Type']).toBe('application/json');
    });

    it('应该验证API响应格式 - 成功响应', () => {
      const successResponse = {
        success: true,
        data: { id: 1, name: 'test' },
        meta: { timestamp: new Date().toISOString() },
      };

      expect(() => {
        ApiTestUtils.validateApiResponse(successResponse, true);
      }).not.toThrow();
    });

    it('应该验证API响应格式 - 错误响应', () => {
      const errorResponse = {
        success: false,
        error: {
          code: 'TEST_ERROR',
          message: 'Test error message',
          request_id: 'test-request-123',
        },
      };

      expect(() => {
        ApiTestUtils.validateApiResponse(errorResponse, false);
      }).not.toThrow();
    });

    it('应该验证分页响应格式', () => {
      const paginationResponse = {
        data: [{ id: 1 }, { id: 2 }],
        pagination: {
          total: 100,
          per_page: 20,
          current_page: 1,
          total_pages: 5,
          has_next: true,
          has_prev: false,
        },
      };

      expect(() => {
        ApiTestUtils.validatePaginationResponse(paginationResponse);
      }).not.toThrow();
    });

    it('应该创建测试用户数据', () => {
      const userData = ApiTestUtils.createTestUserData({
        username: 'customuser',
      });

      expect(userData).toHaveProperty('username');
      expect(userData).toHaveProperty('email');
      expect(userData).toHaveProperty('password');
      expect(userData.username).toBe('customuser');
      expect(userData.email).toMatch(/@test\.example\.com$/);
    });
  });

  describe('数据库Mock工具', () => {
    it('应该创建Mock Repository', () => {
      const repo = createMockRepository();

      expect(repo.find).toBeDefined();
      expect(repo.findOne).toBeDefined();
      expect(repo.save).toBeDefined();
      expect(repo.createQueryBuilder).toBeDefined();

      // 测试查询构建器
      const queryBuilder = repo.createQueryBuilder();
      expect(queryBuilder.where).toBeDefined();
      expect(queryBuilder.orderBy).toBeDefined();
      expect(queryBuilder.limit).toBeDefined();

      // 测试链式调用
      queryBuilder.where('id = :id', { id: 1 }).orderBy('created_at', 'DESC');
      expect(queryBuilder.where).toHaveBeenCalled();
      expect(queryBuilder.orderBy).toHaveBeenCalled();
    });

    it('应该创建测试数据', () => {
      const user = TestDataFactory.createUser();
      expect(user).toHaveProperty('id');
      expect(user).toHaveProperty('username');
      expect(user).toHaveProperty('email');
      expect(user.username).toBe('testuser');

      const article = TestDataFactory.createArticle({
        title: 'Custom Title',
      });
      expect(article).toHaveProperty('id');
      expect(article).toHaveProperty('title');
      expect(article.title).toBe('Custom Title');

      const comment = TestDataFactory.createComment();
      expect(comment).toHaveProperty('id');
      expect(comment).toHaveProperty('content');
      expect(comment).toHaveProperty('author_name');
    });
  });

  describe('错误处理系统', () => {
    it('应该创建AppError', () => {
      const error = new AppError(ErrorCode.VALIDATION_ERROR, 'Test error');
      expect(error).toBeInstanceOf(AppError);
      expect(error.code).toBe(ErrorCode.VALIDATION_ERROR);
      expect(error.message).toBe('Test error');
      expect(error.statusCode).toBe(400);
    });

    it('应该创建ValidationError', () => {
      const error = ValidationError.required('email');
      expect(error).toBeInstanceOf(ValidationError);
      expect(error.code).toBe(ErrorCode.REQUIRED_FIELD);
      expect(error.details).toHaveLength(1);
      expect(error.details![0].field).toBe('email');
    });

    it('应该创建AuthError', () => {
      const error = new AuthError(ErrorCode.TOKEN_EXPIRED);
      expect(error).toBeInstanceOf(AuthError);
      expect(error.code).toBe(ErrorCode.TOKEN_EXPIRED);
      expect(error.statusCode).toBe(401);
    });
  });

  describe('异步测试示例', () => {
    it('应该处理Promise', async () => {
      const result = await Promise.resolve('test result');
      expect(result).toBe('test result');
    });

    it('应该处理async/await', async () => {
      const mockAsyncFunction = jest.fn().mockResolvedValue('async result');
      const result = await mockAsyncFunction();
      expect(result).toBe('async result');
      expect(mockAsyncFunction).toHaveBeenCalledTimes(1);
    });

    it('应该处理Promise错误', async () => {
      const mockFailFunction = jest.fn().mockRejectedValue(new Error('Test error'));
      await expect(mockFailFunction()).rejects.toThrow('Test error');
    });
  });

  describe('Mock验证示例', () => {
    it('应该验证函数调用', () => {
      const mockFn = jest.fn();
      mockFn('arg1', 'arg2');
      
      expect(mockFn).toHaveBeenCalledTimes(1);
      expect(mockFn).toHaveBeenCalledWith('arg1', 'arg2');
    });

    it('应该验证函数返回值', () => {
      const mockFn = jest.fn().mockReturnValue('mocked result');
      const result = mockFn();
      
      expect(result).toBe('mocked result');
      expect(mockFn).toHaveBeenCalled();
    });

    it('应该验证Promise Mock', async () => {
      const mockFn = jest.fn().mockResolvedValue({ id: 1, name: 'test' });
      const result = await mockFn();
      
      expect(result).toEqual({ id: 1, name: 'test' });
      expect(mockFn).toHaveBeenCalled();
    });
  });
});

describe('集成测试示例', () => {
  beforeEach(async () => {
    // 每个测试前的准备工作
    await testDbUtils.clearAllTables();
  });

  afterEach(async () => {
    // 每个测试后的清理工作
    jest.clearAllMocks();
  });

  it('应该模拟完整的API调用流程', async () => {
    // 模拟数据库操作
    const mockRepo = createMockRepository();
    const testUser = TestDataFactory.createUser();
    mockRepo.findOne.mockResolvedValue(testUser);

    // 模拟HTTP请求（注释掉未使用的变量以避免lint警告）
    // const req = createMockRequest({
    //   method: 'GET',
    //   url: '/api/v1/users/1',
    //   params: { id: '1' },
    // });
    const res = createMockResponse();
    // const next = createMockNext();

    // 模拟控制器逻辑
    const user = await mockRepo.findOne({ where: { id: 1 } });
    res.status!(200).json!({
      success: true,
      data: user,
      meta: { timestamp: new Date().toISOString() },
    });

    // 验证结果
    expect(mockRepo.findOne).toHaveBeenCalledWith({ where: { id: 1 } });
    expect(res.status).toHaveBeenCalledWith(200);
    expect(res.json).toHaveBeenCalledWith(
      expect.objectContaining({
        success: true,
        data: testUser,
      })
    );
  });
});