/**
 * HTTP请求Mock工具
 * 提供Express请求/响应对象的Mock和API测试工具
 */

import { Request, Response, NextFunction } from 'express';
import '../setup'; // 导入setup文件以访问全局testUtils

export interface MockRequest extends Partial<Request> {
  body?: any;
  params?: any;
  query?: any;
  headers?: any;
  user?: any;
  method?: string;
  url?: string;
  originalUrl?: string;
  ip?: string;
  connection?: any;
  get?: jest.MockedFunction<any>;
}

export interface MockResponse extends Partial<Response> {
  status?: jest.MockedFunction<any>;
  json?: jest.MockedFunction<any>;
  send?: jest.MockedFunction<any>;
  set?: jest.MockedFunction<any>;
  cookie?: jest.MockedFunction<any>;
  clearCookie?: jest.MockedFunction<any>;
  redirect?: jest.MockedFunction<any>;
  end?: jest.MockedFunction<any>;
  statusCode?: number;
  locals?: any;
}

/**
 * 创建Mock Request对象
 */
export function createMockRequest(options: MockRequest = {}): MockRequest {
  return {
    body: {},
    params: {},
    query: {},
    headers: {},
    method: 'GET',
    url: '/',
    originalUrl: '/',
    ip: '127.0.0.1',
    connection: { remoteAddress: '127.0.0.1' },
    get: jest.fn((header: string) => {
      const headers: any = {
        'user-agent': 'Test Agent',
        'content-type': 'application/json',
        'accept': 'application/json',
        ...options.headers,
      };
      return headers[header.toLowerCase()];
    }),
    ...options,
  };
}

/**
 * 创建Mock Response对象
 */
export function createMockResponse(): MockResponse {
  const res: MockResponse = {
    statusCode: 200,
    locals: {},
  };
  
  // 设置链式调用的mock函数
  res.status = jest.fn().mockReturnValue(res);
  res.json = jest.fn().mockReturnValue(res);
  res.send = jest.fn().mockReturnValue(res);
  res.set = jest.fn().mockReturnValue(res);
  res.cookie = jest.fn().mockReturnValue(res);
  res.clearCookie = jest.fn().mockReturnValue(res);
  res.redirect = jest.fn().mockReturnValue(res);
  res.end = jest.fn().mockReturnValue(res);
  
  return res;
}

/**
 * 创建Mock Next函数
 */
export function createMockNext(): jest.MockedFunction<NextFunction> {
  return jest.fn();
}

/**
 * API测试工具类
 */
export class ApiTestUtils {
  /**
   * 创建认证请求头
   */
  static createAuthHeaders(token?: string): any {
    const authToken = token || testUtils.generateTestToken();
    return {
      'Authorization': `Bearer ${authToken}`,
      'Content-Type': 'application/json',
    };
  }

  /**
   * 创建表单请求头
   */
  static createFormHeaders(): any {
    return {
      'Content-Type': 'application/x-www-form-urlencoded',
    };
  }

  /**
   * 创建文件上传请求头
   */
  static createFileUploadHeaders(): any {
    return {
      'Content-Type': 'multipart/form-data',
    };
  }

  /**
   * 验证API响应格式
   */
  static validateApiResponse(response: any, shouldSucceed: boolean = true): void {
    expect(response).toHaveProperty('success');
    expect(response.success).toBe(shouldSucceed);
    
    if (shouldSucceed) {
      expect(response).toHaveProperty('data');
      expect(response).toHaveProperty('meta');
      expect(response.meta).toHaveProperty('timestamp');
    } else {
      expect(response).toHaveProperty('error');
      expect(response.error).toHaveProperty('code');
      expect(response.error).toHaveProperty('message');
      expect(response.error).toHaveProperty('request_id');
    }
  }

  /**
   * 验证分页响应
   */
  static validatePaginationResponse(response: any): void {
    expect(response).toHaveProperty('data');
    expect(response).toHaveProperty('pagination');
    
    const pagination = response.pagination;
    expect(pagination).toHaveProperty('total');
    expect(pagination).toHaveProperty('per_page');
    expect(pagination).toHaveProperty('current_page');
    expect(pagination).toHaveProperty('total_pages');
    expect(pagination).toHaveProperty('has_next');
    expect(pagination).toHaveProperty('has_prev');
    
    expect(typeof pagination.total).toBe('number');
    expect(typeof pagination.per_page).toBe('number');
    expect(typeof pagination.current_page).toBe('number');
    expect(typeof pagination.total_pages).toBe('number');
    expect(typeof pagination.has_next).toBe('boolean');
    expect(typeof pagination.has_prev).toBe('boolean');
  }

  /**
   * 验证错误响应
   */
  static validateErrorResponse(response: any, expectedErrorCode?: string, expectedStatusCode?: number): void {
    this.validateApiResponse(response, false);
    
    if (expectedErrorCode) {
      expect(response.error.code).toBe(expectedErrorCode);
    }
    
    if (expectedStatusCode) {
      expect(response.statusCode || response.status).toBe(expectedStatusCode);
    }
  }

  /**
   * 创建测试用户数据
   */
  static createTestUserData(overrides: any = {}): any {
    return {
      username: `testuser_${Date.now()}`,
      email: testUtils.randomEmail(),
      password: 'Test123!@#',
      role: 'user',
      ...overrides,
    };
  }

  /**
   * 创建测试文章数据
   */
  static createTestArticleData(overrides: any = {}): any {
    return {
      title: `Test Article ${Date.now()}`,
      content: '<p>This is a test article content</p>',
      summary: 'This is a test article summary',
      category_id: 1,
      language_code: 'zh-CN',
      meta_title: 'Test Article Meta Title',
      meta_description: 'Test article meta description',
      tags: ['test', 'article'],
      status: 'draft',
      ...overrides,
    };
  }

  /**
   * 创建测试评论数据
   */
  static createTestCommentData(overrides: any = {}): any {
    return {
      author_name: 'Test User',
      author_email: testUtils.randomEmail(),
      content: 'This is a test comment',
      article_id: 1,
      language_code: 'en-US',
      ...overrides,
    };
  }
}

/**
 * 中间件测试助手
 */
export class MiddlewareTestHelper {
  /**
   * 测试中间件执行
   */
  static async testMiddleware(
    middleware: any,
    req: MockRequest,
    res: MockResponse,
    next: jest.MockedFunction<NextFunction>
  ): Promise<void> {
    return new Promise((resolve, reject) => {
      const originalNext = next;
      const wrappedNext = (error?: any) => {
        originalNext(error);
        if (error) {
          reject(error);
        } else {
          resolve();
        }
      };
      
      try {
        middleware(req, res, wrappedNext);
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * 测试认证中间件
   */
  static async testAuthMiddleware(middleware: any, token?: string): Promise<{ req: MockRequest; res: MockResponse; next: jest.MockedFunction<NextFunction> }> {
    const req = createMockRequest({
      headers: token ? { authorization: `Bearer ${token}` } : {},
    });
    const res = createMockResponse();
    const next = createMockNext();

    await this.testMiddleware(middleware, req, res, next);

    return { req, res, next };
  }

  /**
   * 测试验证中间件
   */
  static async testValidationMiddleware(middleware: any, body: any, params?: any, query?: any): Promise<{ req: MockRequest; res: MockResponse; next: jest.MockedFunction<NextFunction> }> {
    const req = createMockRequest({
      body,
      params: params || {},
      query: query || {},
    });
    const res = createMockResponse();
    const next = createMockNext();

    await this.testMiddleware(middleware, req, res, next);

    return { req, res, next };
  }
}