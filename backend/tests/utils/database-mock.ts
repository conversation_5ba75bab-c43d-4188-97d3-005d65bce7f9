/**
 * 数据库Mock工具
 * 提供测试用的数据库操作Mock和测试数据
 */

export interface MockRepository {
  find: jest.MockedFunction<any>;
  findOne: jest.MockedFunction<any>;
  findAndCount: jest.MockedFunction<any>;
  save: jest.MockedFunction<any>;
  update: jest.MockedFunction<any>;
  delete: jest.MockedFunction<any>;
  create: jest.MockedFunction<any>;
  createQueryBuilder: jest.MockedFunction<any>;
}

/**
 * 创建Mock Repository
 */
export function createMockRepository(): MockRepository {
  return {
    find: jest.fn(),
    findOne: jest.fn(),
    findAndCount: jest.fn(),
    save: jest.fn(),
    update: jest.fn(),
    delete: jest.fn(),
    create: jest.fn(),
    createQueryBuilder: jest.fn(() => ({
      where: jest.fn().mockReturnThis(),
      andWhere: jest.fn().mockReturnThis(),
      orWhere: jest.fn().mockReturnThis(),
      orderBy: jest.fn().mockReturnThis(),
      limit: jest.fn().mockReturnThis(),
      offset: jest.fn().mockReturnThis(),
      getOne: jest.fn(),
      getMany: jest.fn(),
      getManyAndCount: jest.fn(),
      leftJoinAndSelect: jest.fn().mockReturnThis(),
      innerJoinAndSelect: jest.fn().mockReturnThis(),
      select: jest.fn().mockReturnThis(),
    })),
  };
}

/**
 * 测试数据工厂
 */
export class TestDataFactory {
  static createUser(overrides: any = {}) {
    return {
      id: 1,
      username: 'testuser',
      email: '<EMAIL>',
      password: '$2b$10$hashedpassword',
      role: 'user',
      status: 'active',
      created_at: new Date(),
      updated_at: new Date(),
      ...overrides,
    };
  }

  static createArticle(overrides: any = {}) {
    return {
      id: 1,
      title: 'Test Article',
      slug: 'test-article',
      content: '<p>Test article content</p>',
      summary: 'Test article summary',
      featured_image: '/images/test.jpg',
      meta_title: 'Test Article Meta Title',
      meta_description: 'Test article meta description',
      language_code: 'zh-CN',
      category_id: 1,
      author_id: 1,
      status: 'published',
      view_count: 100,
      comment_count: 5,
      published_at: new Date(),
      created_at: new Date(),
      updated_at: new Date(),
      ...overrides,
    };
  }

  static createCategory(overrides: any = {}) {
    return {
      id: 1,
      slug: 'test-category',
      sort_order: 1,
      parent_id: null,
      created_at: new Date(),
      updated_at: new Date(),
      ...overrides,
    };
  }

  static createComment(overrides: any = {}) {
    return {
      id: 1,
      article_id: 1,
      parent_id: null,
      author_name: 'Test User',
      author_email: '<EMAIL>',
      content: 'This is a test comment',
      status: 'approved',
      ip_address: '127.0.0.1',
      user_agent: 'Test Agent',
      language_code: 'en-US',
      created_at: new Date(),
      updated_at: new Date(),
      ...overrides,
    };
  }

  static createTranslation(overrides: any = {}) {
    return {
      id: 1,
      article_id: 1,
      language_code: 'en-US',
      title: 'Test Article English',
      content: '<p>Test article content in English</p>',
      summary: 'Test article summary in English',
      meta_title: 'Test Article English Meta',
      meta_description: 'Test article English meta description',
      translation_status: 'human_reviewed',
      translated_at: new Date(),
      created_at: new Date(),
      updated_at: new Date(),
      ...overrides,
    };
  }

  static createSite(overrides: any = {}) {
    return {
      id: 1,
      language_code: 'en-US',
      name: 'PetCare English',
      domain: 'www.petcare.com',
      is_default: true,
      status: 'active',
      config: {
        theme: 'default',
        seo: {
          title_suffix: ' - PetCare',
          default_description: 'Pet care tips and advice',
        },
      },
      created_at: new Date(),
      updated_at: new Date(),
      ...overrides,
    };
  }
}

/**
 * Mock数据库连接
 */
export function mockDatabaseConnection() {
  const mockConnection = {
    isConnected: true,
    getRepository: jest.fn((_entity: any) => createMockRepository()),
    query: jest.fn(),
    transaction: jest.fn(),
    close: jest.fn(),
    connect: jest.fn(),
    synchronize: jest.fn(),
    dropDatabase: jest.fn(),
    createQueryRunner: jest.fn(() => ({
      connect: jest.fn(),
      startTransaction: jest.fn(),
      commitTransaction: jest.fn(),
      rollbackTransaction: jest.fn(),
      release: jest.fn(),
      query: jest.fn(),
    })),
  };

  return mockConnection;
}

/**
 * 测试数据库实用工具
 */
export const testDbUtils = {
  /**
   * 清理所有表数据
   */
  async clearAllTables(): Promise<void> {
    // 在实际实现中，这里会清理测试数据库的所有表
    // 现在只是模拟
    console.log('Clearing all test database tables...');
  },

  /**
   * 插入测试数据
   */
  async seedTestData(): Promise<void> {
    // 在实际实现中，这里会插入测试数据
    console.log('Seeding test data...');
  },

  /**
   * 创建测试数据库连接
   */
  async createTestConnection(): Promise<any> {
    return mockDatabaseConnection();
  },
};