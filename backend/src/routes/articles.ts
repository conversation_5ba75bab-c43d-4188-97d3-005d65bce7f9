import { Router, Request, Response } from 'express';

const router = Router();

/**
 * @swagger
 * /articles:
 *   get:
 *     tags: [Articles]
 *     summary: 获取文章列表
 *     description: 获取文章列表，支持分页、过滤和排序
 *     parameters:
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           default: 1
 *         description: 页码
 *       - in: query
 *         name: per_page
 *         schema:
 *           type: integer
 *           minimum: 1
 *           maximum: 100
 *           default: 20
 *         description: 每页数量
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [draft, published, archived]
 *         description: 文章状态过滤
 *       - in: query
 *         name: language_code
 *         schema:
 *           type: string
 *         description: 语言代码过滤
 *         example: "en-US"
 *       - in: query
 *         name: category_id
 *         schema:
 *           type: integer
 *         description: 分类ID过滤
 *       - in: query
 *         name: search
 *         schema:
 *           type: string
 *         description: 搜索关键词
 *       - in: query
 *         name: sort
 *         schema:
 *           type: string
 *           enum: [created_at, updated_at, published_at, view_count, -created_at, -updated_at, -published_at, -view_count]
 *           default: "-created_at"
 *         description: 排序字段，前缀-表示降序
 *     responses:
 *       200:
 *         description: 文章列表获取成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/PaginatedResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: array
 *                       items:
 *                         allOf:
 *                           - $ref: '#/components/schemas/Article'
 *                           - type: object
 *                             properties:
 *                               content:
 *                                 description: "列表中不返回完整内容"
 *                                 nullable: true
 *             example:
 *               success: true
 *               data:
 *                 - id: 123
 *                   title: "10 Tips for Cat Care"
 *                   slug: "10-tips-for-cat-care"
 *                   summary: "Essential tips for taking care of your cat"
 *                   featured_image: "https://cdn.petcare.com/images/cat-care.jpg"
 *                   category:
 *                     id: 3
 *                     name: "Cat Care"
 *                     slug: "cat-care"
 *                   author:
 *                     id: 1
 *                     username: "admin"
 *                     avatar: "https://cdn.petcare.com/avatars/admin.jpg"
 *                   view_count: 1520
 *                   comment_count: 23
 *                   published_at: "2025-01-15T08:00:00.000Z"
 *                   language_code: "en-US"
 *               pagination:
 *                 total: 156
 *                 per_page: 20
 *                 current_page: 1
 *                 total_pages: 8
 *                 has_next: true
 *                 has_prev: false
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/', async (_req: Request, res: Response) => {
  // TODO: 实现获取文章列表逻辑
  res.json({
    success: true,
    message: 'Get articles list endpoint - implementation pending'
  });
});

/**
 * @swagger
 * /articles/{id}:
 *   get:
 *     tags: [Articles]
 *     summary: 获取文章详情
 *     description: 根据ID获取单篇文章的完整信息
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 文章ID
 *         example: 123
 *     responses:
 *       200:
 *         description: 文章详情获取成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       allOf:
 *                         - $ref: '#/components/schemas/Article'
 *                         - type: object
 *                           properties:
 *                             translations:
 *                               type: array
 *                               items:
 *                                 type: object
 *                                 properties:
 *                                   language_code:
 *                                     type: string
 *                                     example: "de-DE"
 *                                   status:
 *                                     type: string
 *                                     example: "published"
 *             example:
 *               success: true
 *               data:
 *                 id: 123
 *                 title: "10 Tips for Cat Care"
 *                 slug: "10-tips-for-cat-care"
 *                 content: "<p>Full article content...</p>"
 *                 summary: "Essential tips for taking care of your cat"
 *                 featured_image: "https://cdn.petcare.com/images/cat-care.jpg"
 *                 meta_title: "10 Essential Cat Care Tips | PetCare"
 *                 meta_description: "Discover the top 10 tips for taking care of your cat"
 *                 meta_keywords: "cat care, pet tips, cat health"
 *                 category:
 *                   id: 3
 *                   name: "Cat Care"
 *                   slug: "cat-care"
 *                 author:
 *                   id: 1
 *                   username: "admin"
 *                   email: "<EMAIL>"
 *                 tags: ["cat-care", "pet-health", "tips"]
 *                 translations:
 *                   - language_code: "de-DE"
 *                     status: "published"
 *                   - language_code: "ru-RU"
 *                     status: "draft"
 *                 view_count: 1520
 *                 comment_count: 23
 *                 status: "published"
 *                 published_at: "2025-01-15T08:00:00.000Z"
 *                 created_at: "2025-01-10T10:00:00.000Z"
 *                 updated_at: "2025-01-15T07:45:00.000Z"
 *       404:
 *         description: 文章不存在
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: object
 *                   properties:
 *                     code:
 *                       type: string
 *                       example: "RESOURCE_NOT_FOUND"
 *                     message:
 *                       type: string
 *                       example: "Article not found"
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.get('/:id', async (_req: Request, res: Response) => {
  // TODO: 实现获取文章详情逻辑
  res.json({
    success: true,
    message: 'Get article detail endpoint - implementation pending'
  });
});

/**
 * @swagger
 * /articles:
 *   post:
 *     tags: [Articles]
 *     summary: 创建文章
 *     description: 创建新的文章
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateArticleRequest'
 *           example:
 *             category_id: 3
 *             title: "新手养猫指南"
 *             content: "<p>详细的养猫指南内容...</p>"
 *             summary: "为新手猫主人准备的完整指南"
 *             featured_image: "base64_encoded_image_or_url"
 *             meta_title: "新手养猫指南 - 完整攻略"
 *             meta_description: "最全面的新手养猫指南"
 *             meta_keywords: "养猫,新手,指南"
 *             tags: ["养猫", "新手指南"]
 *             status: "draft"
 *             language_code: "zh-CN"
 *     responses:
 *       201:
 *         description: 文章创建成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: integer
 *                           example: 124
 *                         title:
 *                           type: string
 *                           example: "新手养猫指南"
 *                         slug:
 *                           type: string
 *                           example: "xin-shou-yang-mao-zhi-nan"
 *                         status:
 *                           type: string
 *                           example: "draft"
 *                         created_at:
 *                           type: string
 *                           format: date-time
 *                           example: "2025-01-20T10:00:00.000Z"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       403:
 *         description: 权限不足
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: object
 *                   properties:
 *                     code:
 *                       type: string
 *                       example: "PERMISSION_DENIED"
 *                     message:
 *                       type: string
 *                       example: "Insufficient permissions to create articles"
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.post('/', async (_req: Request, res: Response) => {
  // TODO: 实现创建文章逻辑
  res.status(201).json({
    success: true,
    message: 'Create article endpoint - implementation pending'
  });
});

/**
 * @swagger
 * /articles/{id}:
 *   put:
 *     tags: [Articles]
 *     summary: 更新文章
 *     description: 完整更新指定文章的信息
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 文章ID
 *         example: 124
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             $ref: '#/components/schemas/CreateArticleRequest'
 *           example:
 *             title: "新手养猫完全指南"
 *             content: "<p>更新后的内容...</p>"
 *             status: "published"
 *     responses:
 *       200:
 *         description: 文章更新成功
 *         content:
 *           application/json:
 *             schema:
 *               allOf:
 *                 - $ref: '#/components/schemas/SuccessResponse'
 *                 - type: object
 *                   properties:
 *                     data:
 *                       type: object
 *                       properties:
 *                         id:
 *                           type: integer
 *                           example: 124
 *                         title:
 *                           type: string
 *                           example: "新手养猫完全指南"
 *                         status:
 *                           type: string
 *                           example: "published"
 *                         updated_at:
 *                           type: string
 *                           format: date-time
 *                           example: "2025-01-20T11:00:00.000Z"
 *       400:
 *         $ref: '#/components/responses/ValidationError'
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: 文章不存在
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: object
 *                   properties:
 *                     code:
 *                       type: string
 *                       example: "RESOURCE_NOT_FOUND"
 *                     message:
 *                       type: string
 *                       example: "Article not found"
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.put('/:id', async (_req: Request, res: Response) => {
  // TODO: 实现更新文章逻辑
  res.json({
    success: true,
    message: 'Update article endpoint - implementation pending'
  });
});

/**
 * @swagger
 * /articles/{id}:
 *   delete:
 *     tags: [Articles]
 *     summary: 删除文章
 *     description: 删除指定的文章
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: id
 *         required: true
 *         schema:
 *           type: integer
 *         description: 文章ID
 *         example: 124
 *     responses:
 *       204:
 *         description: 文章删除成功
 *       401:
 *         $ref: '#/components/responses/UnauthorizedError'
 *       404:
 *         description: 文章不存在
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 error:
 *                   type: object
 *                   properties:
 *                     code:
 *                       type: string
 *                       example: "RESOURCE_NOT_FOUND"
 *                     message:
 *                       type: string
 *                       example: "Article not found"
 *       500:
 *         $ref: '#/components/responses/InternalError'
 */
router.delete('/:id', async (_req: Request, res: Response) => {
  // TODO: 实现删除文章逻辑
  res.status(204).send();
});

export default router;