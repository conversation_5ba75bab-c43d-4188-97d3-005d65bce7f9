import { Router } from 'express';
import authRoutes from './auth';
import articleRoutes from './articles';

/**
 * @swagger
 * components:
 *   parameters:
 *     PageParam:
 *       in: query
 *       name: page
 *       schema:
 *         type: integer
 *         minimum: 1
 *         default: 1
 *       description: 页码
 *     PerPageParam:
 *       in: query
 *       name: per_page
 *       schema:
 *         type: integer
 *         minimum: 1
 *         maximum: 100
 *         default: 20
 *       description: 每页数量
 *     SortParam:
 *       in: query
 *       name: sort
 *       schema:
 *         type: string
 *       description: 排序字段，前缀-表示降序
 *     LanguageParam:
 *       in: query
 *       name: language_code
 *       schema:
 *         type: string
 *       description: 语言代码
 *       example: "en-US"
 */

const router = Router();

// API路由
router.use('/auth', authRoutes);
router.use('/articles', articleRoutes);

/**
 * @swagger
 * /:
 *   get:
 *     tags: [General]
 *     summary: API根端点
 *     description: 获取API基本信息和可用端点列表
 *     responses:
 *       200:
 *         description: API信息
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 version:
 *                   type: string
 *                   example: "1.0.0"
 *                 message:
 *                   type: string
 *                   example: "PetCare Blog API"
 *                 endpoints:
 *                   type: object
 *                   properties:
 *                     health:
 *                       type: string
 *                       example: "/health"
 *                     auth:
 *                       type: string
 *                       example: "/api/v1/auth"
 *                     articles:
 *                       type: string
 *                       example: "/api/v1/articles"
 *                     categories:
 *                       type: string
 *                       example: "/api/v1/categories"
 *                     comments:
 *                       type: string
 *                       example: "/api/v1/comments"
 *             example:
 *               version: "1.0.0"
 *               message: "PetCare Blog API"
 *               endpoints:
 *                 health: "/health"
 *                 auth: "/api/v1/auth"
 *                 articles: "/api/v1/articles"
 *                 categories: "/api/v1/categories"
 *                 comments: "/api/v1/comments"
 */
router.get('/', (_req, res) => {
  res.json({
    version: '1.0.0',
    message: 'PetCare Blog API',
    endpoints: {
      health: '/health',
      docs: '/api/docs',
      auth: '/api/v1/auth',
      articles: '/api/v1/articles',
      categories: '/api/v1/categories',
      comments: '/api/v1/comments',
    },
  });
});

export default router;