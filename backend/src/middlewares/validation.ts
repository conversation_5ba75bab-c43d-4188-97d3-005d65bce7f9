/**
 * 输入验证中间件
 * 提供统一的请求参数验证功能
 */

import { Request, Response, NextFunction } from 'express';
import { ErrorFactory } from '../utils/AppError';
import { ErrorDetail } from '../types/errors';

/**
 * 验证规则接口
 */
export interface ValidationRule {
  required?: boolean;
  type?: 'string' | 'number' | 'boolean' | 'email' | 'array' | 'object';
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  enum?: any[];
  custom?: (value: any) => boolean | string;
  message?: string;
}

/**
 * 验证规则集合
 */
export interface ValidationRules {
  body?: Record<string, ValidationRule>;
  params?: Record<string, ValidationRule>;
  query?: Record<string, ValidationRule>;
  headers?: Record<string, ValidationRule>;
}

/**
 * 验证结果接口
 */
interface ValidationResult {
  isValid: boolean;
  errors: ErrorDetail[];
}

/**
 * 验证器类
 */
export class Validator {
  /**
   * 验证单个值
   */
  private static validateValue(
    value: any,
    rule: ValidationRule,
    fieldName: string,
    location: string
  ): ErrorDetail[] {
    const errors: ErrorDetail[] = [];
    const fieldPath = `${location}.${fieldName}`;

    // 检查必填字段
    if (rule.required && (value === undefined || value === null || value === '')) {
      errors.push({
        field: fieldPath,
        message: rule.message || `${fieldName} is required`,
        code: 'required'
      });
      return errors; // 如果必填字段为空，不进行其他验证
    }

    // 如果值为空且非必填，跳过其他验证
    if (value === undefined || value === null || value === '') {
      return errors;
    }

    // 类型验证
    if (rule.type && !this.validateType(value, rule.type)) {
      errors.push({
        field: fieldPath,
        message: rule.message || `${fieldName} must be of type ${rule.type}`,
        code: 'invalid_type',
        value: value
      });
    }

    // 字符串长度验证
    if (typeof value === 'string') {
      if (rule.minLength !== undefined && value.length < rule.minLength) {
        errors.push({
          field: fieldPath,
          message: rule.message || `${fieldName} must be at least ${rule.minLength} characters`,
          code: 'min_length',
          value: value
        });
      }
      if (rule.maxLength !== undefined && value.length > rule.maxLength) {
        errors.push({
          field: fieldPath,
          message: rule.message || `${fieldName} must not exceed ${rule.maxLength} characters`,
          code: 'max_length',
          value: value
        });
      }
    }

    // 数值范围验证
    if (typeof value === 'number') {
      if (rule.min !== undefined && value < rule.min) {
        errors.push({
          field: fieldPath,
          message: rule.message || `${fieldName} must be at least ${rule.min}`,
          code: 'min_value',
          value: value
        });
      }
      if (rule.max !== undefined && value > rule.max) {
        errors.push({
          field: fieldPath,
          message: rule.message || `${fieldName} must not exceed ${rule.max}`,
          code: 'max_value',
          value: value
        });
      }
    }

    // 正则表达式验证
    if (rule.pattern && typeof value === 'string' && !rule.pattern.test(value)) {
      errors.push({
        field: fieldPath,
        message: rule.message || `${fieldName} format is invalid`,
        code: 'invalid_format',
        value: value
      });
    }

    // 枚举值验证
    if (rule.enum && !rule.enum.includes(value)) {
      errors.push({
        field: fieldPath,
        message: rule.message || `${fieldName} must be one of: ${rule.enum.join(', ')}`,
        code: 'invalid_enum',
        value: value
      });
    }

    // 自定义验证
    if (rule.custom) {
      const customResult = rule.custom(value);
      if (customResult !== true) {
        errors.push({
          field: fieldPath,
          message: typeof customResult === 'string' ? customResult : (rule.message || `${fieldName} is invalid`),
          code: 'custom_validation',
          value: value
        });
      }
    }

    return errors;
  }

  /**
   * 类型验证
   */
  private static validateType(value: any, type: string): boolean {
    switch (type) {
      case 'string':
        return typeof value === 'string';
      case 'number':
        return typeof value === 'number' && !isNaN(value);
      case 'boolean':
        return typeof value === 'boolean';
      case 'array':
        return Array.isArray(value);
      case 'object':
        return typeof value === 'object' && value !== null && !Array.isArray(value);
      case 'email':
        if (typeof value !== 'string') return false;
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        return emailRegex.test(value);
      default:
        return true;
    }
  }

  /**
   * 验证对象
   */
  public static validateObject(
    obj: any,
    rules: Record<string, ValidationRule>,
    location: string
  ): ValidationResult {
    const errors: ErrorDetail[] = [];

    // 验证规则中定义的字段
    for (const [fieldName, rule] of Object.entries(rules)) {
      const fieldErrors = this.validateValue(obj[fieldName], rule, fieldName, location);
      errors.push(...fieldErrors);
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * 验证整个请求
   */
  public static validateRequest(req: Request, rules: ValidationRules): ValidationResult {
    const allErrors: ErrorDetail[] = [];

    // 验证请求体
    if (rules.body) {
      const result = this.validateObject(req.body || {}, rules.body, 'body');
      allErrors.push(...result.errors);
    }

    // 验证路径参数
    if (rules.params) {
      const result = this.validateObject(req.params || {}, rules.params, 'params');
      allErrors.push(...result.errors);
    }

    // 验证查询参数
    if (rules.query) {
      const result = this.validateObject(req.query || {}, rules.query, 'query');
      allErrors.push(...result.errors);
    }

    // 验证请求头
    if (rules.headers) {
      const result = this.validateObject(req.headers || {}, rules.headers, 'headers');
      allErrors.push(...result.errors);
    }

    return {
      isValid: allErrors.length === 0,
      errors: allErrors
    };
  }
}

/**
 * 创建验证中间件
 */
export const validate = (rules: ValidationRules) => {
  return (req: Request, _res: Response, next: NextFunction) => {
    const result = Validator.validateRequest(req, rules);
    
    if (!result.isValid) {
      const validationError = ErrorFactory.multiFieldValidation(
        result.errors.map(err => ({
          field: err.field || 'unknown',
          message: err.message,
          code: err.code || 'validation_error'
        }))
      );
      
      throw validationError;
    }

    next();
  };
};

/**
 * 常用验证规则
 */
export const commonRules = {
  // ID验证
  id: {
    required: true,
    type: 'number' as const,
    min: 1,
    message: 'Valid ID is required'
  },

  // 字符串ID验证（UUID等）
  stringId: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    message: 'Valid ID is required'
  },

  // 邮箱验证
  email: {
    required: true,
    type: 'email' as const,
    message: 'Valid email address is required'
  },

  // 密码验证
  password: {
    required: true,
    type: 'string' as const,
    minLength: 8,
    maxLength: 128,
    pattern: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]/,
    message: 'Password must be 8-128 characters and contain uppercase, lowercase, number and special character'
  },

  // 用户名验证
  username: {
    required: true,
    type: 'string' as const,
    minLength: 3,
    maxLength: 50,
    pattern: /^[a-zA-Z0-9_-]+$/,
    message: 'Username must be 3-50 characters and contain only letters, numbers, underscore and dash'
  },

  // 分页参数
  page: {
    type: 'number' as const,
    min: 1,
    message: 'Page must be a positive number'
  },

  perPage: {
    type: 'number' as const,
    min: 1,
    max: 100,
    message: 'Per page must be between 1 and 100'
  },

  // 状态验证
  status: {
    type: 'string' as const,
    enum: ['draft', 'published', 'archived'],
    message: 'Status must be draft, published, or archived'
  },

  // 语言代码验证
  languageCode: {
    type: 'string' as const,
    pattern: /^[a-z]{2}-[A-Z]{2}$/,
    message: 'Language code must be in format: en-US'
  },

  // 标题验证
  title: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    maxLength: 200,
    message: 'Title is required and must be 1-200 characters'
  },

  // 内容验证
  content: {
    required: true,
    type: 'string' as const,
    minLength: 1,
    message: 'Content is required'
  },

  // URL验证
  url: {
    type: 'string' as const,
    pattern: /^https?:\/\/.+/,
    message: 'Must be a valid HTTP/HTTPS URL'
  },

  // 分类ID验证
  categoryId: {
    required: true,
    type: 'number' as const,
    min: 1,
    message: 'Valid category ID is required'
  }
};

/**
 * 预定义的验证规则集合
 */
export const validationRules = {
  // 分页查询
  pagination: {
    query: {
      page: { ...commonRules.page, required: false },
      per_page: { ...commonRules.perPage, required: false },
      sort: {
        type: 'string' as const,
        pattern: /^-?[a-zA-Z_]+$/,
        message: 'Sort must be a field name, optionally prefixed with - for descending'
      }
    }
  },

  // ID参数
  idParam: {
    params: {
      id: commonRules.id
    }
  },

  // 语言参数
  languageParam: {
    params: {
      language: commonRules.languageCode
    }
  },

  // 用户登录
  userLogin: {
    body: {
      email: commonRules.email,
      password: {
        required: true,
        type: 'string' as const,
        minLength: 1,
        message: 'Password is required'
      }
    }
  },

  // 用户注册
  userRegister: {
    body: {
      username: commonRules.username,
      email: commonRules.email,
      password: commonRules.password
    }
  },

  // 文章创建
  createArticle: {
    body: {
      category_id: commonRules.categoryId,
      title: commonRules.title,
      content: commonRules.content,
      summary: {
        type: 'string' as const,
        maxLength: 500,
        message: 'Summary must not exceed 500 characters'
      },
      status: { ...commonRules.status, required: false },
      language_code: { ...commonRules.languageCode, required: false },
      tags: {
        type: 'array' as const,
        required: false,
        custom: (value: any[]) => {
          if (!Array.isArray(value)) return 'Tags must be an array';
          if (value.length > 10) return 'Maximum 10 tags allowed';
          return value.every(tag => typeof tag === 'string' && tag.length <= 50) || 'Each tag must be a string with maximum 50 characters';
        }
      }
    }
  },

  // 文章更新
  updateArticle: {
    params: {
      id: commonRules.id
    },
    body: {
      title: { ...commonRules.title, required: false },
      content: { ...commonRules.content, required: false },
      summary: {
        type: 'string' as const,
        maxLength: 500,
        required: false,
        message: 'Summary must not exceed 500 characters'
      },
      status: { ...commonRules.status, required: false },
      tags: {
        type: 'array' as const,
        required: false,
        custom: (value: any[]) => {
          if (!Array.isArray(value)) return 'Tags must be an array';
          if (value.length > 10) return 'Maximum 10 tags allowed';
          return value.every(tag => typeof tag === 'string' && tag.length <= 50) || 'Each tag must be a string with maximum 50 characters';
        }
      }
    }
  },

  // 评论提交
  submitComment: {
    params: {
      id: commonRules.id // 文章ID
    },
    body: {
      author_name: {
        required: true,
        type: 'string' as const,
        minLength: 1,
        maxLength: 100,
        message: 'Author name is required and must be 1-100 characters'
      },
      author_email: commonRules.email,
      content: {
        required: true,
        type: 'string' as const,
        minLength: 1,
        maxLength: 1000,
        message: 'Comment content is required and must be 1-1000 characters'
      },
      parent_id: {
        type: 'number' as const,
        min: 1,
        required: false,
        message: 'Parent comment ID must be a positive number'
      }
    }
  }
};

/**
 * 创建自定义验证规则的便捷函数
 */
export const createValidation = {
  required: (type: ValidationRule['type'], message?: string): ValidationRule => ({
    required: true,
    type,
    message
  }),

  optional: (type: ValidationRule['type'], message?: string): ValidationRule => ({
    required: false,
    type,
    message
  }),

  string: (minLength?: number, maxLength?: number, message?: string): ValidationRule => ({
    type: 'string',
    minLength,
    maxLength,
    message
  }),

  number: (min?: number, max?: number, message?: string): ValidationRule => ({
    type: 'number',
    min,
    max,
    message
  }),

  enum: (values: any[], message?: string): ValidationRule => ({
    enum: values,
    message: message || `Must be one of: ${values.join(', ')}`
  }),

  pattern: (regex: RegExp, message?: string): ValidationRule => ({
    pattern: regex,
    message
  }),

  custom: (validator: (value: any) => boolean | string, message?: string): ValidationRule => ({
    custom: validator,
    message
  })
};