import { Request, Response, NextFunction } from 'express';
import { getLogger } from '../logger';

/**
 * Swagger文档访问控制中间件
 * 根据环境变量配置访问权限
 */
export const swaggerAuthMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const environment = process.env.NODE_ENV || 'development';
  const logger = getLogger();
  
  // 开发环境下允许所有访问
  if (environment === 'development') {
    logger.debug('Swagger docs accessed in development mode', {
      extra: {
        ip: req.ip,
        userAgent: req.get('User-Agent'),
        path: req.path
      }
    });
    return next();
  }

  // 生产环境下的访问控制
  if (environment === 'production') {
    // 检查是否配置了文档访问密码
    const docsPassword = process.env.SWAGGER_DOCS_PASSWORD;
    
    if (!docsPassword) {
      logger.warn('Swagger docs access blocked - no password configured in production', {
        extra: { ip: req.ip }
      });
      return res.status(404).json({
        success: false,
        error: {
          code: 'RESOURCE_NOT_FOUND',
          message: 'Page not found'
        }
      });
    }

    // 检查基本认证
    const authHeader = req.get('Authorization');
    if (!authHeader || !authHeader.startsWith('Basic ')) {
      logger.warn('Swagger docs access blocked - missing auth header', {
        extra: { ip: req.ip }
      });
      res.set('WWW-Authenticate', 'Basic realm="API Documentation"');
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTH_FAILED',
          message: 'Authentication required to access API documentation'
        }
      });
    }

    try {
      const base64Credentials = authHeader.substring(6);
      const credentials = Buffer.from(base64Credentials, 'base64').toString('ascii');
      const [username, password] = credentials.split(':');

      // 验证凭据
      const expectedUsername = process.env.SWAGGER_DOCS_USERNAME || 'admin';
      if (username !== expectedUsername || password !== docsPassword) {
        logger.warn('Swagger docs access blocked - invalid credentials', {
          extra: { 
            ip: req.ip,
            username: username,
            timestamp: new Date().toISOString()
          }
        });
        res.set('WWW-Authenticate', 'Basic realm="API Documentation"');
        return res.status(401).json({
          success: false,
          error: {
            code: 'AUTH_FAILED',
            message: 'Invalid credentials'
          }
        });
      }

      // 认证成功
      logger.info('Swagger docs access granted', {
        extra: {
          ip: req.ip,
          username: username,
          userAgent: req.get('User-Agent'),
          path: req.path
        }
      });
      return next();

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : String(error);
      logger.error('Error processing swagger auth credentials', new Error(errorMessage), {
        extra: { 
          ip: req.ip 
        }
      });
      res.set('WWW-Authenticate', 'Basic realm="API Documentation"');
      return res.status(401).json({
        success: false,
        error: {
          code: 'AUTH_FAILED',
          message: 'Authentication error'
        }
      });
    }
  }

  // 测试环境下允许访问但记录日志
  logger.info('Swagger docs accessed in test environment', {
    extra: {
      ip: req.ip,
      userAgent: req.get('User-Agent'),
      path: req.path
    }
  });
  return next();
};

/**
 * IP白名单中间件（可选配置）
 */
export const ipWhitelistMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const allowedIps = process.env.SWAGGER_ALLOWED_IPS;
  const logger = getLogger();
  
  if (!allowedIps) {
    return next();
  }

  const clientIp = req.ip || req.connection.remoteAddress;
  const ipList = allowedIps.split(',').map(ip => ip.trim());
  
  if (!clientIp || !ipList.includes(clientIp)) {
    logger.warn('Swagger docs access blocked - IP not in whitelist', {
      extra: { 
        clientIp,
        allowedIps: ipList,
        userAgent: req.get('User-Agent')
      }
    });
    return res.status(403).json({
      success: false,
      error: {
        code: 'PERMISSION_DENIED',
        message: 'Access denied from this IP address'
      }
    });
  }

  logger.debug('Swagger docs IP whitelist check passed', {
    extra: { clientIp, allowedIps: ipList }
  });
  return next();
};

/**
 * 时间访问限制中间件（可选配置）
 */
export const timeBasedAccessMiddleware = (req: Request, res: Response, next: NextFunction) => {
  const accessWindow = process.env.SWAGGER_ACCESS_HOURS;
  const logger = getLogger();
  
  if (!accessWindow) {
    return next();
  }

  try {
    const [startHour, endHour] = accessWindow.split('-').map(h => parseInt(h.trim()));
    const currentHour = new Date().getHours();
    
    if (currentHour < startHour || currentHour > endHour) {
      logger.warn('Swagger docs access blocked - outside allowed time window', {
        extra: { 
          currentHour,
          allowedWindow: `${startHour}-${endHour}`,
          ip: req.ip
        }
      });
      return res.status(403).json({
        success: false,
        error: {
          code: 'PERMISSION_DENIED',
          message: `API documentation access is only allowed between ${startHour}:00 and ${endHour}:00`
        }
      });
    }

    logger.debug('Swagger docs time-based access check passed', {
      extra: { currentHour, allowedWindow: `${startHour}-${endHour}` }
    });
    return next();

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error('Error processing time-based access control', new Error(errorMessage), {
      extra: { 
        accessWindow 
      }
    });
    return next(); // 配置错误时允许访问
  }
};