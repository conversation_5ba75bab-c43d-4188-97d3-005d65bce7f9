/**
 * Express错误处理中间件
 * 实现统一的错误响应格式，符合API设计规范
 */

import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { AppError, ErrorFactory } from '../utils/AppError';
import { ErrorResponse, ErrorCode, ErrorConfig, ErrorContext } from '../types/errors';
import { loggingSystem } from '../logger';

/**
 * 扩展Request接口以支持requestId
 */
interface RequestWithId extends Request {
  requestId?: string;
  user?: {
    id: string;
    [key: string]: any;
  };
}

/**
 * 错误处理中间件类
 */
export class ErrorHandlerMiddleware {
  private config: ErrorConfig;
  
  constructor(config?: Partial<ErrorConfig>) {
    this.config = {
      includeDetails: process.env.NODE_ENV !== 'production',
      includeStack: process.env.NODE_ENV === 'development',
      logErrors: true,
      sensitiveFields: ['password', 'token', 'secret', 'key', 'auth', 'authorization'],
      sendNotifications: process.env.NODE_ENV === 'production',
      ...config
    };
  }

  /**
   * 创建Express错误处理中间件
   */
  createMiddleware() {
    return (error: Error, req: RequestWithId, res: Response, _next: NextFunction) => {
      // 确保有requestId
      if (!req.requestId) {
        req.requestId = uuidv4();
      }

      // 将原生错误转换为应用错误
      const appError = error instanceof AppError ? error : ErrorFactory.fromNativeError(error);

      // 构建错误上下文
      const context: ErrorContext = {
        requestId: req.requestId,
        userId: req.user?.id,
        url: req.originalUrl || req.url,
        method: req.method,
        userAgent: req.get('User-Agent'),
        ip: req.ip || req.connection.remoteAddress,
        body: this.sanitizeData(req.body),
        params: this.sanitizeData(req.params),
        query: this.sanitizeData(req.query),
        headers: this.sanitizeHeaders(req.headers as Record<string, string>)
      };

      // 记录错误日志
      if (this.config.logErrors) {
        this.logError(appError, context);
      }

      // 构建错误响应
      const errorResponse = this.buildErrorResponse(appError, req.requestId);

      // 设置响应头
      res.set('X-Request-ID', req.requestId);
      res.set('Content-Type', 'application/json');

      // 发送错误响应
      res.status(appError.statusCode).json(errorResponse);
    };
  }

  /**
   * 构建符合API规范的错误响应
   */
  private buildErrorResponse(appError: AppError, requestId?: string): ErrorResponse {
    const errorResponse: ErrorResponse = {
      success: false,
      error: {
        code: appError.code,
        message: this.getErrorMessage(appError),
        request_id: requestId
      },
      meta: {
        timestamp: new Date().toISOString(),
        request_id: requestId,
        version: process.env.npm_package_version || '1.0.0'
      }
    };

    // 添加错误详情（非生产环境或配置允许）
    if (this.config.includeDetails && appError.details) {
      errorResponse.error.details = appError.details;
    }

    // 添加堆栈信息（仅开发环境）
    if (this.config.includeStack && appError.stack) {
      (errorResponse.error as any).stack = appError.stack;
    }

    return errorResponse;
  }

  /**
   * 获取错误消息（根据环境决定详细程度）
   */
  private getErrorMessage(appError: AppError): string {
    // 生产环境中，系统错误不暴露详细信息
    if (process.env.NODE_ENV === 'production' && appError.category === 'system') {
      switch (appError.code) {
        case ErrorCode.INTERNAL_ERROR:
          return 'Internal server error';
        case ErrorCode.DATABASE_ERROR:
          return 'Service temporarily unavailable';
        case ErrorCode.NETWORK_ERROR:
          return 'Service temporarily unavailable';
        default:
          return appError.message;
      }
    }

    return appError.message;
  }

  /**
   * 记录错误日志
   */
  private logError(appError: AppError, context: ErrorContext): void {
    const errorHandler = loggingSystem.getErrorHandler();
    
    if (errorHandler) {
      // 使用现有的错误处理器
      errorHandler.handleError(appError, {
        requestId: context.requestId,
        userId: context.userId,
        url: context.url,
        method: context.method,
        userAgent: context.userAgent,
        ip: context.ip,
        context: {
          body: context.body,
          params: context.params,
          query: context.query,
          headers: context.headers
        }
      });
    } else {
      // 备用日志记录
      const logger = loggingSystem.getCoreLogger();
      
      const logData = {
        errorId: `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
        errorCode: appError.code,
        errorCategory: appError.category,
        errorLevel: appError.level,
        requestId: context.requestId,
        userId: context.userId,
        extra: {
          url: context.url,
          method: context.method,
          userAgent: context.userAgent,
          ip: context.ip,
          statusCode: appError.statusCode,
          isOperational: appError.isOperational,
          context: context
        }
      };

      switch (appError.level) {
        case 'critical':
          logger.error(`CRITICAL ERROR: ${appError.message}`, appError, logData);
          break;
        case 'error':
          logger.error(`APPLICATION ERROR: ${appError.message}`, appError, logData);
          break;
        case 'warning':
          logger.warn(`WARNING: ${appError.message}`, logData);
          break;
      }
    }
  }

  /**
   * 清理敏感数据
   */
  private sanitizeData(data: any): any {
    if (!data || typeof data !== 'object') {
      return data;
    }

    const sanitized = Array.isArray(data) ? [] : {};
    
    for (const key in data) {
      if (this.isSensitiveField(key)) {
        (sanitized as any)[key] = '***SANITIZED***';
      } else if (typeof data[key] === 'object') {
        (sanitized as any)[key] = this.sanitizeData(data[key]);
      } else {
        (sanitized as any)[key] = data[key];
      }
    }
    
    return sanitized;
  }

  /**
   * 清理敏感请求头
   */
  private sanitizeHeaders(headers: Record<string, string>): Record<string, string> {
    const sanitized: Record<string, string> = {};
    
    for (const [key, value] of Object.entries(headers)) {
      if (this.isSensitiveField(key)) {
        sanitized[key] = '***SANITIZED***';
      } else {
        sanitized[key] = value;
      }
    }
    
    return sanitized;
  }

  /**
   * 判断字段是否敏感
   */
  private isSensitiveField(field: string): boolean {
    const fieldLower = field.toLowerCase();
    return this.config.sensitiveFields.some(sensitive => 
      fieldLower.includes(sensitive.toLowerCase())
    );
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig: Partial<ErrorConfig>): void {
    this.config = { ...this.config, ...newConfig };
  }

  /**
   * 获取当前配置
   */
  getConfig(): ErrorConfig {
    return { ...this.config };
  }
}

/**
 * RequestID中间件 - 为每个请求生成唯一ID
 */
export const requestIdMiddleware = (req: RequestWithId, res: Response, next: NextFunction) => {
  // 优先使用客户端提供的request ID，否则生成新的
  req.requestId = req.get('X-Request-ID') || uuidv4();
  
  // 在响应头中返回request ID
  res.set('X-Request-ID', req.requestId);
  
  next();
};

/**
 * 404处理中间件 - 处理未匹配的路由
 */
export const notFoundMiddleware = (req: RequestWithId, res: Response, _next: NextFunction) => {
  const error = new AppError(
    ErrorCode.RESOURCE_NOT_FOUND,
    `Route ${req.method} ${req.originalUrl} not found`
  );

  // 构建404响应
  const errorResponse: ErrorResponse = {
    success: false,
    error: {
      code: error.code,
      message: error.message,
      request_id: req.requestId
    },
    meta: {
      timestamp: new Date().toISOString(),
      request_id: req.requestId,
      version: process.env.npm_package_version || '1.0.0'
    }
  };

  res.set('X-Request-ID', req.requestId || uuidv4());
  res.status(404).json(errorResponse);
};

/**
 * 异步处理包装器 - 自动捕获异步路由中的错误
 */
export const asyncHandler = (fn: Function) => (req: Request, res: Response, next: NextFunction) => {
  Promise.resolve(fn(req, res, next)).catch(next);
};

/**
 * 验证错误处理器 - 将验证库的错误转换为统一格式
 */
export const validationErrorHandler = (validationResult: any): AppError => {
  if (validationResult.errors) {
    // 处理express-validator的错误格式
    const details = validationResult.errors.map((err: any) => ({
      field: err.param || err.path,
      message: err.msg || err.message,
      code: err.type || 'validation_error',
      value: err.value
    }));

    return ErrorFactory.multiFieldValidation(details);
  }

  // 处理其他验证库的错误格式
  return new AppError(ErrorCode.VALIDATION_ERROR, 'Validation failed');
};

// 导出默认错误处理中间件实例
export const errorHandler = new ErrorHandlerMiddleware();
export const errorMiddleware = errorHandler.createMiddleware();

// 导出便捷的错误抛出函数
export const throwError = {
  // 认证错误
  authFailed: (message?: string) => { throw new AppError(ErrorCode.AUTH_FAILED, message); },
  tokenExpired: (message?: string) => { throw new AppError(ErrorCode.TOKEN_EXPIRED, message); },
  tokenInvalid: (message?: string) => { throw new AppError(ErrorCode.TOKEN_INVALID, message); },
  permissionDenied: (message?: string) => { throw new AppError(ErrorCode.PERMISSION_DENIED, message); },

  // 验证错误
  validation: (message?: string, details?: any[]) => { throw new AppError(ErrorCode.VALIDATION_ERROR, message, details); },
  required: (field: string) => { throw new AppError(ErrorCode.REQUIRED_FIELD, `Field '${field}' is required`); },
  invalidFormat: (field: string, format: string) => { throw new AppError(ErrorCode.INVALID_FORMAT, `Invalid format for '${field}', expected: ${format}`); },

  // 业务错误
  notFound: (resource: string, id?: string | number) => { 
    const message = id ? `${resource} with ID '${id}' not found` : `${resource} not found`;
    throw new AppError(ErrorCode.RESOURCE_NOT_FOUND, message);
  },
  locked: (resource: string, reason?: string) => { 
    const message = reason ? `${resource} is locked: ${reason}` : `${resource} is locked`;
    throw new AppError(ErrorCode.RESOURCE_LOCKED, message);
  },
  quotaExceeded: (resource: string) => { throw new AppError(ErrorCode.QUOTA_EXCEEDED, `Quota exceeded for ${resource}`); },

  // 系统错误
  internal: (message?: string) => { throw new AppError(ErrorCode.INTERNAL_ERROR, message); },
  serviceUnavailable: (service?: string) => { 
    const message = service ? `Service ${service} is unavailable` : 'Service temporarily unavailable';
    throw new AppError(ErrorCode.SERVICE_UNAVAILABLE, message);
  },
  timeout: (operation?: string) => { 
    const message = operation ? `Operation ${operation} timed out` : 'Request timed out';
    throw new AppError(ErrorCode.TIMEOUT, message);
  }
};