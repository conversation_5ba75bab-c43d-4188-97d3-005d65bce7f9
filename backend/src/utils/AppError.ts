/**
 * 标准化应用程序错误类
 * 提供统一的错误处理和分类功能
 */

import { ErrorCode, ErrorDetail, ErrorLevel, ErrorCategory, HTTP_STATUS_CODES, ERROR_CATEGORIES, DEFAULT_ERROR_MESSAGES } from '../types/errors';

/**
 * 应用程序基础错误类
 */
export class AppError extends Error {
  public readonly code: ErrorCode;
  public readonly statusCode: number;
  public readonly level: ErrorLevel;
  public readonly category: ErrorCategory;
  public readonly details?: ErrorDetail[];
  public readonly isOperational: boolean;
  public readonly timestamp: Date;

  constructor(
    code: ErrorCode,
    message?: string,
    details?: ErrorDetail[],
    level: ErrorLevel = 'error',
    isOperational: boolean = true
  ) {
    // 使用提供的消息或默认消息
    super(message || DEFAULT_ERROR_MESSAGES[code]);
    
    this.name = 'AppError';
    this.code = code;
    this.statusCode = HTTP_STATUS_CODES[code];
    this.level = level;
    this.category = ERROR_CATEGORIES[code];
    this.details = details;
    this.isOperational = isOperational;
    this.timestamp = new Date();

    // 确保堆栈跟踪正确指向实际错误位置
    Error.captureStackTrace(this, AppError);
  }

  /**
   * 将错误转换为JSON格式
   */
  toJSON() {
    return {
      name: this.name,
      code: this.code,
      message: this.message,
      statusCode: this.statusCode,
      level: this.level,
      category: this.category,
      details: this.details,
      isOperational: this.isOperational,
      timestamp: this.timestamp.toISOString(),
      stack: this.stack
    };
  }

  /**
   * 检查是否为特定类型的错误
   */
  isCategory(category: ErrorCategory): boolean {
    return this.category === category;
  }

  /**
   * 检查是否为特定级别的错误
   */
  isLevel(level: ErrorLevel): boolean {
    return this.level === level;
  }

  /**
   * 静态工厂方法 - 认证错误
   */
  static auth(code: ErrorCode.AUTH_FAILED | ErrorCode.TOKEN_EXPIRED | ErrorCode.TOKEN_INVALID | ErrorCode.PERMISSION_DENIED, message?: string, details?: ErrorDetail[]): AppError {
    return new AppError(code, message, details, 'error');
  }

  /**
   * 静态工厂方法 - 验证错误
   */
  static validation(code: ErrorCode.VALIDATION_ERROR | ErrorCode.REQUIRED_FIELD | ErrorCode.INVALID_FORMAT | ErrorCode.DUPLICATE_ENTRY, message?: string, details?: ErrorDetail[]): AppError {
    return new AppError(code, message, details, 'warning');
  }

  /**
   * 静态工厂方法 - 业务错误
   */
  static business(code: ErrorCode.RESOURCE_NOT_FOUND | ErrorCode.RESOURCE_LOCKED | ErrorCode.QUOTA_EXCEEDED | ErrorCode.OPERATION_FAILED, message?: string, details?: ErrorDetail[]): AppError {
    return new AppError(code, message, details, 'error');
  }

  /**
   * 静态工厂方法 - 系统错误
   */
  static system(code: ErrorCode.INTERNAL_ERROR | ErrorCode.SERVICE_UNAVAILABLE | ErrorCode.TIMEOUT | ErrorCode.DATABASE_ERROR | ErrorCode.NETWORK_ERROR, message?: string, details?: ErrorDetail[]): AppError {
    return new AppError(code, message, details, 'critical');
  }
}

/**
 * 认证错误类
 */
export class AuthError extends AppError {
  constructor(code: ErrorCode.AUTH_FAILED | ErrorCode.TOKEN_EXPIRED | ErrorCode.TOKEN_INVALID | ErrorCode.PERMISSION_DENIED = ErrorCode.AUTH_FAILED, message?: string, details?: ErrorDetail[]) {
    super(code, message, details, 'error');
    this.name = 'AuthError';
  }
}

/**
 * 验证错误类
 */
export class ValidationError extends AppError {
  constructor(code: ErrorCode.VALIDATION_ERROR | ErrorCode.REQUIRED_FIELD | ErrorCode.INVALID_FORMAT | ErrorCode.DUPLICATE_ENTRY = ErrorCode.VALIDATION_ERROR, message?: string, details?: ErrorDetail[]) {
    super(code, message, details, 'warning');
    this.name = 'ValidationError';
  }

  /**
   * 创建字段验证错误
   */
  static field(field: string, message: string, code: string = 'invalid'): ValidationError {
    return new ValidationError(ErrorCode.VALIDATION_ERROR, `Validation failed for field: ${field}`, [
      { field, message, code }
    ]);
  }

  /**
   * 创建必填字段错误
   */
  static required(field: string): ValidationError {
    return new ValidationError(ErrorCode.REQUIRED_FIELD, `Field '${field}' is required`, [
      { field, message: `${field} is required`, code: 'required' }
    ]);
  }

  /**
   * 创建格式错误
   */
  static format(field: string, expectedFormat: string): ValidationError {
    return new ValidationError(ErrorCode.INVALID_FORMAT, `Invalid format for field '${field}', expected: ${expectedFormat}`, [
      { field, message: `Invalid format, expected: ${expectedFormat}`, code: 'invalid_format' }
    ]);
  }
}

/**
 * 业务逻辑错误类
 */
export class BusinessError extends AppError {
  constructor(code: ErrorCode.RESOURCE_NOT_FOUND | ErrorCode.RESOURCE_LOCKED | ErrorCode.QUOTA_EXCEEDED | ErrorCode.OPERATION_FAILED = ErrorCode.OPERATION_FAILED, message?: string, details?: ErrorDetail[]) {
    super(code, message, details, 'error');
    this.name = 'BusinessError';
  }

  /**
   * 资源未找到错误
   */
  static notFound(resource: string, identifier?: string | number): BusinessError {
    const message = identifier 
      ? `${resource} with ID '${identifier}' not found`
      : `${resource} not found`;
    
    return new BusinessError(ErrorCode.RESOURCE_NOT_FOUND, message);
  }

  /**
   * 资源锁定错误
   */
  static locked(resource: string, reason?: string): BusinessError {
    const message = reason 
      ? `${resource} is locked: ${reason}`
      : `${resource} is currently locked`;
    
    return new BusinessError(ErrorCode.RESOURCE_LOCKED, message);
  }

  /**
   * 配额超限错误
   */
  static quotaExceeded(resource: string, limit: number, current: number): BusinessError {
    return new BusinessError(
      ErrorCode.QUOTA_EXCEEDED, 
      `Quota exceeded for ${resource}: ${current}/${limit}`,
      [{ field: resource, message: `Limit: ${limit}, Current: ${current}`, code: 'quota_exceeded' }]
    );
  }
}

/**
 * 系统错误类
 */
export class SystemError extends AppError {
  constructor(code: ErrorCode.INTERNAL_ERROR | ErrorCode.SERVICE_UNAVAILABLE | ErrorCode.TIMEOUT | ErrorCode.DATABASE_ERROR | ErrorCode.NETWORK_ERROR = ErrorCode.INTERNAL_ERROR, message?: string, details?: ErrorDetail[]) {
    super(code, message, details, 'critical', false); // 系统错误不是操作性错误
    this.name = 'SystemError';
  }

  /**
   * 数据库错误
   */
  static database(operation: string, originalError?: Error): SystemError {
    return new SystemError(
      ErrorCode.DATABASE_ERROR, 
      `Database operation failed: ${operation}`,
      originalError ? [{ message: originalError.message, code: 'db_error' }] : undefined
    );
  }

  /**
   * 网络错误
   */
  static network(service: string, originalError?: Error): SystemError {
    return new SystemError(
      ErrorCode.NETWORK_ERROR, 
      `Network error connecting to ${service}`,
      originalError ? [{ message: originalError.message, code: 'network_error' }] : undefined
    );
  }

  /**
   * 服务不可用错误
   */
  static serviceUnavailable(service: string, reason?: string): SystemError {
    const message = reason 
      ? `Service ${service} is unavailable: ${reason}`
      : `Service ${service} is temporarily unavailable`;
    
    return new SystemError(ErrorCode.SERVICE_UNAVAILABLE, message);
  }

  /**
   * 超时错误
   */
  static timeout(operation: string, timeoutMs: number): SystemError {
    return new SystemError(
      ErrorCode.TIMEOUT, 
      `Operation '${operation}' timed out after ${timeoutMs}ms`
    );
  }
}

/**
 * 错误工厂类 - 便于统一创建错误
 */
export class ErrorFactory {
  /**
   * 从原生错误创建应用错误
   */
  static fromNativeError(error: Error): AppError {
    // 如果已经是应用错误，直接返回
    if (error instanceof AppError) {
      return error;
    }

    // 根据错误类型和信息推断错误类别
    if (error.name === 'ValidationError') {
      return new ValidationError(ErrorCode.VALIDATION_ERROR, error.message);
    }

    if (error.name === 'MongoError' || error.name === 'SequelizeError') {
      return SystemError.database('query', error);
    }

    if (error.name === 'TimeoutError') {
      return SystemError.timeout('request', 30000);
    }

    // 默认返回内部错误
    return new SystemError(ErrorCode.INTERNAL_ERROR, error.message);
  }

  /**
   * 从HTTP状态码创建错误
   */
  static fromHttpStatus(statusCode: number, message?: string): AppError {
    switch (statusCode) {
      case 400:
        return new ValidationError(ErrorCode.VALIDATION_ERROR, message);
      case 401:
        return new AuthError(ErrorCode.AUTH_FAILED, message);
      case 403:
        return new AuthError(ErrorCode.PERMISSION_DENIED, message);
      case 404:
        return BusinessError.notFound('Resource');
      case 409:
        return new ValidationError(ErrorCode.DUPLICATE_ENTRY, message);
      case 422:
        return new BusinessError(ErrorCode.OPERATION_FAILED, message);
      case 429:
        return new BusinessError(ErrorCode.QUOTA_EXCEEDED, message);
      case 500:
        return new SystemError(ErrorCode.INTERNAL_ERROR, message);
      case 502:
        return new SystemError(ErrorCode.NETWORK_ERROR, message);
      case 503:
        return new SystemError(ErrorCode.SERVICE_UNAVAILABLE, message);
      case 504:
        return new SystemError(ErrorCode.TIMEOUT, message);
      default:
        return new SystemError(ErrorCode.INTERNAL_ERROR, message || `HTTP ${statusCode} error`);
    }
  }

  /**
   * 创建多字段验证错误
   */
  static multiFieldValidation(errors: Array<{field: string, message: string, code?: string}>): ValidationError {
    const details: ErrorDetail[] = errors.map(err => ({
      field: err.field,
      message: err.message,
      code: err.code || 'invalid'
    }));

    return new ValidationError(
      ErrorCode.VALIDATION_ERROR, 
      `Validation failed for ${errors.length} field(s)`,
      details
    );
  }
}

// 导出便捷的错误创建函数
export const Errors = {
  // 认证错误
  authFailed: (message?: string) => new AuthError(ErrorCode.AUTH_FAILED, message),
  tokenExpired: (message?: string) => new AuthError(ErrorCode.TOKEN_EXPIRED, message),
  tokenInvalid: (message?: string) => new AuthError(ErrorCode.TOKEN_INVALID, message),
  permissionDenied: (message?: string) => new AuthError(ErrorCode.PERMISSION_DENIED, message),

  // 验证错误
  validation: (message?: string, details?: ErrorDetail[]) => new ValidationError(ErrorCode.VALIDATION_ERROR, message, details),
  required: (field: string) => ValidationError.required(field),
  format: (field: string, expectedFormat: string) => ValidationError.format(field, expectedFormat),
  duplicate: (resource: string) => new ValidationError(ErrorCode.DUPLICATE_ENTRY, `${resource} already exists`),

  // 业务错误
  notFound: (resource: string, id?: string | number) => BusinessError.notFound(resource, id),
  locked: (resource: string, reason?: string) => BusinessError.locked(resource, reason),
  quotaExceeded: (resource: string, limit: number, current: number) => BusinessError.quotaExceeded(resource, limit, current),
  operationFailed: (operation: string, reason?: string) => new BusinessError(ErrorCode.OPERATION_FAILED, reason ? `${operation} failed: ${reason}` : `${operation} failed`),

  // 系统错误
  internal: (message?: string) => new SystemError(ErrorCode.INTERNAL_ERROR, message),
  database: (operation: string, originalError?: Error) => SystemError.database(operation, originalError),
  network: (service: string, originalError?: Error) => SystemError.network(service, originalError),
  serviceUnavailable: (service: string, reason?: string) => SystemError.serviceUnavailable(service, reason),
  timeout: (operation: string, timeoutMs: number = 30000) => SystemError.timeout(operation, timeoutMs)
};