/**
 * 日志系统类型定义
 * 定义日志级别、格式、传输方式和配置选项
 */

import winston from 'winston';

// 日志级别枚举
export enum LogLevel {
  ERROR = 'error',
  WARN = 'warn', 
  INFO = 'info',
  HTTP = 'http',
  VERBOSE = 'verbose',
  DEBUG = 'debug',
  SILLY = 'silly'
}

// 日志格式类型
export enum LogFormat {
  JSON = 'json',
  SIMPLE = 'simple',
  COMBINED = 'combined',
  CUSTOM = 'custom'
}

// 传输方式类型
export enum TransportType {
  CONSOLE = 'console',
  FILE = 'file',
  DAILY_ROTATE = 'daily_rotate',
  HTTP = 'http',
  SYSLOG = 'syslog'
}

// 日志轮转配置
export interface LogRotationConfig {
  /** 文件名模式 */
  filename: string;
  /** 日期格式模式 */
  datePattern: string;
  /** 压缩旧文件 */
  zippedArchive: boolean;
  /** 最大文件大小 */
  maxSize: string;
  /** 最大保留文件数 */
  maxFiles: number | string;
  /** 最大保留天数 */
  maxRetentionDays?: number;
  /** 审计文件路径 */
  auditFile?: string;
  /** 频率 */
  frequency?: string;
  /** UTC时间 */
  utc?: boolean;
}

// 控制台传输配置
export interface ConsoleTransportConfig {
  /** 日志级别 */
  level: LogLevel;
  /** 是否处理异常 */
  handleExceptions: boolean;
  /** 是否处理拒绝 */
  handleRejections: boolean;
  /** 格式化选项 */
  format: LogFormat;
  /** 是否启用颜色 */
  colorize: boolean;
  /** 是否显示时间戳 */
  timestamp: boolean;
}

// 文件传输配置
export interface FileTransportConfig {
  /** 日志级别 */
  level: LogLevel;
  /** 文件路径 */
  filename: string;
  /** 最大文件大小 */
  maxsize: number;
  /** 最大保留文件数 */
  maxFiles: number;
  /** 是否压缩 */
  tailable: boolean;
  /** 是否处理异常 */
  handleExceptions: boolean;
  /** 是否处理拒绝 */
  handleRejections: boolean;
  /** 格式化选项 */
  format: LogFormat;
}

// HTTP传输配置
export interface HttpTransportConfig {
  /** 日志级别 */
  level: LogLevel;
  /** 服务器主机 */
  host: string;
  /** 服务器端口 */
  port: number;
  /** 请求路径 */
  path?: string;
  /** 是否使用SSL */
  ssl?: boolean;
  /** 身份验证 */
  auth?: {
    username: string;
    password: string;
  };
}

// 系统日志配置
export interface SyslogTransportConfig {
  /** 日志级别 */
  level: LogLevel;
  /** 系统日志主机 */
  host?: string;
  /** 系统日志端口 */
  port?: number;
  /** 应用程序标识 */
  app_name?: string;
  /** 设施 */
  facility?: string;
  /** 协议 */
  protocol?: 'tcp4' | 'tcp6' | 'udp4' | 'udp6';
}

// 日志传输配置联合类型
export type LogTransportConfig = 
  | ConsoleTransportConfig
  | FileTransportConfig  
  | LogRotationConfig
  | HttpTransportConfig
  | SyslogTransportConfig;

// 日志过滤器接口
export interface LogFilter {
  /** 过滤器名称 */
  name: string;
  /** 过滤条件 */
  condition: (info: winston.LogEntry) => boolean;
  /** 过滤器优先级 */
  priority?: number;
}

// 日志元数据接口
export interface LogMetadata {
  /** 请求ID */
  requestId?: string;
  /** 用户ID */
  userId?: string;
  /** 会话ID */
  sessionId?: string;
  /** IP地址 */
  ip?: string;
  /** 用户代理 */
  userAgent?: string;
  /** 模块名称 */
  module?: string;
  /** 函数名称 */
  function?: string;
  /** 执行时间 */
  duration?: number;
  /** 错误栈 */
  stack?: string;
  /** 额外数据 */
  extra?: Record<string, any>;
}

// 日志上下文接口
export interface LogContext {
  /** 跟踪ID */
  traceId?: string;
  /** 跨度ID */
  spanId?: string;
  /** 关联ID */
  correlationId?: string;
  /** 环境信息 */
  environment: string;
  /** 服务名称 */
  service: string;
  /** 版本信息 */
  version?: string;
  /** 主机名 */
  hostname?: string;
}

// 日志配置接口
export interface LoggerConfig {
  /** 默认日志级别 */
  level: LogLevel;
  /** 是否启用 */
  enabled: boolean;
  /** 日志格式 */
  format: LogFormat;
  /** 传输配置列表 */
  transports: Array<{
    type: TransportType;
    config: LogTransportConfig;
    enabled: boolean;
  }>;
  /** 全局元数据 */
  defaultMeta?: LogMetadata;
  /** 日志上下文 */
  context?: LogContext;
  /** 过滤器列表 */
  filters?: LogFilter[];
  /** 退出时处理 */
  exitOnError: boolean;
  /** 异常处理 */
  handleExceptions: boolean;
  /** 拒绝处理 */
  handleRejections: boolean;
  /** 性能监控 */
  performance?: {
    enabled: boolean;
    slowThreshold: number;
    includeArgs: boolean;
  };
  /** 采样配置 */
  sampling?: {
    enabled: boolean;
    rate: number;
    rules?: Array<{
      level: LogLevel;
      rate: number;
    }>;
  };
}

// HTTP日志配置接口
export interface HttpLogConfig {
  /** 是否启用 */
  enabled: boolean;
  /** 日志格式 */
  format: 'combined' | 'common' | 'dev' | 'short' | 'tiny' | 'custom';
  /** 自定义格式字符串 */
  customFormat?: string;
  /** 跳过条件 */
  skip?: (req: any, res: any) => boolean;
  /** 包含敏感数据 */
  includeSensitive: boolean;
  /** 最大请求体大小 */
  maxBodySize: number;
  /** 最大响应体大小 */
  maxResponseSize: number;
  /** 过滤器 */
  filters?: Array<{
    path: string;
    method?: string;
    skip: boolean;
  }>;
}

// 错误日志配置接口
export interface ErrorLogConfig {
  /** 是否启用 */
  enabled: boolean;
  /** 是否包含堆栈跟踪 */
  includeStack: boolean;
  /** 是否包含请求上下文 */
  includeRequestContext: boolean;
  /** 错误级别映射 */
  levelMapping: Record<string, LogLevel>;
  /** 敏感字段过滤 */
  sensitiveFields: string[];
  /** 错误通知 */
  notifications?: {
    enabled: boolean;
    channels: Array<'email' | 'slack' | 'webhook'>;
    threshold: LogLevel;
    cooldown: number;
  };
}

// 调试日志配置接口
export interface DebugLogConfig {
  /** 是否启用 */
  enabled: boolean;
  /** 调试命名空间 */
  namespaces: string[];
  /** 是否包含时间戳 */
  includeTimestamp: boolean;
  /** 是否包含文件信息 */
  includeFileInfo: boolean;
  /** 最大调试级别 */
  maxLevel: LogLevel;
  /** 调试输出目标 */
  targets: TransportType[];
}

// 性能日志配置接口
export interface PerformanceLogConfig {
  /** 是否启用 */
  enabled: boolean;
  /** 慢查询阈值(ms) */
  slowThreshold: number;
  /** 是否记录查询参数 */
  includeParams: boolean;
  /** 是否记录结果数量 */
  includeResultCount: boolean;
  /** 采样率 */
  samplingRate: number;
  /** 分析器 */
  profiler?: {
    enabled: boolean;
    includeMemory: boolean;
    includeCpu: boolean;
  };
}

// 安全日志配置接口
export interface SecurityLogConfig {
  /** 是否启用 */
  enabled: boolean;
  /** 审计事件 */
  auditEvents: string[];
  /** IP地址记录 */
  logIpAddress: boolean;
  /** 用户代理记录 */
  logUserAgent: boolean;
  /** 敏感操作记录 */
  logSensitiveOperations: boolean;
  /** 失败尝试跟踪 */
  trackFailedAttempts: boolean;
  /** 地理位置记录 */
  logGeolocation?: boolean;
}

// 日志查询接口
export interface LogQuery {
  /** 日志级别 */
  level?: LogLevel | LogLevel[];
  /** 开始时间 */
  from?: Date | string;
  /** 结束时间 */
  to?: Date | string;
  /** 搜索关键字 */
  search?: string;
  /** 模块过滤 */
  module?: string;
  /** 用户ID */
  userId?: string;
  /** 请求ID */
  requestId?: string;
  /** 页码 */
  page?: number;
  /** 每页数量 */
  limit?: number;
  /** 排序方式 */
  sort?: 'asc' | 'desc';
  /** 字段选择 */
  fields?: string[];
}

// 日志统计接口
export interface LogStats {
  /** 总数 */
  total: number;
  /** 按级别统计 */
  byLevel: Record<LogLevel, number>;
  /** 按模块统计 */
  byModule: Record<string, number>;
  /** 时间范围 */
  timeRange: {
    from: Date;
    to: Date;
  };
  /** 错误率 */
  errorRate: number;
  /** 最常见错误 */
  topErrors: Array<{
    message: string;
    count: number;
    lastOccurred: Date;
  }>;
}

// 导出所有类型
// 已在接口声明时导出，移除重复export type