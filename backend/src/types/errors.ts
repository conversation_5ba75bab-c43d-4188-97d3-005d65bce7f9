/**
 * 错误处理类型定义
 * 根据API设计文档定义统一的错误码体系和接口
 */

/**
 * 错误级别
 */
export type ErrorLevel = 'error' | 'warning' | 'critical';

/**
 * 错误分类
 */
export type ErrorCategory = 'auth' | 'validation' | 'business' | 'system';

/**
 * 标准化错误码枚举
 */
export enum ErrorCode {
  // 认证错误 (AUTH_*)
  AUTH_FAILED = 'AUTH_FAILED',
  TOKEN_EXPIRED = 'TOKEN_EXPIRED', 
  TOKEN_INVALID = 'TOKEN_INVALID',
  PERMISSION_DENIED = 'PERMISSION_DENIED',

  // 验证错误 (VALIDATION_*)
  VALIDATION_ERROR = 'VALIDATION_ERROR',
  REQUIRED_FIELD = 'REQUIRED_FIELD',
  INVALID_FORMAT = 'INVALID_FORMAT',
  DUPLICATE_ENTRY = 'DUPLICATE_ENTRY',

  // 业务错误 (BUSINESS_*)
  RESOURCE_NOT_FOUND = 'RESOURCE_NOT_FOUND',
  RESOURCE_LOCKED = 'RESOURCE_LOCKED',
  QUOTA_EXCEEDED = 'QUOTA_EXCEEDED',
  OPERATION_FAILED = 'OPERATION_FAILED',

  // 系统错误 (SYSTEM_*)
  INTERNAL_ERROR = 'INTERNAL_ERROR',
  SERVICE_UNAVAILABLE = 'SERVICE_UNAVAILABLE',
  TIMEOUT = 'TIMEOUT',
  DATABASE_ERROR = 'DATABASE_ERROR',
  NETWORK_ERROR = 'NETWORK_ERROR'
}

/**
 * 错误详情接口
 */
export interface ErrorDetail {
  field?: string;
  message: string;
  code?: string;
  value?: any;
}

/**
 * 错误响应接口 - 符合API设计规范
 */
export interface ErrorResponse {
  success: false;
  error: {
    code: ErrorCode;
    message: string;
    details?: ErrorDetail[];
    request_id?: string;
  };
  meta?: {
    timestamp: string;
    request_id?: string;
    version?: string;
  };
}

/**
 * 应用程序错误上下文
 */
export interface ErrorContext {
  requestId?: string;
  userId?: string;
  url?: string;
  method?: string;
  userAgent?: string;
  ip?: string;
  body?: any;
  params?: any;
  query?: any;
  headers?: Record<string, string>;
}

/**
 * 错误配置接口
 */
export interface ErrorConfig {
  // 是否在响应中包含错误详情
  includeDetails: boolean;
  // 是否在响应中包含堆栈信息（仅开发环境）
  includeStack: boolean;
  // 是否记录错误日志
  logErrors: boolean;
  // 敏感字段列表，用于清理
  sensitiveFields: string[];
  // 是否发送错误通知
  sendNotifications: boolean;
}

/**
 * HTTP状态码映射
 */
export const HTTP_STATUS_CODES: Record<ErrorCode, number> = {
  // 认证错误 4xx
  [ErrorCode.AUTH_FAILED]: 401,
  [ErrorCode.TOKEN_EXPIRED]: 401,
  [ErrorCode.TOKEN_INVALID]: 401,
  [ErrorCode.PERMISSION_DENIED]: 403,

  // 验证错误 4xx
  [ErrorCode.VALIDATION_ERROR]: 400,
  [ErrorCode.REQUIRED_FIELD]: 400,
  [ErrorCode.INVALID_FORMAT]: 400,
  [ErrorCode.DUPLICATE_ENTRY]: 409,

  // 业务错误 4xx
  [ErrorCode.RESOURCE_NOT_FOUND]: 404,
  [ErrorCode.RESOURCE_LOCKED]: 409,
  [ErrorCode.QUOTA_EXCEEDED]: 429,
  [ErrorCode.OPERATION_FAILED]: 422,

  // 系统错误 5xx
  [ErrorCode.INTERNAL_ERROR]: 500,
  [ErrorCode.SERVICE_UNAVAILABLE]: 503,
  [ErrorCode.TIMEOUT]: 504,
  [ErrorCode.DATABASE_ERROR]: 500,
  [ErrorCode.NETWORK_ERROR]: 502
};

/**
 * 错误分类映射
 */
export const ERROR_CATEGORIES: Record<ErrorCode, ErrorCategory> = {
  // 认证错误
  [ErrorCode.AUTH_FAILED]: 'auth',
  [ErrorCode.TOKEN_EXPIRED]: 'auth',
  [ErrorCode.TOKEN_INVALID]: 'auth',
  [ErrorCode.PERMISSION_DENIED]: 'auth',

  // 验证错误
  [ErrorCode.VALIDATION_ERROR]: 'validation',
  [ErrorCode.REQUIRED_FIELD]: 'validation',
  [ErrorCode.INVALID_FORMAT]: 'validation',
  [ErrorCode.DUPLICATE_ENTRY]: 'validation',

  // 业务错误
  [ErrorCode.RESOURCE_NOT_FOUND]: 'business',
  [ErrorCode.RESOURCE_LOCKED]: 'business',
  [ErrorCode.QUOTA_EXCEEDED]: 'business',
  [ErrorCode.OPERATION_FAILED]: 'business',

  // 系统错误
  [ErrorCode.INTERNAL_ERROR]: 'system',
  [ErrorCode.SERVICE_UNAVAILABLE]: 'system',
  [ErrorCode.TIMEOUT]: 'system',
  [ErrorCode.DATABASE_ERROR]: 'system',
  [ErrorCode.NETWORK_ERROR]: 'system'
};

/**
 * 默认错误消息
 */
export const DEFAULT_ERROR_MESSAGES: Record<ErrorCode, string> = {
  // 认证错误
  [ErrorCode.AUTH_FAILED]: 'Authentication failed',
  [ErrorCode.TOKEN_EXPIRED]: 'Token has expired',
  [ErrorCode.TOKEN_INVALID]: 'Invalid token',
  [ErrorCode.PERMISSION_DENIED]: 'Permission denied',

  // 验证错误
  [ErrorCode.VALIDATION_ERROR]: 'Validation failed',
  [ErrorCode.REQUIRED_FIELD]: 'Required field is missing',
  [ErrorCode.INVALID_FORMAT]: 'Invalid format',
  [ErrorCode.DUPLICATE_ENTRY]: 'Duplicate entry',

  // 业务错误
  [ErrorCode.RESOURCE_NOT_FOUND]: 'Resource not found',
  [ErrorCode.RESOURCE_LOCKED]: 'Resource is locked',
  [ErrorCode.QUOTA_EXCEEDED]: 'Quota exceeded',
  [ErrorCode.OPERATION_FAILED]: 'Operation failed',

  // 系统错误
  [ErrorCode.INTERNAL_ERROR]: 'Internal server error',
  [ErrorCode.SERVICE_UNAVAILABLE]: 'Service temporarily unavailable',
  [ErrorCode.TIMEOUT]: 'Request timeout',
  [ErrorCode.DATABASE_ERROR]: 'Database error',
  [ErrorCode.NETWORK_ERROR]: 'Network error'
};

/**
 * 中文错误消息映射（可选，支持国际化）
 */
export const CHINESE_ERROR_MESSAGES: Record<ErrorCode, string> = {
  // 认证错误
  [ErrorCode.AUTH_FAILED]: '认证失败',
  [ErrorCode.TOKEN_EXPIRED]: 'Token已过期',
  [ErrorCode.TOKEN_INVALID]: '无效的Token',
  [ErrorCode.PERMISSION_DENIED]: '权限不足',

  // 验证错误
  [ErrorCode.VALIDATION_ERROR]: '参数验证失败',
  [ErrorCode.REQUIRED_FIELD]: '必填字段缺失',
  [ErrorCode.INVALID_FORMAT]: '格式错误',
  [ErrorCode.DUPLICATE_ENTRY]: '重复条目',

  // 业务错误
  [ErrorCode.RESOURCE_NOT_FOUND]: '资源不存在',
  [ErrorCode.RESOURCE_LOCKED]: '资源被锁定',
  [ErrorCode.QUOTA_EXCEEDED]: '配额超限',
  [ErrorCode.OPERATION_FAILED]: '操作失败',

  // 系统错误
  [ErrorCode.INTERNAL_ERROR]: '服务器内部错误',
  [ErrorCode.SERVICE_UNAVAILABLE]: '服务暂不可用',
  [ErrorCode.TIMEOUT]: '请求超时',
  [ErrorCode.DATABASE_ERROR]: '数据库错误',
  [ErrorCode.NETWORK_ERROR]: '网络错误'
};