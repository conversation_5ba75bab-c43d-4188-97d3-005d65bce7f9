/**
 * 环境变量配置类型定义
 * 用于类型安全的配置管理
 */

export interface DatabaseConfig {
  host: string;
  port: number;
  name: string;
  user: string;
  password: string;
  charset: string;
  collation: string;
  connectionLimit: number;
  acquireTimeout: number;
  timeout: number;
}

export interface RedisConfig {
  host: string;
  port: number;
  password?: string;
  db: number;
  connectTimeout: number;
  commandTimeout: number;
  retryDelay: number;
}

export interface JWTConfig {
  secret: string;
  expiresIn: string;
  refreshExpiresIn: string;
  issuer: string;
  audience: string;
}

export interface GeminiConfig {
  apiEndpoint: string;
  apiKey: string;
  model: string;
  maxTokens: number;
  temperature: number;
  timeout: number;
}

export interface FileUploadConfig {
  uploadDir: string;
  maxFileSize: number;
  allowedFileTypes: string[];
  urlPrefix: string;
  cdnEnabled: boolean;
  cdnBaseUrl?: string;
  cdnBucket?: string;
}

export interface EmailConfig {
  host: string;
  port: number;
  secure: boolean;
  user: string;
  password: string;
  fromName: string;
  fromAddress: string;
}

export interface SecurityConfig {
  corsOrigin: string[];
  corsCredentials: boolean;
  rateLimitWindowMs: number;
  rateLimitMaxRequests: number;
  rateLimitSkipSuccess: boolean;
  sessionSecret: string;
  sessionMaxAge: number;
}

export interface LoggingConfig {
  level: string;
  file: string;
  maxSize: string;
  maxFiles: number;
  datePattern: string;
  console: {
    enabled: boolean;
    level: string;
    colorize: boolean;
  };
  fileRotation: {
    enabled: boolean;
    filename: string;
    datePattern: string;
    maxSize: string;
    maxFiles: number | string;
    zippedArchive: boolean;
  };
  http: {
    enabled: boolean;
    format: string;
    skip?: (req: any, res: any) => boolean;
  };
  error: {
    enabled: boolean;
    filename: string;
    includeStack: boolean;
    maxSize: string;
    maxFiles: number;
  };
  debug: {
    enabled: boolean;
    namespace: string;
  };
  performance: {
    enabled: boolean;
    slowThreshold: number;
  };
}

export interface SiteConfig {
  en: {
    domain: string;
    name: string;
    description: string;
  };
  de: {
    domain: string;
    name: string;
    description: string;
  };
  ru: {
    domain: string;
    name: string;
    description: string;
  };
  admin: {
    domain: string;
    name: string;
    secretKey: string;
  };
}

export interface SEOConfig {
  sitemapBaseUrl: string;
  robotsAllowAll: boolean;
  metaDefaultKeywords: string[];
  analyticsId?: string;
}

export interface ThirdPartyConfig {
  googleAnalytics?: {
    trackingId: string;
    debug: boolean;
  };
  sentry?: {
    dsn: string;
    environment: string;
  };
  cloudflare?: {
    zoneId: string;
    apiToken: string;
  };
}

export interface DevelopmentConfig {
  debug: boolean;
  debugNamespace: string;
  hotReload: boolean;
  apiDocsEnabled: boolean;
  apiDocsPath: string;
  testDbName?: string;
  testDbUser?: string;
  testDbPassword?: string;
}

/**
 * 应用程序完整配置接口
 */
export interface AppConfig {
  // 基础环境
  nodeEnv: 'development' | 'test' | 'production';
  port: number;
  apiPrefix: string;
  
  // 核心服务配置
  database: DatabaseConfig;
  redis: RedisConfig;
  jwt: JWTConfig;
  gemini: GeminiConfig;
  
  // 功能配置
  fileUpload: FileUploadConfig;
  email?: EmailConfig;
  security: SecurityConfig;
  logging: LoggingConfig;
  
  // 站点配置
  sites: SiteConfig;
  seo: SEOConfig;
  
  // 第三方服务
  thirdParty: ThirdPartyConfig;
  
  // 开发环境配置
  development: DevelopmentConfig;
}

/**
 * 环境变量验证规则
 */
export interface EnvValidationRules {
  required: string[];
  optional: string[];
  numeric: string[];
  boolean: string[];
  array: string[];
  encrypted?: string[];
}

/**
 * 配置加载选项
 */
export interface ConfigLoadOptions {
  envFile?: string;
  validateRequired?: boolean;
  decryptSecrets?: boolean;
  cacheConfig?: boolean;
}

/**
 * 配置错误类型
 */
export class ConfigError extends Error {
  constructor(
    message: string,
    public field?: string,
    public value?: any
  ) {
    super(message);
    this.name = 'ConfigError';
  }
}