/**
 * 环境变量加密和安全管理模块
 * 用于处理敏感信息的加密存储和解密
 */

import crypto from 'crypto';
import { ConfigError } from '../types/config';

// 加密算法配置
const ENCRYPTION_ALGORITHM = 'aes-256-gcm';
const KEY_LENGTH = 32; // 256 bits
const IV_LENGTH = 16;  // 128 bits for GCM
const SALT_LENGTH = 32; // 256 bits

/**
 * 从主密钥派生加密密钥
 */
function deriveKey(masterKey: string, salt: Buffer): Buffer {
  return crypto.pbkdf2Sync(masterKey, salt, 100000, KEY_LENGTH, 'sha256');
}

/**
 * 生成主密钥（用于首次设置）
 */
export function generateMasterKey(): string {
  return crypto.randomBytes(32).toString('hex');
}

/**
 * 加密敏感字符串
 */
export function encryptSecret(plaintext: string, masterKey?: string): string {
  if (!masterKey) {
    // 如果没有提供主密钥，从环境变量或生成一个
    masterKey = process.env.ENCRYPTION_MASTER_KEY || generateMasterKey();
  }
  
  try {
    const salt = crypto.randomBytes(SALT_LENGTH);
    const key = deriveKey(masterKey, salt);
    const iv = crypto.randomBytes(IV_LENGTH);
    
    const cipher = crypto.createCipheriv(ENCRYPTION_ALGORITHM, key, iv);
    cipher.setAAD(salt); // 使用salt作为额外认证数据
    
    let encrypted = cipher.update(plaintext, 'utf8', 'hex');
    encrypted += cipher.final('hex');
    
    const tag = cipher.getAuthTag();
    
    // 格式: salt:iv:tag:encrypted
    return [
      salt.toString('hex'),
      iv.toString('hex'), 
      tag.toString('hex'),
      encrypted
    ].join(':');
  } catch (error) {
    throw new ConfigError(`Failed to encrypt secret: ${(error as Error).message}`);
  }
}

/**
 * 解密敏感字符串
 */
export function decryptSecret(encryptedData: string, masterKey?: string): string {
  if (!masterKey) {
    masterKey = process.env.ENCRYPTION_MASTER_KEY;
    if (!masterKey) {
      throw new ConfigError('Master key required for decryption');
    }
  }
  
  try {
    const parts = encryptedData.split(':');
    if (parts.length !== 4) {
      throw new Error('Invalid encrypted data format');
    }
    
    const [saltHex, ivHex, tagHex, encrypted] = parts;
    const salt = Buffer.from(saltHex, 'hex');
    const iv = Buffer.from(ivHex, 'hex');
    const tag = Buffer.from(tagHex, 'hex');
    
    const key = deriveKey(masterKey, salt);
    
    const decipher = crypto.createDecipheriv(ENCRYPTION_ALGORITHM, key, iv);
    decipher.setAuthTag(tag);
    decipher.setAAD(salt);
    
    let decrypted = decipher.update(encrypted, 'hex', 'utf8');
    decrypted += decipher.final('utf8');
    
    return decrypted;
  } catch (error) {
    throw new ConfigError(`Failed to decrypt secret: ${(error as Error).message}`);
  }
}

/**
 * 检查字符串是否为加密格式
 */
export function isEncrypted(value: string): boolean {
  if (!value || typeof value !== 'string') return false;
  
  // 检查格式: hex:hex:hex:hex （四个十六进制字符串用冒号分隔）
  const parts = value.split(':');
  if (parts.length !== 4) return false;
  
  // 验证每部分都是有效的十六进制
  const hexPattern = /^[0-9a-fA-F]+$/;
  return parts.every(part => hexPattern.test(part));
}

/**
 * 安全地处理环境变量中的敏感信息
 */
export function processSecretEnvVar(
  key: string, 
  value: string | undefined, 
  shouldDecrypt = false,
  masterKey?: string
): string | undefined {
  if (!value) return value;
  
  // 如果值看起来已经加密且需要解密
  if (isEncrypted(value) && shouldDecrypt) {
    try {
      return decryptSecret(value, masterKey);
    } catch (error) {
      console.error(`⚠️  Failed to decrypt ${key}: ${(error as Error).message}`);
      // 在开发环境中，可能返回原值作为fallback
      if (process.env.NODE_ENV === 'development') {
        console.warn(`   Using unencrypted value for ${key} in development mode`);
        return value;
      }
      throw error;
    }
  }
  
  return value;
}

/**
 * 批量处理敏感环境变量
 */
export function processSecretEnvVars(
  env: NodeJS.ProcessEnv,
  encryptedFields: string[],
  shouldDecrypt = false,
  masterKey?: string
): Record<string, string | undefined> {
  const processed: Record<string, string | undefined> = {};
  
  for (const field of encryptedFields) {
    processed[field] = processSecretEnvVar(field, env[field], shouldDecrypt, masterKey);
  }
  
  return processed;
}

/**
 * 生成用于生产环境的加密配置文件
 */
export function generateEncryptedEnvTemplate(
  sourceEnv: NodeJS.ProcessEnv,
  encryptedFields: string[],
  masterKey?: string
): string {
  const lines: string[] = [
    '# 加密的环境变量配置文件',
    '# 使用 npm run decrypt-env 解密查看明文',
    '# 请妥善保管 ENCRYPTION_MASTER_KEY',
    '',
    `ENCRYPTION_MASTER_KEY=${masterKey || generateMasterKey()}`,
    ''
  ];
  
  // 处理每个环境变量
  Object.entries(sourceEnv).forEach(([key, value]) => {
    if (!value) return;
    
    if (encryptedFields.includes(key)) {
      try {
        const encrypted = encryptSecret(value, masterKey);
        lines.push(`${key}=${encrypted}`);
        lines.push(`# ${key}_ENCRYPTED=true`);
      } catch (error) {
        console.error(`Failed to encrypt ${key}: ${(error as Error).message}`);
        lines.push(`${key}=${value} # ENCRYPTION_FAILED`);
      }
    } else {
      lines.push(`${key}=${value}`);
    }
  });
  
  return lines.join('\n');
}

/**
 * 安全地显示配置信息（隐藏敏感数据）
 */
export function maskSensitiveConfig(config: any, sensitiveFields: string[]): any {
  const masked = JSON.parse(JSON.stringify(config));
  
  function maskObject(obj: any, path = ''): void {
    for (const [key, value] of Object.entries(obj)) {
      const fullPath = path ? `${path}.${key}` : key;
      
      if (sensitiveFields.includes(key) || 
          sensitiveFields.includes(fullPath.toLowerCase()) ||
          key.toLowerCase().includes('secret') ||
          key.toLowerCase().includes('password') ||
          key.toLowerCase().includes('token') ||
          key.toLowerCase().includes('key')) {
        
        if (typeof value === 'string' && value.length > 0) {
          obj[key] = `${value.substring(0, 4)}****${value.substring(value.length - 4)}`;
        }
      } else if (typeof value === 'object' && value !== null) {
        maskObject(value, fullPath);
      }
    }
  }
  
  maskObject(masked);
  return masked;
}

/**
 * 验证主密钥强度
 */
export function validateMasterKey(key: string): boolean {
  if (!key || key.length < 32) {
    return false;
  }
  
  // 检查是否为十六进制格式
  const hexPattern = /^[0-9a-fA-F]+$/;
  if (!hexPattern.test(key)) {
    return false;
  }
  
  return true;
}

/**
 * 安全清理内存中的敏感数据
 */
export function secureClearString(str: string): void {
  if (typeof str !== 'string') return;
  
  // Node.js中无法直接覆写字符串内存，但可以尝试垃圾回收
  // 这更多是一个信号表明我们关心安全
  str = '';
  if (global.gc) {
    global.gc();
  }
}