/**
 * 配置管理系统统一入口
 * 提供环境变量加载、验证、加密和配置管理功能
 */

// 核心配置模块
export { default as ConfigLoader, loadConfig, reloadConfig, getConfig, isConfigLoaded } from './loader';

// 配置验证模块
export { 
  validateAllEnvVars,
  validateRequiredEnvVars,
  validateNumericEnvVars,
  validateBooleanEnvVars,
  validateJWTSecrets,
  validateDatabaseConfig,
  validateRedisConfig,
  validateFileUploadConfig,
  validateEmailConfig,
  validateProductionSecurity,
  ENV_VALIDATION_RULES 
} from './validation';

// 加密和安全模块
export {
  generateMasterKey,
  encryptSecret,
  decryptSecret,
  isEncrypted,
  processSecretEnvVar,
  processSecretEnvVars,
  generateEncryptedEnvTemplate,
  maskSensitiveConfig,
  validateMasterKey,
  secureClearString
} from './encryption';

// 类型定义
export * from '../types/config';

/**
 * 配置管理类
 * 提供统一的配置访问接口
 */
import { AppConfig, ConfigLoadOptions, ConfigError } from '../types/config';
import ConfigLoader from './loader';

class ConfigManager {
  private static instance: ConfigManager;
  private config: AppConfig | null = null;
  
  private constructor() {}
  
  /**
   * 获取配置管理器单例
   */
  static getInstance(): ConfigManager {
    if (!ConfigManager.instance) {
      ConfigManager.instance = new ConfigManager();
    }
    return ConfigManager.instance;
  }
  
  /**
   * 初始化配置
   */
  async initialize(options: ConfigLoadOptions = {}): Promise<AppConfig> {
    try {
      console.log('🚀 Initializing configuration system...');
      this.config = ConfigLoader.load(options);
      console.log('✅ Configuration system initialized');
      return this.config;
    } catch (error) {
      console.error('❌ Failed to initialize configuration:', (error as Error).message);
      throw error;
    }
  }
  
  /**
   * 获取配置
   */
  get(): AppConfig {
    if (!this.config) {
      throw new ConfigError('Configuration not initialized. Call initialize() first.');
    }
    return this.config;
  }
  
  /**
   * 重新加载配置
   */
  async reload(options: ConfigLoadOptions = {}): Promise<AppConfig> {
    console.log('🔄 Reloading configuration...');
    this.config = ConfigLoader.reload(options);
    console.log('✅ Configuration reloaded');
    return this.config;
  }
  
  /**
   * 检查配置是否已初始化
   */
  isInitialized(): boolean {
    return this.config !== null;
  }
  
  /**
   * 获取数据库配置
   */
  getDatabase() {
    return this.get().database;
  }
  
  /**
   * 获取Redis配置
   */
  getRedis() {
    return this.get().redis;
  }
  
  /**
   * 获取JWT配置
   */
  getJWT() {
    return this.get().jwt;
  }
  
  /**
   * 获取Gemini配置
   */
  getGemini() {
    return this.get().gemini;
  }
  
  /**
   * 获取安全配置
   */
  getSecurity() {
    return this.get().security;
  }
  
  /**
   * 获取站点配置
   */
  getSites() {
    return this.get().sites;
  }
  
  /**
   * 获取特定语言的站点配置
   */
  getSite(language: 'en' | 'de' | 'ru' | 'admin') {
    return this.get().sites[language];
  }
  
  /**
   * 检查是否为生产环境
   */
  isProduction(): boolean {
    return this.get().nodeEnv === 'production';
  }
  
  /**
   * 检查是否为开发环境
   */
  isDevelopment(): boolean {
    return this.get().nodeEnv === 'development';
  }
  
  /**
   * 检查是否为测试环境
   */
  isTest(): boolean {
    return this.get().nodeEnv === 'test';
  }
  
  /**
   * 获取服务器端口
   */
  getPort(): number {
    return this.get().port;
  }
  
  /**
   * 获取API前缀
   */
  getApiPrefix(): string {
    return this.get().apiPrefix;
  }
  
  /**
   * 获取调试状态
   */
  isDebugMode(): boolean {
    return this.get().development.debug;
  }
  
  /**
   * 检查邮件服务是否配置
   */
  hasEmailConfig(): boolean {
    return this.get().email !== undefined;
  }
  
  /**
   * 检查CDN是否启用
   */
  isCDNEnabled(): boolean {
    return this.get().fileUpload.cdnEnabled;
  }
}

/**
 * 全局配置管理器实例
 */
export const configManager = ConfigManager.getInstance();

/**
 * 便捷的配置访问函数
 */
export const config = {
  /**
   * 初始化配置系统
   */
  init: (options?: ConfigLoadOptions) => configManager.initialize(options),
  
  /**
   * 获取完整配置
   */
  get: () => configManager.get(),
  
  /**
   * 重新加载配置
   */
  reload: (options?: ConfigLoadOptions) => configManager.reload(options),
  
  /**
   * 检查是否已初始化
   */
  isReady: () => configManager.isInitialized(),
  
  /**
   * 快速访问常用配置
   */
  db: () => configManager.getDatabase(),
  redis: () => configManager.getRedis(),
  jwt: () => configManager.getJWT(),
  gemini: () => configManager.getGemini(),
  security: () => configManager.getSecurity(),
  sites: () => configManager.getSites(),
  
  /**
   * 环境检查
   */
  isProd: () => configManager.isProduction(),
  isDev: () => configManager.isDevelopment(),
  isTest: () => configManager.isTest(),
  isDebug: () => configManager.isDebugMode(),
  
  /**
   * 服务器配置
   */
  port: () => configManager.getPort(),
  apiPrefix: () => configManager.getApiPrefix(),
  
  /**
   * 功能检查
   */
  hasEmail: () => configManager.hasEmailConfig(),
  hasCDN: () => configManager.isCDNEnabled()
};

/**
 * 默认导出配置对象
 */
export default config;