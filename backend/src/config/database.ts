/**
 * 数据库连接配置
 * 基于TypeORM实现MySQL连接池管理
 */

import 'reflect-metadata';
import { DataSource, DataSourceOptions } from 'typeorm';
import { config } from './index';
import { getLogger } from '../logger';

/**
 * TypeORM数据源配置选项
 */
export function getDatabaseConfig(): DataSourceOptions {
  const dbConfig = config.db();
  const environment = config.get().nodeEnv;
  
  return {
    type: 'mysql',
    host: dbConfig.host,
    port: dbConfig.port,
    username: dbConfig.user,
    password: dbConfig.password,
    database: dbConfig.name,
    charset: dbConfig.charset,
    entities: [
      environment === 'production' 
        ? 'dist/models/**/*.js' 
        : 'src/models/**/*.ts'
    ],
    migrations: [
      environment === 'production' 
        ? 'dist/migrations/**/*.js' 
        : 'migrations/**/*.ts'
    ],
    synchronize: false,
    logging: environment === 'development' ? ['error', 'warn', 'query'] : ['error'],
    extra: {
      connectionLimit: dbConfig.connectionLimit,
      acquireTimeout: dbConfig.acquireTimeout,
      timeout: dbConfig.timeout,
      reconnect: true,
      idleTimeout: 300000,
    },
    maxQueryExecutionTime: 10000,
    cache: {
      duration: 300000,
    },
  };
}

class DatabaseManager {
  private static instance: DatabaseManager;
  private dataSource: DataSource | null = null;
  private logger: any = null;

  private getLoggerInstance() {
    if (!this.logger) {
      this.logger = getLogger();
    }
    return this.logger;
  }
  
  private constructor() {}
  
  static getInstance(): DatabaseManager {
    if (!DatabaseManager.instance) {
      DatabaseManager.instance = new DatabaseManager();
    }
    return DatabaseManager.instance;
  }
  
  async initialize(): Promise<DataSource> {
    try {
      if (this.dataSource?.isInitialized) {
        this.getLoggerInstance().warn('Database already initialized');
        return this.dataSource;
      }
      
      this.getLoggerInstance().info('Initializing database connection...');
      
      const dbConfig = getDatabaseConfig();
      this.dataSource = new DataSource(dbConfig);
      
      await this.dataSource.initialize();
      
      this.getLoggerInstance().info('Database connection established successfully');
      
      return this.dataSource;
      
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      this.getLoggerInstance().error('Failed to initialize database connection: ' + errorMsg);
      throw error;
    }
  }
  
  getDataSource(): DataSource {
    if (!this.dataSource?.isInitialized) {
      throw new Error('Database not initialized. Call initialize() first.');
    }
    return this.dataSource;
  }
  
  isConnected(): boolean {
    return this.dataSource?.isInitialized || false;
  }
  
  getConnectionPoolStatus() {
    if (!this.dataSource?.isInitialized) {
      return { connected: false, status: 'Not initialized' };
    }
    
    return {
      connected: true,
      status: 'Connected',
      database: config.db().name,
      host: config.db().host
    };
  }
  
  async healthCheck() {
    try {
      if (!this.isConnected()) {
        return {
          status: 'unhealthy',
          message: 'Database not connected',
          details: { connected: false }
        };
      }
      
      const result = await this.dataSource!.query('SELECT 1 as test');
      
      return {
        status: 'healthy',
        message: 'Database connection is healthy',
        details: {
          connected: true,
          queryTest: result[0]?.test === 1,
          poolStatus: this.getConnectionPoolStatus()
        }
      };
      
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      this.getLoggerInstance().error('Database health check failed: ' + errorMsg);
      
      return {
        status: 'unhealthy',
        message: 'Database health check failed',
        details: {
          error: errorMsg,
          connected: false
        }
      };
    }
  }
  
  async close(): Promise<void> {
    try {
      if (!this.dataSource?.isInitialized) {
        this.getLoggerInstance().warn('Database not initialized, nothing to close');
        return;
      }
      
      this.getLoggerInstance().info('Closing database connection...');
      await this.dataSource.destroy();
      this.dataSource = null;
      this.getLoggerInstance().info('Database connection closed successfully');
      
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      this.getLoggerInstance().error('Failed to close database connection: ' + errorMsg);
      throw error;
    }
  }
  
  async reconnect(): Promise<DataSource> {
    this.getLoggerInstance().info('Reconnecting to database...');
    
    try {
      if (this.dataSource?.isInitialized) {
        await this.dataSource.destroy();
      }
      
      return await this.initialize();
      
    } catch (error) {
      const errorMsg = error instanceof Error ? error.message : String(error);
      this.getLoggerInstance().error('Failed to reconnect to database: ' + errorMsg);
      throw error;
    }
  }
}

export { DatabaseManager };
export const databaseManager = DatabaseManager.getInstance();

export const db = {
  init: () => databaseManager.initialize(),
  getDataSource: () => databaseManager.getDataSource(),
  isConnected: () => databaseManager.isConnected(),
  healthCheck: () => databaseManager.healthCheck(),
  close: () => databaseManager.close(),
  reconnect: () => databaseManager.reconnect(),
  getStatus: () => databaseManager.getConnectionPoolStatus()
};

export default db;
