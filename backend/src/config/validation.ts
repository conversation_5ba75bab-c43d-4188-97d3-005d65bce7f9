/**
 * 环境变量验证模块
 * 确保所有必需配置项存在且格式正确
 */

import { ConfigError, EnvValidationRules } from '../types/config';

/**
 * 环境变量验证规则定义
 */
export const ENV_VALIDATION_RULES: EnvValidationRules = {
  required: [
    // 基础环境
    'NODE_ENV',
    'PORT',
    'API_PREFIX',
    
    // 数据库配置 - 必需
    'DB_HOST',
    'DB_PORT',
    'DB_NAME',
    'DB_USER',
    'DB_PASSWORD',
    
    // Redis配置 - 必需
    'REDIS_HOST',
    'REDIS_PORT',
    
    // JWT配置 - 必需
    'JWT_SECRET',
    'JWT_EXPIRES_IN',
    
    // Gemini API - 必需
    'GEMINI_API_ENDPOINT',
    'GEMINI_API_KEY',
    'GEMINI_MODEL',
    
    // 安全配置
    'SESSION_SECRET',
    'CORS_ORIGIN',
    
    // 站点配置 - 基本信息
    'SITE_EN_DOMAIN',
    'SITE_EN_NAME',
    'SITE_DE_DOMAIN',
    'SITE_DE_NAME',
    'SITE_RU_DOMAIN',
    'SITE_RU_NAME',
    'ADMIN_DOMAIN',
    'ADMIN_NAME',
    'ADMIN_SECRET_KEY'
  ],
  
  optional: [
    // 数据库连接池
    'DB_CONNECTION_LIMIT',
    'DB_ACQUIRETIMEOUT',
    'DB_TIMEOUT',
    'DB_CHARSET',
    'DB_COLLATION',
    
    // Redis可选配置
    'REDIS_PASSWORD',
    'REDIS_DB',
    'REDIS_CONNECT_TIMEOUT',
    'REDIS_COMMAND_TIMEOUT',
    'REDIS_RETRY_DELAY',
    
    // JWT可选配置
    'JWT_REFRESH_EXPIRES_IN',
    'JWT_ISSUER',
    'JWT_AUDIENCE',
    
    // Gemini可选配置
    'GEMINI_MAX_TOKENS',
    'GEMINI_TEMPERATURE',
    'GEMINI_TIMEOUT',
    
    // 文件上传
    'UPLOAD_DIR',
    'MAX_FILE_SIZE',
    'ALLOWED_FILE_TYPES',
    'UPLOAD_URL_PREFIX',
    'CDN_ENABLED',
    'CDN_BASE_URL',
    'CDN_BUCKET',
    
    // 邮件服务
    'SMTP_HOST',
    'SMTP_PORT',
    'SMTP_SECURE',
    'SMTP_USER',
    'SMTP_PASS',
    'MAIL_FROM_NAME',
    'MAIL_FROM_ADDRESS',
    
    // 安全配置
    'CORS_CREDENTIALS',
    'RATE_LIMIT_WINDOW_MS',
    'RATE_LIMIT_MAX_REQUESTS',
    'RATE_LIMIT_SKIP_SUCCESS_REQUESTS',
    'SESSION_MAX_AGE',
    
    // 日志配置
    'LOG_LEVEL',
    'LOG_FILE',
    'LOG_MAX_SIZE',
    'LOG_MAX_FILES',
    'LOG_DATE_PATTERN',
    
    // 站点描述
    'SITE_EN_DESCRIPTION',
    'SITE_DE_DESCRIPTION',
    'SITE_RU_DESCRIPTION',
    
    // SEO配置
    'SITEMAP_BASE_URL',
    'ROBOTS_ALLOW_ALL',
    'META_DEFAULT_KEYWORDS',
    'ANALYTICS_ID',
    
    // 第三方服务
    'GA_TRACKING_ID',
    'GA_DEBUG',
    'SENTRY_DSN',
    'SENTRY_ENVIRONMENT',
    'CLOUDFLARE_ZONE_ID',
    'CLOUDFLARE_API_TOKEN',
    
    // 开发环境
    'DEBUG',
    'DEBUG_NAMESPACE',
    'HOT_RELOAD',
    'API_DOCS_ENABLED',
    'API_DOCS_PATH',
    'TEST_DB_NAME',
    'TEST_DB_USER',
    'TEST_DB_PASSWORD'
  ],
  
  numeric: [
    'PORT',
    'DB_PORT',
    'DB_CONNECTION_LIMIT',
    'DB_ACQUIRETIMEOUT',
    'DB_TIMEOUT',
    'REDIS_PORT',
    'REDIS_DB',
    'REDIS_CONNECT_TIMEOUT',
    'REDIS_COMMAND_TIMEOUT',
    'REDIS_RETRY_DELAY',
    'GEMINI_MAX_TOKENS',
    'GEMINI_TEMPERATURE',
    'GEMINI_TIMEOUT',
    'MAX_FILE_SIZE',
    'SMTP_PORT',
    'RATE_LIMIT_WINDOW_MS',
    'RATE_LIMIT_MAX_REQUESTS',
    'SESSION_MAX_AGE',
    'LOG_MAX_FILES'
  ],
  
  boolean: [
    'SMTP_SECURE',
    'CDN_ENABLED',
    'CORS_CREDENTIALS',
    'RATE_LIMIT_SKIP_SUCCESS_REQUESTS',
    'ROBOTS_ALLOW_ALL',
    'GA_DEBUG',
    'DEBUG',
    'HOT_RELOAD',
    'API_DOCS_ENABLED'
  ],
  
  array: [
    'CORS_ORIGIN',
    'ALLOWED_FILE_TYPES',
    'META_DEFAULT_KEYWORDS'
  ],
  
  encrypted: [
    'DB_PASSWORD',
    'JWT_SECRET',
    'JWT_REFRESH_SECRET',
    'GEMINI_API_KEY',
    'SESSION_SECRET',
    'ADMIN_SECRET_KEY',
    'SMTP_PASS',
    'CLOUDFLARE_API_TOKEN',
    'SENTRY_DSN'
  ]
};

/**
 * 验证必需的环境变量
 */
export function validateRequiredEnvVars(env: NodeJS.ProcessEnv): void {
  const missing: string[] = [];
  
  for (const key of ENV_VALIDATION_RULES.required) {
    if (!env[key] || env[key]?.trim() === '') {
      missing.push(key);
    }
  }
  
  if (missing.length > 0) {
    throw new ConfigError(
      `Missing required environment variables: ${missing.join(', ')}`,
      'validation',
      missing
    );
  }
}

/**
 * 验证数值型环境变量
 */
export function validateNumericEnvVars(env: NodeJS.ProcessEnv): void {
  const invalid: string[] = [];
  
  for (const key of ENV_VALIDATION_RULES.numeric) {
    const value = env[key];
    if (value && isNaN(Number(value))) {
      invalid.push(`${key}=${value}`);
    }
  }
  
  if (invalid.length > 0) {
    throw new ConfigError(
      `Invalid numeric environment variables: ${invalid.join(', ')}`,
      'validation',
      invalid
    );
  }
}

/**
 * 验证布尔型环境变量
 */
export function validateBooleanEnvVars(env: NodeJS.ProcessEnv): void {
  const validBooleanValues = ['true', 'false', '1', '0', 'yes', 'no', 'on', 'off'];
  const invalid: string[] = [];
  
  for (const key of ENV_VALIDATION_RULES.boolean) {
    const value = env[key]?.toLowerCase();
    if (value && !validBooleanValues.includes(value)) {
      invalid.push(`${key}=${env[key]}`);
    }
  }
  
  if (invalid.length > 0) {
    throw new ConfigError(
      `Invalid boolean environment variables: ${invalid.join(', ')}`,
      'validation',
      invalid
    );
  }
}

/**
 * 验证JWT密钥强度
 */
export function validateJWTSecrets(env: NodeJS.ProcessEnv): void {
  const secrets = ['JWT_SECRET', 'JWT_REFRESH_SECRET'];
  const weak: string[] = [];
  
  for (const key of secrets) {
    const secret = env[key];
    if (secret && secret.length < 32) {
      weak.push(key);
    }
  }
  
  if (weak.length > 0) {
    throw new ConfigError(
      `JWT secrets must be at least 32 characters long: ${weak.join(', ')}`,
      'security',
      weak
    );
  }
}

/**
 * 验证数据库连接配置
 */
export function validateDatabaseConfig(env: NodeJS.ProcessEnv): void {
  const dbPort = Number(env.DB_PORT);
  if (dbPort && (dbPort < 1 || dbPort > 65535)) {
    throw new ConfigError(
      `Invalid database port: ${dbPort}. Must be between 1-65535`,
      'DB_PORT',
      dbPort
    );
  }
  
  // 验证字符集配置
  const charset = env.DB_CHARSET;
  if (charset && !['utf8', 'utf8mb4', 'latin1'].includes(charset)) {
    console.warn(`Warning: Uncommon database charset '${charset}'. Consider using 'utf8mb4'`);
  }
}

/**
 * 验证Redis连接配置
 */
export function validateRedisConfig(env: NodeJS.ProcessEnv): void {
  const redisPort = Number(env.REDIS_PORT);
  if (redisPort && (redisPort < 1 || redisPort > 65535)) {
    throw new ConfigError(
      `Invalid Redis port: ${redisPort}. Must be between 1-65535`,
      'REDIS_PORT',
      redisPort
    );
  }
  
  const redisDb = Number(env.REDIS_DB || '0');
  if (redisDb < 0 || redisDb > 15) {
    throw new ConfigError(
      `Invalid Redis database number: ${redisDb}. Must be between 0-15`,
      'REDIS_DB',
      redisDb
    );
  }
}

/**
 * 验证文件上传配置
 */
export function validateFileUploadConfig(env: NodeJS.ProcessEnv): void {
  const maxSize = Number(env.MAX_FILE_SIZE);
  if (maxSize && maxSize > 100 * 1024 * 1024) { // 100MB
    console.warn(`Warning: Large max file size (${maxSize} bytes). Consider security implications.`);
  }
  
  const allowedTypes = env.ALLOWED_FILE_TYPES?.split(',') || [];
  const dangerousTypes = ['exe', 'php', 'js', 'html', 'htm'];
  const hasDangerous = allowedTypes.some(type => 
    dangerousTypes.includes(type.toLowerCase().replace('.', ''))
  );
  
  if (hasDangerous) {
    console.warn(`Warning: Potentially dangerous file types allowed: ${env.ALLOWED_FILE_TYPES}`);
  }
}

/**
 * 验证EMAIL配置（如果启用）
 */
export function validateEmailConfig(env: NodeJS.ProcessEnv): void {
  const hasEmailConfig = env.SMTP_HOST || env.SMTP_USER;
  if (!hasEmailConfig) return;
  
  const required = ['SMTP_HOST', 'SMTP_PORT', 'SMTP_USER', 'SMTP_PASS'];
  const missing = required.filter(key => !env[key]);
  
  if (missing.length > 0) {
    throw new ConfigError(
      `Incomplete email configuration. Missing: ${missing.join(', ')}`,
      'email',
      missing
    );
  }
  
  const smtpPort = Number(env.SMTP_PORT);
  if (smtpPort && ![25, 465, 587, 2525].includes(smtpPort)) {
    console.warn(`Warning: Uncommon SMTP port ${smtpPort}. Standard ports: 25, 465, 587, 2525`);
  }
}

/**
 * 验证生产环境安全配置
 */
export function validateProductionSecurity(env: NodeJS.ProcessEnv): void {
  if (env.NODE_ENV !== 'production') return;
  
  const warnings: string[] = [];
  
  // 检查调试模式
  if (env.DEBUG === 'true') {
    warnings.push('DEBUG mode enabled in production');
  }
  
  // 检查API文档
  if (env.API_DOCS_ENABLED === 'true') {
    warnings.push('API documentation enabled in production');
  }
  
  // 检查日志级别
  if (env.LOG_LEVEL === 'debug') {
    warnings.push('Debug log level in production may expose sensitive information');
  }
  
  // 检查默认密码
  const defaultPasswords = ['admin123456', 'password', '123456'];
  if (env.ADMIN_PASSWORD && defaultPasswords.includes(env.ADMIN_PASSWORD)) {
    warnings.push('Using default admin password');
  }
  
  if (warnings.length > 0) {
    console.warn('⚠️  Production Security Warnings:');
    warnings.forEach(warning => console.warn(`   - ${warning}`));
  }
}

/**
 * 执行完整的环境变量验证
 */
export function validateAllEnvVars(env: NodeJS.ProcessEnv = process.env): void {
  console.log('🔍 Validating environment configuration...');
  
  try {
    validateRequiredEnvVars(env);
    validateNumericEnvVars(env);
    validateBooleanEnvVars(env);
    validateJWTSecrets(env);
    validateDatabaseConfig(env);
    validateRedisConfig(env);
    validateFileUploadConfig(env);
    validateEmailConfig(env);
    validateProductionSecurity(env);
    
    console.log('✅ Environment configuration validation passed');
  } catch (error) {
    console.error('❌ Environment configuration validation failed:');
    throw error;
  }
}