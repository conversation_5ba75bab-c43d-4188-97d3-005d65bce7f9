/**
 * 配置系统测试脚本
 * 用于验证环境变量配置系统的功能
 */

import { config, validateAllEnvVars, encryptSecret, decryptSecret } from './index';
import { ConfigError } from '../types/config';

/**
 * 测试配置加载
 */
async function testConfigLoading(): Promise<void> {
  console.log('\n🧪 Testing configuration loading...');
  
  try {
    // 测试配置初始化
    await config.init();
    console.log('✅ Configuration initialized successfully');
    
    // 测试配置访问
    const appConfig = config.get();
    console.log('✅ Configuration retrieved successfully');
    
    // 测试环境检查
    console.log(`   Environment: ${appConfig.nodeEnv}`);
    console.log(`   Is Production: ${config.isProd()}`);
    console.log(`   Is Development: ${config.isDev()}`);
    console.log(`   Is Debug Mode: ${config.isDebug()}`);
    
    // 测试数据库配置
    const dbConfig = config.db();
    console.log(`   Database: ${dbConfig.host}:${dbConfig.port}/${dbConfig.name}`);
    console.log(`   DB User: ${dbConfig.user}`);
    console.log(`   DB Connection Limit: ${dbConfig.connectionLimit}`);
    
    // 测试Redis配置
    const redisConfig = config.redis();
    console.log(`   Redis: ${redisConfig.host}:${redisConfig.port}/${redisConfig.db}`);
    
    // 测试JWT配置
    const jwtConfig = config.jwt();
    console.log(`   JWT Expires In: ${jwtConfig.expiresIn}`);
    console.log(`   JWT Issuer: ${jwtConfig.issuer}`);
    
    // 测试Gemini配置
    const geminiConfig = config.gemini();
    console.log(`   Gemini Model: ${geminiConfig.model}`);
    console.log(`   Gemini Max Tokens: ${geminiConfig.maxTokens}`);
    
    // 测试站点配置
    const sitesConfig = config.sites();
    console.log(`   EN Site: ${sitesConfig.en.name} (${sitesConfig.en.domain})`);
    console.log(`   DE Site: ${sitesConfig.de.name} (${sitesConfig.de.domain})`);
    console.log(`   RU Site: ${sitesConfig.ru.name} (${sitesConfig.ru.domain})`);
    
    // 测试功能检查
    console.log(`   Has Email Config: ${config.hasEmail()}`);
    console.log(`   Has CDN Config: ${config.hasCDN()}`);
    
    console.log('✅ Configuration loading test passed');
  } catch (error) {
    console.error('❌ Configuration loading test failed:', (error as Error).message);
    throw error;
  }
}

/**
 * 测试环境变量验证
 */
function testValidation(): void {
  console.log('\n🧪 Testing environment validation...');
  
  try {
    // 测试当前环境变量验证
    validateAllEnvVars();
    console.log('✅ Environment validation passed');
    
    // 测试缺少必需变量的情况
    const testEnv = { ...process.env };
    delete testEnv.DB_HOST;
    
    try {
      validateAllEnvVars(testEnv);
      console.error('❌ Validation should have failed for missing DB_HOST');
    } catch (error) {
      if (error instanceof ConfigError) {
        console.log('✅ Validation correctly caught missing required variable');
      } else {
        throw error;
      }
    }
    
    console.log('✅ Environment validation test passed');
  } catch (error) {
    console.error('❌ Environment validation test failed:', (error as Error).message);
    throw error;
  }
}

/**
 * 测试加密功能
 */
function testEncryption(): void {
  console.log('\n🧪 Testing encryption functionality...');
  
  try {
    const testSecret = 'test_database_password_123';
    const masterKey = 'abcdef0123456789abcdef0123456789abcdef0123456789abcdef0123456789';
    
    // 测试加密
    const encrypted = encryptSecret(testSecret, masterKey);
    console.log(`   Original: ${testSecret}`);
    console.log(`   Encrypted: ${encrypted.substring(0, 50)}...`);
    
    // 测试解密
    const decrypted = decryptSecret(encrypted, masterKey);
    console.log(`   Decrypted: ${decrypted}`);
    
    // 验证加密解密一致性
    if (decrypted === testSecret) {
      console.log('✅ Encryption/decryption test passed');
    } else {
      throw new Error('Decrypted text does not match original');
    }
    
    // 测试无效密钥
    try {
      decryptSecret(encrypted, 'wrong_key');
      console.error('❌ Decryption should have failed with wrong key');
    } catch (error) {
      console.log('✅ Encryption correctly rejected wrong key');
    }
    
    console.log('✅ Encryption functionality test passed');
  } catch (error) {
    console.error('❌ Encryption test failed:', (error as Error).message);
    throw error;
  }
}

/**
 * 测试配置重载
 */
async function testConfigReload(): Promise<void> {
  console.log('\n🧪 Testing configuration reload...');
  
  try {
    const originalConfig = config.get();
    const originalPort = originalConfig.port;
    
    // 重新加载配置
    await config.reload();
    const reloadedConfig = config.get();
    
    if (reloadedConfig.port === originalPort) {
      console.log('✅ Configuration reload test passed');
    } else {
      throw new Error('Configuration changed unexpectedly after reload');
    }
  } catch (error) {
    console.error('❌ Configuration reload test failed:', (error as Error).message);
    throw error;
  }
}

/**
 * 测试错误处理
 */
function testErrorHandling(): void {
  console.log('\n🧪 Testing error handling...');
  
  try {
    // 测试配置错误类型
    const testError = new ConfigError('Test error message', 'test_field', 'test_value');
    console.log(`   Error name: ${testError.name}`);
    console.log(`   Error field: ${testError.field}`);
    console.log(`   Error value: ${testError.value}`);
    
    if (testError.name === 'ConfigError' && testError.field === 'test_field') {
      console.log('✅ ConfigError class working correctly');
    } else {
      throw new Error('ConfigError class not working as expected');
    }
    
    console.log('✅ Error handling test passed');
  } catch (error) {
    console.error('❌ Error handling test failed:', (error as Error).message);
    throw error;
  }
}

/**
 * 生成测试报告
 */
function generateTestReport(): void {
  console.log('\n📊 Configuration System Test Report');
  console.log('=====================================');
  
  const appConfig = config.get();
  
  console.log('Environment Information:');
  console.log(`  Node Environment: ${appConfig.nodeEnv}`);
  console.log(`  Server Port: ${appConfig.port}`);
  console.log(`  API Prefix: ${appConfig.apiPrefix}`);
  console.log(`  Debug Mode: ${appConfig.development.debug}`);
  
  console.log('\nDatabase Configuration:');
  console.log(`  Host: ${appConfig.database.host}`);
  console.log(`  Port: ${appConfig.database.port}`);
  console.log(`  Database: ${appConfig.database.name}`);
  console.log(`  Charset: ${appConfig.database.charset}`);
  console.log(`  Connection Limit: ${appConfig.database.connectionLimit}`);
  
  console.log('\nRedis Configuration:');
  console.log(`  Host: ${appConfig.redis.host}`);
  console.log(`  Port: ${appConfig.redis.port}`);
  console.log(`  Database: ${appConfig.redis.db}`);
  
  console.log('\nSecurity Configuration:');
  console.log(`  JWT Expires In: ${appConfig.jwt.expiresIn}`);
  console.log(`  Session Max Age: ${appConfig.security.sessionMaxAge / 1000 / 60 / 60}h`);
  console.log(`  Rate Limit: ${appConfig.security.rateLimitMaxRequests} requests per ${appConfig.security.rateLimitWindowMs / 1000}s`);
  
  console.log('\nFeature Configuration:');
  console.log(`  Email Service: ${appConfig.email ? 'Enabled' : 'Disabled'}`);
  console.log(`  CDN: ${appConfig.fileUpload.cdnEnabled ? 'Enabled' : 'Disabled'}`);
  console.log(`  API Docs: ${appConfig.development.apiDocsEnabled ? 'Enabled' : 'Disabled'}`);
  console.log(`  Hot Reload: ${appConfig.development.hotReload ? 'Enabled' : 'Disabled'}`);
  
  console.log('\n✅ All tests completed successfully!');
}

/**
 * 主测试函数
 */
async function runTests(): Promise<void> {
  console.log('🚀 Starting Configuration System Tests');
  console.log('=====================================');
  
  try {
    await testConfigLoading();
    testValidation();
    testEncryption();
    await testConfigReload();
    testErrorHandling();
    generateTestReport();
    
    console.log('\n🎉 All configuration tests passed!');
  } catch (error) {
    console.error('\n💥 Configuration test suite failed:');
    console.error(error);
    process.exit(1);
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runTests().catch(console.error);
}

export { runTests };