import { Options } from 'swagger-jsdoc';

/**
 * OpenAPI 3.0 配置 - 宠物博客站群系统API文档
 */
export const swaggerConfig: Options = {
  definition: {
    openapi: '3.0.0',
    info: {
      title: 'PetCare Blog API',
      version: '1.0.0',
      description: '宠物博客多语言站群系统的后端API服务',
      contact: {
        name: 'API Support',
        email: '<EMAIL>',
      },
      license: {
        name: 'ISC',
        url: 'https://opensource.org/licenses/ISC',
      },
    },
    servers: [
      {
        url: 'http://localhost:3000/api/v1',
        description: '开发环境',
      },
      {
        url: 'https://api.petcare.com/api/v1',
        description: '生产环境',
      },
    ],
    components: {
      securitySchemes: {
        bearerAuth: {
          type: 'http',
          scheme: 'bearer',
          bearerFormat: 'JWT',
          description: 'JWT Bearer Token认证',
        },
      },
      responses: {
        UnauthorizedError: {
          description: '认证失败',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: {
                    type: 'boolean',
                    example: false,
                  },
                  error: {
                    type: 'object',
                    properties: {
                      code: {
                        type: 'string',
                        example: 'TOKEN_EXPIRED',
                      },
                      message: {
                        type: 'string',
                        example: 'Your session has expired, please login again',
                      },
                    },
                  },
                },
              },
            },
          },
        },
        ValidationError: {
          description: '参数验证失败',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: {
                    type: 'boolean',
                    example: false,
                  },
                  error: {
                    type: 'object',
                    properties: {
                      code: {
                        type: 'string',
                        example: 'VALIDATION_ERROR',
                      },
                      message: {
                        type: 'string',
                        example: 'Validation failed',
                      },
                      details: {
                        type: 'array',
                        items: {
                          type: 'object',
                          properties: {
                            field: {
                              type: 'string',
                              example: 'title',
                            },
                            message: {
                              type: 'string',
                              example: 'Title is required',
                            },
                            code: {
                              type: 'string',
                              example: 'required',
                            },
                          },
                        },
                      },
                    },
                  },
                },
              },
            },
          },
        },
        InternalError: {
          description: '服务器内部错误',
          content: {
            'application/json': {
              schema: {
                type: 'object',
                properties: {
                  success: {
                    type: 'boolean',
                    example: false,
                  },
                  error: {
                    type: 'object',
                    properties: {
                      code: {
                        type: 'string',
                        example: 'INTERNAL_ERROR',
                      },
                      message: {
                        type: 'string',
                        example: 'An unexpected error occurred',
                      },
                      request_id: {
                        type: 'string',
                        example: 'req_xyz123',
                      },
                    },
                  },
                },
              },
            },
          },
        },
      },
      schemas: {
        // 基础响应结构
        SuccessResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: true,
            },
            data: {
              type: 'object',
              description: '响应数据',
            },
            meta: {
              type: 'object',
              properties: {
                timestamp: {
                  type: 'string',
                  format: 'date-time',
                  example: '2025-01-20T10:00:00.000Z',
                },
                version: {
                  type: 'string',
                  example: '1.0',
                },
              },
            },
          },
        },
        
        // 分页响应结构
        PaginatedResponse: {
          type: 'object',
          properties: {
            success: {
              type: 'boolean',
              example: true,
            },
            data: {
              type: 'array',
              items: {
                type: 'object',
              },
              description: '数据列表',
            },
            pagination: {
              type: 'object',
              properties: {
                total: {
                  type: 'integer',
                  example: 100,
                  description: '总记录数',
                },
                per_page: {
                  type: 'integer',
                  example: 20,
                  description: '每页数量',
                },
                current_page: {
                  type: 'integer',
                  example: 1,
                  description: '当前页码',
                },
                total_pages: {
                  type: 'integer',
                  example: 5,
                  description: '总页数',
                },
                has_next: {
                  type: 'boolean',
                  example: true,
                  description: '是否有下一页',
                },
                has_prev: {
                  type: 'boolean',
                  example: false,
                  description: '是否有上一页',
                },
              },
            },
            meta: {
              type: 'object',
              properties: {
                timestamp: {
                  type: 'string',
                  format: 'date-time',
                  example: '2025-01-20T10:00:00.000Z',
                },
              },
            },
          },
        },

        // 用户相关模型
        User: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              example: 1,
            },
            username: {
              type: 'string',
              example: 'admin',
            },
            email: {
              type: 'string',
              format: 'email',
              example: '<EMAIL>',
            },
            role: {
              type: 'string',
              enum: ['super_admin', 'admin', 'editor'],
              example: 'admin',
            },
            avatar: {
              type: 'string',
              format: 'url',
              example: 'https://cdn.petcare.com/avatars/admin.jpg',
            },
            created_at: {
              type: 'string',
              format: 'date-time',
              example: '2025-01-10T10:00:00.000Z',
            },
          },
        },

        // 登录请求
        LoginRequest: {
          type: 'object',
          required: ['email', 'password'],
          properties: {
            email: {
              type: 'string',
              format: 'email',
              example: '<EMAIL>',
            },
            password: {
              type: 'string',
              format: 'password',
              example: 'secure_password',
            },
          },
        },

        // 登录响应
        LoginResponse: {
          allOf: [
            {
              $ref: '#/components/schemas/SuccessResponse',
            },
            {
              type: 'object',
              properties: {
                data: {
                  type: 'object',
                  properties: {
                    access_token: {
                      type: 'string',
                      example: 'eyJhbGciOiJIUzI1NiIs...',
                    },
                    refresh_token: {
                      type: 'string',
                      example: 'eyJhbGciOiJIUzI1NiIs...',
                    },
                    token_type: {
                      type: 'string',
                      example: 'Bearer',
                    },
                    expires_in: {
                      type: 'integer',
                      example: 604800,
                    },
                    user: {
                      $ref: '#/components/schemas/User',
                    },
                  },
                },
              },
            },
          ],
        },

        // 分类模型
        Category: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              example: 3,
            },
            name: {
              type: 'string',
              example: 'Cat Care',
            },
            slug: {
              type: 'string',
              example: 'cat-care',
            },
            icon: {
              type: 'string',
              example: 'fa-cat',
            },
            parent_id: {
              type: 'integer',
              nullable: true,
              example: 1,
            },
            children: {
              type: 'array',
              items: {
                $ref: '#/components/schemas/Category',
              },
            },
          },
        },

        // 文章模型
        Article: {
          type: 'object',
          properties: {
            id: {
              type: 'integer',
              example: 123,
            },
            title: {
              type: 'string',
              example: '10 Tips for Cat Care',
            },
            slug: {
              type: 'string',
              example: '10-tips-for-cat-care',
            },
            content: {
              type: 'string',
              description: 'HTML格式的文章内容',
            },
            summary: {
              type: 'string',
              example: 'Essential tips for taking care of your cat',
            },
            featured_image: {
              type: 'string',
              format: 'url',
              example: 'https://cdn.petcare.com/images/cat-care.jpg',
            },
            meta_title: {
              type: 'string',
              example: '10 Essential Cat Care Tips | PetCare',
            },
            meta_description: {
              type: 'string',
              example: 'Discover the top 10 tips for taking care of your cat',
            },
            meta_keywords: {
              type: 'string',
              example: 'cat care, pet tips, cat health',
            },
            category: {
              $ref: '#/components/schemas/Category',
            },
            author: {
              $ref: '#/components/schemas/User',
            },
            tags: {
              type: 'array',
              items: {
                type: 'string',
              },
              example: ['cat-care', 'pet-health', 'tips'],
            },
            view_count: {
              type: 'integer',
              example: 1520,
            },
            comment_count: {
              type: 'integer',
              example: 23,
            },
            status: {
              type: 'string',
              enum: ['draft', 'published', 'archived'],
              example: 'published',
            },
            language_code: {
              type: 'string',
              example: 'en-US',
            },
            published_at: {
              type: 'string',
              format: 'date-time',
              nullable: true,
              example: '2025-01-15T08:00:00.000Z',
            },
            created_at: {
              type: 'string',
              format: 'date-time',
              example: '2025-01-10T10:00:00.000Z',
            },
            updated_at: {
              type: 'string',
              format: 'date-time',
              example: '2025-01-15T07:45:00.000Z',
            },
          },
        },

        // 创建文章请求
        CreateArticleRequest: {
          type: 'object',
          required: ['category_id', 'title', 'content', 'language_code'],
          properties: {
            category_id: {
              type: 'integer',
              example: 3,
            },
            title: {
              type: 'string',
              example: '新手养猫指南',
            },
            content: {
              type: 'string',
              example: '<p>详细的养猫指南内容...</p>',
            },
            summary: {
              type: 'string',
              example: '为新手猫主人准备的完整指南',
            },
            featured_image: {
              type: 'string',
              description: 'base64编码的图片或URL',
            },
            meta_title: {
              type: 'string',
              example: '新手养猫指南 - 完整攻略',
            },
            meta_description: {
              type: 'string',
              example: '最全面的新手养猫指南',
            },
            meta_keywords: {
              type: 'string',
              example: '养猫,新手,指南',
            },
            tags: {
              type: 'array',
              items: {
                type: 'string',
              },
              example: ['养猫', '新手指南'],
            },
            status: {
              type: 'string',
              enum: ['draft', 'published', 'archived'],
              default: 'draft',
              example: 'draft',
            },
            language_code: {
              type: 'string',
              example: 'zh-CN',
            },
          },
        },
      },
    },
    security: [
      {
        bearerAuth: [],
      },
    ],
    tags: [
      {
        name: 'Authentication',
        description: '用户认证相关接口',
      },
      {
        name: 'Articles',
        description: '文章管理接口',
      },
      {
        name: 'Categories',
        description: '分类管理接口',
      },
      {
        name: 'Comments',
        description: '评论管理接口',
      },
      {
        name: 'Media',
        description: '媒体文件管理接口',
      },
      {
        name: 'Translations',
        description: '翻译管理接口',
      },
      {
        name: 'Sites',
        description: '站点配置管理接口',
      },
      {
        name: 'Statistics',
        description: '统计数据接口',
      },
    ],
  },
  apis: [
    './src/routes/*.ts',
    './src/routes/**/*.ts',
    './src/controllers/*.ts',
    './src/controllers/**/*.ts',
    './src/index.ts',
  ],
};

/**
 * Swagger UI 配置选项
 */
export const swaggerUiOptions = {
  customCss: `
    .swagger-ui .topbar { display: none }
    .swagger-ui .info .title { color: #3b82f6; }
  `,
  customSiteTitle: 'PetCare API Documentation',
  swaggerOptions: {
    persistAuthorization: true,
    displayRequestDuration: true,
    filter: true,
    tryItOutEnabled: true,
  },
};