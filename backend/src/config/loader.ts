/**
 * 配置加载器模块
 * 负责从环境变量加载、验证和构建应用配置
 */

import { config as dotenvConfig } from 'dotenv';
import path from 'path';
import fs from 'fs';
import { 
  AppConfig, 
  ConfigLoadOptions, 
  ConfigError
} from '../types/config';
import { 
  validateAllEnvVars,
  ENV_VALIDATION_RULES
} from './validation';
import { 
  processSecretEnvVars,
  maskSensitiveConfig
} from './encryption';

/**
 * 缓存的配置实例
 */
let cachedConfig: AppConfig | null = null;

/**
 * 获取环境文件路径
 */
function getEnvFilePath(envFile?: string): string {
  if (envFile) {
    return path.isAbsolute(envFile) ? envFile : path.resolve(process.cwd(), envFile);
  }
  
  const nodeEnv = process.env.NODE_ENV || 'development';
  const possibleFiles = [
    `.env.${nodeEnv}.local`,
    `.env.${nodeEnv}`,
    '.env.local',
    '.env'
  ];
  
  // 查找第一个存在的环境文件
  for (const file of possibleFiles) {
    const fullPath = path.resolve(process.cwd(), file);
    if (fs.existsSync(fullPath)) {
      return fullPath;
    }
  }
  
  // 如果没找到，返回默认的.env路径
  return path.resolve(process.cwd(), '.env');
}

/**
 * 加载环境变量文件
 */
function loadEnvFile(envFile?: string): void {
  const envPath = getEnvFilePath(envFile);
  
  if (fs.existsSync(envPath)) {
    console.log(`📁 Loading environment from: ${envPath}`);
    const result = dotenvConfig({ path: envPath });
    
    if (result.error) {
      throw new ConfigError(`Failed to load environment file: ${result.error.message}`);
    }
  } else {
    console.warn(`⚠️  Environment file not found: ${envPath}`);
  }
}

/**
 * 解析布尔值环境变量
 */
function parseBoolean(value: string | undefined, defaultValue: boolean = false): boolean {
  if (!value) return defaultValue;
  
  const truthyValues = ['true', '1', 'yes', 'on'];
  const falsyValues = ['false', '0', 'no', 'off'];
  
  const lowerValue = value.toLowerCase().trim();
  
  if (truthyValues.includes(lowerValue)) return true;
  if (falsyValues.includes(lowerValue)) return false;
  
  return defaultValue;
}

/**
 * 解析数值环境变量
 */
function parseNumber(value: string | undefined, defaultValue: number): number {
  if (!value) return defaultValue;
  
  const parsed = Number(value);
  return isNaN(parsed) ? defaultValue : parsed;
}

/**
 * 解析数组环境变量（逗号分隔）
 */
function parseArray(value: string | undefined, defaultValue: string[] = []): string[] {
  if (!value) return defaultValue;
  
  return value
    .split(',')
    .map(item => item.trim())
    .filter(item => item.length > 0);
}

/**
 * 处理敏感配置项
 */
function processSecrets(
  env: NodeJS.ProcessEnv, 
  shouldDecrypt: boolean,
  masterKey?: string
): Record<string, string | undefined> {
  const encryptedFields = ENV_VALIDATION_RULES.encrypted || [];
  return processSecretEnvVars(env, encryptedFields, shouldDecrypt, masterKey);
}

/**
 * 构建应用配置对象
 */
function buildAppConfig(env: NodeJS.ProcessEnv, secrets: Record<string, string | undefined>): AppConfig {
  const nodeEnv = (env.NODE_ENV || 'development') as 'development' | 'test' | 'production';
  
  const config: AppConfig = {
    // 基础环境配置
    nodeEnv,
    port: parseNumber(env.PORT, 3000),
    apiPrefix: env.API_PREFIX || '/api/v1',
    
    // 数据库配置
    database: {
      host: env.DB_HOST!,
      port: parseNumber(env.DB_PORT, 3306),
      name: env.DB_NAME!,
      user: env.DB_USER!,
      password: secrets.DB_PASSWORD || env.DB_PASSWORD!,
      charset: env.DB_CHARSET || 'utf8mb4',
      collation: env.DB_COLLATION || 'utf8mb4_unicode_ci',
      connectionLimit: parseNumber(env.DB_CONNECTION_LIMIT, 10),
      acquireTimeout: parseNumber(env.DB_ACQUIRETIMEOUT, 10000),
      timeout: parseNumber(env.DB_TIMEOUT, 5000)
    },
    
    // Redis配置
    redis: {
      host: env.REDIS_HOST || 'localhost',
      port: parseNumber(env.REDIS_PORT, 6379),
      password: secrets.REDIS_PASSWORD || env.REDIS_PASSWORD,
      db: parseNumber(env.REDIS_DB, 0),
      connectTimeout: parseNumber(env.REDIS_CONNECT_TIMEOUT, 5000),
      commandTimeout: parseNumber(env.REDIS_COMMAND_TIMEOUT, 2000),
      retryDelay: parseNumber(env.REDIS_RETRY_DELAY, 100)
    },
    
    // JWT配置
    jwt: {
      secret: secrets.JWT_SECRET || env.JWT_SECRET!,
      expiresIn: env.JWT_EXPIRES_IN || '7d',
      refreshExpiresIn: env.JWT_REFRESH_EXPIRES_IN || '30d',
      issuer: env.JWT_ISSUER || 'petcare-blog',
      audience: env.JWT_AUDIENCE || 'petcare-users'
    },
    
    // Gemini AI配置
    gemini: {
      apiEndpoint: env.GEMINI_API_ENDPOINT!,
      apiKey: secrets.GEMINI_API_KEY || env.GEMINI_API_KEY!,
      model: env.GEMINI_MODEL || 'gemini-2.5-pro',
      maxTokens: parseNumber(env.GEMINI_MAX_TOKENS, 8192),
      temperature: parseNumber(env.GEMINI_TEMPERATURE, 0.7),
      timeout: parseNumber(env.GEMINI_TIMEOUT, 30000)
    },
    
    // 文件上传配置
    fileUpload: {
      uploadDir: env.UPLOAD_DIR || './uploads',
      maxFileSize: parseNumber(env.MAX_FILE_SIZE, 10 * 1024 * 1024), // 10MB
      allowedFileTypes: parseArray(env.ALLOWED_FILE_TYPES, ['jpg', 'jpeg', 'png', 'gif', 'webp']),
      urlPrefix: env.UPLOAD_URL_PREFIX || '/uploads',
      cdnEnabled: parseBoolean(env.CDN_ENABLED, false),
      cdnBaseUrl: env.CDN_BASE_URL,
      cdnBucket: env.CDN_BUCKET
    },
    
    // 邮件配置（可选）
    email: env.SMTP_HOST ? {
      host: env.SMTP_HOST,
      port: parseNumber(env.SMTP_PORT, 587),
      secure: parseBoolean(env.SMTP_SECURE, false),
      user: env.SMTP_USER!,
      password: secrets.SMTP_PASS || env.SMTP_PASS!,
      fromName: env.MAIL_FROM_NAME || 'PetCare Blog',
      fromAddress: env.MAIL_FROM_ADDRESS || '<EMAIL>'
    } : undefined,
    
    // 安全配置
    security: {
      corsOrigin: parseArray(env.CORS_ORIGIN, ['http://localhost:4321']),
      corsCredentials: parseBoolean(env.CORS_CREDENTIALS, true),
      rateLimitWindowMs: parseNumber(env.RATE_LIMIT_WINDOW_MS, 60000),
      rateLimitMaxRequests: parseNumber(env.RATE_LIMIT_MAX_REQUESTS, 100),
      rateLimitSkipSuccess: parseBoolean(env.RATE_LIMIT_SKIP_SUCCESS_REQUESTS, false),
      sessionSecret: secrets.SESSION_SECRET || env.SESSION_SECRET!,
      sessionMaxAge: parseNumber(env.SESSION_MAX_AGE, 24 * 60 * 60 * 1000) // 24小时
    },
    
    // 日志配置
    logging: {
      level: env.LOG_LEVEL || (nodeEnv === 'production' ? 'info' : 'debug'),
      file: env.LOG_FILE || './logs/app.log',
      maxSize: env.LOG_MAX_SIZE || '10m',
      maxFiles: parseNumber(env.LOG_MAX_FILES, 5),
      datePattern: env.LOG_DATE_PATTERN || 'YYYY-MM-DD',
      console: {
        enabled: parseBoolean(env.LOG_CONSOLE_ENABLED, nodeEnv !== 'production'),
        level: env.LOG_CONSOLE_LEVEL || (nodeEnv === 'production' ? 'info' : 'debug'),
        colorize: parseBoolean(env.LOG_CONSOLE_COLORIZE, nodeEnv === 'development')
      },
      fileRotation: {
        enabled: parseBoolean(env.LOG_ROTATION_ENABLED, true),
        filename: env.LOG_ROTATION_FILENAME || './logs/app-%DATE%.log',
        datePattern: env.LOG_ROTATION_DATE_PATTERN || 'YYYY-MM-DD',
        maxSize: env.LOG_ROTATION_MAX_SIZE || '20m',
        maxFiles: env.LOG_ROTATION_MAX_FILES || '30d',
        zippedArchive: parseBoolean(env.LOG_ROTATION_ZIPPED, true)
      },
      http: {
        enabled: parseBoolean(env.LOG_HTTP_ENABLED, true),
        format: env.LOG_HTTP_FORMAT || 'combined'
      },
      error: {
        enabled: parseBoolean(env.LOG_ERROR_ENABLED, true),
        filename: env.LOG_ERROR_FILENAME || './logs/error-%DATE%.log',
        includeStack: parseBoolean(env.LOG_ERROR_INCLUDE_STACK, true),
        maxSize: env.LOG_ERROR_MAX_SIZE || '20m',
        maxFiles: parseNumber(env.LOG_ERROR_MAX_FILES, 30)
      },
      debug: {
        enabled: parseBoolean(env.LOG_DEBUG_ENABLED, nodeEnv === 'development'),
        namespace: env.LOG_DEBUG_NAMESPACE || 'petcare:*'
      },
      performance: {
        enabled: parseBoolean(env.LOG_PERFORMANCE_ENABLED, nodeEnv !== 'production'),
        slowThreshold: parseNumber(env.LOG_PERFORMANCE_SLOW_THRESHOLD, 1000)
      }
    },
    
    // 站点配置
    sites: {
      en: {
        domain: env.SITE_EN_DOMAIN || 'localhost:4321',
        name: env.SITE_EN_NAME || 'PetCare Blog',
        description: env.SITE_EN_DESCRIPTION || 'Your trusted source for pet care information'
      },
      de: {
        domain: env.SITE_DE_DOMAIN || 'localhost:4322',
        name: env.SITE_DE_NAME || 'Haustier Pflege',
        description: env.SITE_DE_DESCRIPTION || 'Ihre vertrauenswürdige Quelle für Haustierpflege'
      },
      ru: {
        domain: env.SITE_RU_DOMAIN || 'localhost:4323',
        name: env.SITE_RU_NAME || 'Уход за Домашними Животными',
        description: env.SITE_RU_DESCRIPTION || 'Надежный источник информации об уходе за домашними животными'
      },
      admin: {
        domain: env.ADMIN_DOMAIN || 'localhost:4324',
        name: env.ADMIN_NAME || 'PetCare Admin',
        secretKey: secrets.ADMIN_SECRET_KEY || env.ADMIN_SECRET_KEY!
      }
    },
    
    // SEO配置
    seo: {
      sitemapBaseUrl: env.SITEMAP_BASE_URL || 'https://www.petcare.com',
      robotsAllowAll: parseBoolean(env.ROBOTS_ALLOW_ALL, true),
      metaDefaultKeywords: parseArray(env.META_DEFAULT_KEYWORDS, ['pet', 'care', 'blog', 'animals', 'health']),
      analyticsId: env.ANALYTICS_ID
    },
    
    // 第三方服务配置
    thirdParty: {
      googleAnalytics: env.GA_TRACKING_ID ? {
        trackingId: env.GA_TRACKING_ID,
        debug: parseBoolean(env.GA_DEBUG, false)
      } : undefined,
      
      sentry: env.SENTRY_DSN ? {
        dsn: secrets.SENTRY_DSN || env.SENTRY_DSN,
        environment: env.SENTRY_ENVIRONMENT || nodeEnv
      } : undefined,
      
      cloudflare: env.CLOUDFLARE_ZONE_ID ? {
        zoneId: env.CLOUDFLARE_ZONE_ID,
        apiToken: secrets.CLOUDFLARE_API_TOKEN || env.CLOUDFLARE_API_TOKEN!
      } : undefined
    },
    
    // 开发环境配置
    development: {
      debug: parseBoolean(env.DEBUG, nodeEnv === 'development'),
      debugNamespace: env.DEBUG_NAMESPACE || 'petcare:*',
      hotReload: parseBoolean(env.HOT_RELOAD, nodeEnv === 'development'),
      apiDocsEnabled: parseBoolean(env.API_DOCS_ENABLED, nodeEnv !== 'production'),
      apiDocsPath: env.API_DOCS_PATH || '/api-docs',
      testDbName: env.TEST_DB_NAME,
      testDbUser: env.TEST_DB_USER,
      testDbPassword: env.TEST_DB_PASSWORD
    }
  };
  
  return config;
}

/**
 * 加载并构建应用配置
 */
export function loadConfig(options: ConfigLoadOptions = {}): AppConfig {
  // 如果已缓存且没有特殊选项，直接返回缓存
  if (cachedConfig && options.cacheConfig !== false && !options.envFile) {
    return cachedConfig;
  }
  
  console.log('🔧 Loading application configuration...');
  
  try {
    // 1. 加载环境变量文件
    loadEnvFile(options.envFile);
    
    // 2. 验证环境变量
    if (options.validateRequired !== false) {
      validateAllEnvVars(process.env);
    }
    
    // 3. 处理敏感信息
    const masterKey = process.env.ENCRYPTION_MASTER_KEY;
    const secrets = processSecrets(process.env, options.decryptSecrets || false, masterKey);
    
    // 4. 构建配置对象
    const config = buildAppConfig(process.env, secrets);
    
    // 5. 缓存配置
    if (options.cacheConfig !== false) {
      cachedConfig = config;
    }
    
    // 6. 显示配置摘要（隐藏敏感信息）
    if (config.development.debug) {
      const maskedConfig = maskSensitiveConfig(config, ENV_VALIDATION_RULES.encrypted || []);
      console.log('📋 Configuration loaded:', JSON.stringify(maskedConfig, null, 2));
    } else {
      console.log('✅ Configuration loaded successfully');
      console.log(`   Environment: ${config.nodeEnv}`);
      console.log(`   Port: ${config.port}`);
      console.log(`   Database: ${config.database.host}:${config.database.port}/${config.database.name}`);
      console.log(`   Redis: ${config.redis.host}:${config.redis.port}/${config.redis.db}`);
      console.log(`   Debug mode: ${config.development.debug ? 'ON' : 'OFF'}`);
    }
    
    return config;
    
  } catch (error) {
    console.error('❌ Failed to load configuration:');
    console.error((error as Error).message);
    
    // 在生产环境中，配置失败应该终止应用
    if (process.env.NODE_ENV === 'production') {
      process.exit(1);
    }
    
    throw error;
  }
}

/**
 * 重新加载配置（清除缓存）
 */
export function reloadConfig(options: ConfigLoadOptions = {}): AppConfig {
  console.log('🔄 Reloading configuration...');
  cachedConfig = null;
  return loadConfig(options);
}

/**
 * 获取当前缓存的配置
 */
export function getConfig(): AppConfig {
  if (!cachedConfig) {
    return loadConfig();
  }
  return cachedConfig;
}

/**
 * 检查配置是否已加载
 */
export function isConfigLoaded(): boolean {
  return cachedConfig !== null;
}

/**
 * 验证特定的配置部分
 */
export function validateConfigSection<K extends keyof AppConfig>(
  sectionName: K,
  config: AppConfig = getConfig()
): AppConfig[K] {
  const section = config[sectionName];
  
  if (!section) {
    throw new ConfigError(`Configuration section '${sectionName}' is missing or empty`);
  }
  
  return section;
}

/**
 * 获取环境特定的配置值
 */
export function getEnvConfig<T>(
  key: string,
  defaultValue?: T,
  transform?: (value: string) => T
): T {
  const value = process.env[key];
  
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new ConfigError(`Environment variable '${key}' is required but not set`);
  }
  
  if (transform) {
    try {
      return transform(value);
    } catch (error) {
      throw new ConfigError(`Failed to transform environment variable '${key}': ${(error as Error).message}`);
    }
  }
  
  return value as unknown as T;
}

/**
 * 导出默认配置实例
 */
export default {
  load: loadConfig,
  reload: reloadConfig,
  get: getConfig,
  isLoaded: isConfigLoaded,
  validateSection: validateConfigSection,
  getEnv: getEnvConfig
};