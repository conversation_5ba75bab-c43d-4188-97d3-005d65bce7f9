import express, { Application, Request, Response } from 'express';
import cors from 'cors';
import helmet from 'helmet';
import compression from 'compression';
import dotenv from 'dotenv';
import swaggerJsdoc from 'swagger-jsdoc';
import swaggerUi from 'swagger-ui-express';

// Load environment variables first
dotenv.config();

// Initialize logging system
import { initializeLogging, getLogger, loggingSystem } from './logger';
import { Environment } from './logger/strategies';
import { swaggerConfig, swaggerUiOptions } from './config/swagger';
import { swaggerAuthMiddleware, ipWhitelistMiddleware, timeBasedAccessMiddleware } from './middlewares/swaggerAuth';
import apiRoutes from './routes';


// Import configuration and database systems
import { config } from './config';
import { db } from './config/database';
/**
 * 异步启动应用程序
 */
async function startApplication(): Promise<void> {
  try {
    // Initialize logging system first
    const environment = (process.env.NODE_ENV as Environment) || 'development';
    await initializeLogging(environment);
    
    const logger = getLogger();
    logger.info('Logging system initialized successfully', {
      extra: { environment }
    });

    // Initialize configuration system
    logger.info('🔧 Initializing configuration system...');
    await config.init();
    logger.info('✅ Configuration system initialized successfully');

    // Initialize database connection
    logger.info('🗄️ Initializing database connection...');
    try {
      await db.init();
      logger.info('✅ Database connection established successfully');
    } catch (error) {
      logger.error('❌ Failed to establish database connection: ' + (error instanceof Error ? error.message : String(error)));
      logger.warn('⚠️ Server will start without database connection');
    }

    // Create Express application
    const app: Application = express();

    // Set port
    const PORT = process.env.PORT || 3000;

    // Get HTTP logger middleware
    const httpLogger = loggingSystem.getHttpLogger();

    // Middleware
    app.use(helmet());
    app.use(cors());
    app.use(compression());
    
    // Use our HTTP logger instead of morgan
    if (httpLogger) {
      const middlewareStack = httpLogger.getMiddlewareStack();
      middlewareStack.forEach(middleware => app.use(middleware));
    }
    
    app.use(express.json());
    app.use(express.urlencoded({ extended: true }));

    // Health check endpoint with logging system status
    app.get('/health', async (_req: Request, res: Response) => {
      const systemHealth = await loggingSystem.healthCheck();
      const systemStats = loggingSystem.getSystemStats();
      
      res.json({
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        environment: process.env.NODE_ENV || 'development',
        logging: {
          status: systemHealth.status,
          components: systemHealth.components,
          initialized: systemStats.initialized
        }
      });
      
      logger.debug('Health check endpoint accessed');
    });

    // API version endpoint
    app.get('/api/v1', (_req: Request, res: Response) => {
      res.json({
        version: '1.0.0',
        message: 'PetCare Blog API',
        docs: '/api/docs',
        endpoints: {
          health: '/health',
          docs: '/api/docs',
          auth: '/api/v1/auth',
          articles: '/api/v1/articles',
          categories: '/api/v1/categories',
          comments: '/api/v1/comments',
        },
      });
      
      logger.debug('API version endpoint accessed');
    });

    // Import error handling middleware
    const { requestIdMiddleware, errorMiddleware, notFoundMiddleware, asyncHandler } = await import('./middlewares/errorHandler');
    const { AppError } = await import('./utils/AppError');
    const { ErrorCode } = await import('./types/errors');

    // Request ID middleware (must be early in the stack)
    app.use(requestIdMiddleware);

    // Initialize Swagger documentation
    const swaggerSpec = swaggerJsdoc(swaggerConfig);
    
    // Swagger UI setup with authentication middleware
    app.use('/api/docs', 
      ipWhitelistMiddleware,
      timeBasedAccessMiddleware,
      swaggerAuthMiddleware,
      swaggerUi.serve
    );
    app.get('/api/docs', swaggerUi.setup(swaggerSpec, swaggerUiOptions));
    
    // JSON endpoint for swagger spec (also protected)
    app.get('/api/docs.json', 
      ipWhitelistMiddleware,
      timeBasedAccessMiddleware,
      swaggerAuthMiddleware,
      (_req: Request, res: Response) => {
        res.setHeader('Content-Type', 'application/json');
        res.send(swaggerSpec);
      }
    );

    // API routes
    app.use('/api/v1', apiRoutes);

    // Test route to demonstrate error handling
    app.get('/api/v1/test-error', asyncHandler(async (req: Request, _res: Response) => {
      const errorType = req.query.type as string;
      
      switch (errorType) {
        case 'validation':
          throw new AppError(ErrorCode.VALIDATION_ERROR, 'Test validation error');
        case 'auth':
          throw new AppError(ErrorCode.AUTH_FAILED, 'Test authentication error');
        case 'notfound':
          throw new AppError(ErrorCode.RESOURCE_NOT_FOUND, 'Test resource not found');
        case 'system':
          throw new AppError(ErrorCode.INTERNAL_ERROR, 'Test system error');
        default:
          throw new Error('Test generic error');
      }
    }));

    // 404 handler for undefined routes (must be after all routes)
    app.use(notFoundMiddleware);

    // Error handling middleware (must be last)
    app.use(errorMiddleware);

    // Start server
    app.listen(PORT, () => {
      logger.info('Server started successfully', {
        extra: {
          port: PORT,
          environment: process.env.NODE_ENV || 'development',
          nodeVersion: process.version,
          pid: process.pid
        }
      });
      
      logger.info('Server endpoints available', {
        extra: {
          server: `http://localhost:${PORT}`,
          api: `http://localhost:${PORT}/api/v1`,
          health: `http://localhost:${PORT}/health`
        }
      });
    });

    // Handle graceful shutdown
    process.on('SIGTERM', async () => {
      logger.info('SIGTERM received, shutting down gracefully');
      await loggingSystem.shutdown();
      process.exit(0);
    });

    process.on('SIGINT', async () => {
      logger.info('SIGINT received, shutting down gracefully');
      await loggingSystem.shutdown();
      process.exit(0);
    });

  } catch (error) {
    console.error('❌ Failed to start application:', error);
    process.exit(1);
  }
}

// Start the application
startApplication().catch((error) => {
  console.error('❌ Failed to start application:', error);
  process.exit(1);
});
