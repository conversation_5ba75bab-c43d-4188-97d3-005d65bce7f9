/**
 * 错误处理机制测试
 * 测试统一错误类、错误中间件和验证中间件的功能
 */

import { AppError, ValidationError, AuthError, BusinessError, SystemError, ErrorFactory, Errors } from '../utils/AppError';
import { ErrorCode } from '../types/errors';
import { ErrorHandlerMiddleware } from '../middlewares/errorHandler';
import { validationRules, Validator } from '../middlewares/validation';

describe('错误处理系统', () => {
  describe('错误类功能', () => {
    it('应该正确创建AppError', () => {
      const appError = new AppError(ErrorCode.INTERNAL_ERROR, 'Test internal error');
      expect(appError.code).toBe(ErrorCode.INTERNAL_ERROR);
      expect(appError.statusCode).toBeDefined();
      expect(appError.level).toBeDefined();
      expect(appError.category).toBeDefined();
      expect(appError.message).toBe('Test internal error');
    });

    it('应该正确创建ValidationError', () => {
      const validationError = ValidationError.required('email');
      expect(validationError).toBeInstanceOf(ValidationError);
      expect(validationError.details).toBeDefined();
      expect(validationError.details).toHaveLength(1);
    });

    it('应该正确创建AuthError', () => {
      const authError = new AuthError(ErrorCode.TOKEN_EXPIRED);
      expect(authError).toBeInstanceOf(AuthError);
      expect(authError.statusCode).toBe(401);
    });

    it('应该正确创建BusinessError', () => {
      const businessError = BusinessError.notFound('Article', '123');
      expect(businessError).toBeInstanceOf(BusinessError);
      expect(businessError.message).toContain('Article');
      expect(businessError.message).toContain('123');
    });

    it('应该正确创建SystemError', () => {
      const systemError = SystemError.database('query', new Error('Connection failed'));
      expect(systemError).toBeInstanceOf(SystemError);
      expect(systemError.details).toBeDefined();
    });

    it('应该通过ErrorFactory创建错误', () => {
      const fromNative = ErrorFactory.fromNativeError(new Error('Test native error'));
      expect(fromNative).toBeInstanceOf(AppError);

      const fromHttpStatus = ErrorFactory.fromHttpStatus(404, 'Not found');
      expect(fromHttpStatus.statusCode).toBe(404);
    });

    it('应该使用Errors便捷对象', () => {
      const authFailed = Errors.authFailed('Invalid credentials');
      expect(authFailed.message).toContain('Invalid credentials');

      const notFound = Errors.notFound('User', '123');
      expect(notFound.message).toContain('User');

      const required = Errors.required('username');
      expect(required.message).toContain('username');
    });
  });

  describe('验证器功能', () => {
    it('应该验证用户注册数据', () => {
      const mockRequest = {
        body: {
          email: '<EMAIL>',
          password: 'Test123!@#',
          username: 'testuser'
        },
        params: {},
        query: {},
        headers: {}
      };

      const result = Validator.validateRequest(mockRequest as any, validationRules.userRegister);
      expect(result.isValid).toBe(true);
    });

    it('应该验证分页参数', () => {
      const mockRequest = {
        body: {},
        params: {},
        query: {
          page: 1,
          per_page: 20
        },
        headers: {}
      };

      const result = Validator.validateRequest(mockRequest as any, validationRules.pagination);
      expect(result.isValid).toBe(true);
    });

    it('应该验证ID参数', () => {
      const mockRequest = {
        body: {},
        params: {
          id: 123
        },
        query: {},
        headers: {}
      };

      const result = Validator.validateRequest(mockRequest as any, validationRules.idParam);
      expect(result.isValid).toBe(true);
    });

    it('应该检测无效数据', () => {
      const invalidRequest = {
        body: {
          email: 'invalid-email',
          password: '123',
          username: 'a'
        },
        params: {},
        query: {},
        headers: {}
      };

      const result = Validator.validateRequest(invalidRequest as any, validationRules.userRegister);
      expect(result.isValid).toBe(false);
      expect(result.errors.length).toBeGreaterThan(0);
    });
  });

  describe('错误处理中间件', () => {
    let errorHandler: ErrorHandlerMiddleware;
    let mockReq: any;
    let mockRes: any;
    let mockNext: any;
    let capturedResponse: any;

    beforeEach(() => {
      errorHandler = new ErrorHandlerMiddleware({
        logErrors: false,
        includeDetails: true,
        includeStack: false,
        sensitiveFields: ['password', 'token'],
        sendNotifications: false
      });

      mockReq = {
        requestId: 'test-request-123',
        originalUrl: '/api/v1/test',
        method: 'GET',
        get: (header: string) => header === 'User-Agent' ? 'Test Agent' : undefined,
        ip: '127.0.0.1',
        connection: { remoteAddress: '127.0.0.1' },
        body: {},
        params: {},
        query: {},
        headers: {}
      };

      capturedResponse = {};
      mockRes = {
        status: (code: number) => {
          capturedResponse.statusCode = code;
          return mockRes;
        },
        json: (data: any) => {
          capturedResponse.body = data;
          return mockRes;
        },
        set: () => mockRes
      };

      mockNext = jest.fn();
    });

    it('应该处理验证错误', () => {
      const validationError = new ValidationError(ErrorCode.VALIDATION_ERROR, 'Test validation error', [
        { field: 'email', message: 'Invalid email format', code: 'invalid_format' }
      ]);

      const middleware = errorHandler.createMiddleware();
      middleware(validationError, mockReq, mockRes, mockNext);

      expect(capturedResponse.statusCode).toBe(400);
      expect(capturedResponse.body?.success).toBe(false);
      expect(capturedResponse.body?.error?.code).toBe('VALIDATION_ERROR');
      expect(capturedResponse.body?.error?.request_id).toBeDefined();
      expect(capturedResponse.body?.meta?.timestamp).toBeDefined();
    });

    it('应该处理认证错误', () => {
      const authError = new AuthError(ErrorCode.TOKEN_EXPIRED);
      const middleware = errorHandler.createMiddleware();
      middleware(authError, mockReq, mockRes, mockNext);

      expect(capturedResponse.statusCode).toBe(401);
      expect(capturedResponse.body?.error?.code).toBe('TOKEN_EXPIRED');
    });

    it('应该处理系统错误', () => {
      const systemError = new SystemError(ErrorCode.INTERNAL_ERROR, 'Test system error');
      const middleware = errorHandler.createMiddleware();
      middleware(systemError, mockReq, mockRes, mockNext);

      expect(capturedResponse.statusCode).toBe(500);
      expect(capturedResponse.body?.error?.code).toBe('INTERNAL_ERROR');
    });

    it('应该转换原生错误', () => {
      const nativeError = new Error('Test native error');
      const middleware = errorHandler.createMiddleware();
      middleware(nativeError, mockReq, mockRes, mockNext);

      expect(capturedResponse.statusCode).toBe(500);
      expect(capturedResponse.body?.error?.code).toBe('INTERNAL_ERROR');
    });

    it('应该符合API响应格式', () => {
      const validationError = new ValidationError(ErrorCode.VALIDATION_ERROR, 'Test error', [
        { field: 'email', message: 'Invalid email', code: 'invalid' }
      ]);

      const middleware = errorHandler.createMiddleware();
      middleware(validationError, mockReq, mockRes, mockNext);

      expect(capturedResponse.body).toHaveProperty('success');
      expect(capturedResponse.body.success).toBe(false);
      expect(capturedResponse.body).toHaveProperty('error');
      expect(capturedResponse.body.error).toHaveProperty('code');
      expect(capturedResponse.body.error).toHaveProperty('message');
      expect(capturedResponse.body.error).toHaveProperty('request_id');
      expect(capturedResponse.body).toHaveProperty('meta');
      expect(capturedResponse.body.meta).toHaveProperty('timestamp');
    });
  });
});