/**
 * 生产环境日志策略配置
 * 定义不同环境下的日志配置策略和最佳实践
 */

import { LoggingConfig } from '../types/config';
import { HttpLogConfig, ErrorLogConfig, DebugLogConfig, PerformanceLogConfig, LogLevel, TransportType } from '../types/logger';

/**
 * 环境类型
 */
export type Environment = 'development' | 'test' | 'staging' | 'production';

/**
 * 日志策略配置接口
 */
export interface LogStrategyConfig {
  logging: LoggingConfig;
  http: HttpLogConfig;
  error: ErrorLogConfig;
  debug: DebugLogConfig;
  performance: PerformanceLogConfig;
}

/**
 * 日志策略工厂
 */
export class LogStrategyFactory {
  /**
   * 根据环境获取日志策略
   */
  static getStrategy(environment: Environment): LogStrategyConfig {
    switch (environment) {
      case 'development':
        return this.getDevelopmentStrategy();
      case 'test':
        return this.getTestStrategy();
      case 'staging':
        return this.getStagingStrategy();
      case 'production':
        return this.getProductionStrategy();
      default:
        return this.getDevelopmentStrategy();
    }
  }

  /**
   * 开发环境策略
   */
  private static getDevelopmentStrategy(): LogStrategyConfig {
    return {
      logging: {
        level: 'debug',
        file: './logs/app.log',
        maxSize: '10m',
        maxFiles: 5,
        datePattern: 'YYYY-MM-DD',
        console: {
          enabled: true,
          level: 'debug',
          colorize: true
        },
        fileRotation: {
          enabled: false, // 开发环境不需要轮转
          filename: './logs/app-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          maxSize: '10m',
          maxFiles: '7d',
          zippedArchive: false
        },
        http: {
          enabled: true,
          format: 'dev'
        },
        error: {
          enabled: true,
          filename: './logs/error.log',
          includeStack: true,
          maxSize: '10m',
          maxFiles: 3
        },
        debug: {
          enabled: true,
          namespace: 'petcare:*'
        },
        performance: {
          enabled: true,
          slowThreshold: 500 // 开发环境更严格的慢查询阈值
        }
      },
      http: {
        enabled: true,
        format: 'dev',
        includeSensitive: true, // 开发环境包含敏感数据用于调试
        maxBodySize: 1024 * 1024, // 1MB
        maxResponseSize: 1024 * 1024, // 1MB
        filters: []
      },
      error: {
        enabled: true,
        includeStack: true,
        includeRequestContext: true,
        levelMapping: {
          'ValidationError': LogLevel.WARN,
          'CastError': LogLevel.WARN,
          'MongoError': LogLevel.ERROR,
          'JsonWebTokenError': LogLevel.WARN
        },
        sensitiveFields: ['password', 'token', 'secret'],
        notifications: {
          enabled: false, // 开发环境不发送通知
          channels: [] as ('email' | 'slack' | 'webhook')[],
          threshold: LogLevel.ERROR,
          cooldown: 0
        }
      },
      debug: {
        enabled: true,
        namespaces: ['petcare:*', 'app:*', 'db:*', 'auth:*'],
        includeTimestamp: true,
        includeFileInfo: true,
        maxLevel: LogLevel.SILLY,
        targets: [TransportType.CONSOLE, TransportType.FILE]
      },
      performance: {
        enabled: true,
        slowThreshold: 500,
        includeParams: true,
        includeResultCount: true,
        samplingRate: 1.0, // 100%采样
        profiler: {
          enabled: true,
          includeMemory: true,
          includeCpu: true
        }
      }
    };
  }

  /**
   * 测试环境策略
   */
  private static getTestStrategy(): LogStrategyConfig {
    return {
      logging: {
        level: 'warn', // 测试环境减少日志噪音
        file: './logs/test.log',
        maxSize: '5m',
        maxFiles: 2,
        datePattern: 'YYYY-MM-DD',
        console: {
          enabled: false, // 测试时不输出到控制台
          level: 'error',
          colorize: false
        },
        fileRotation: {
          enabled: false,
          filename: './logs/test-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          maxSize: '5m',
          maxFiles: '3d',
          zippedArchive: false
        },
        http: {
          enabled: false, // 测试时不记录HTTP请求
          format: 'tiny'
        },
        error: {
          enabled: true,
          filename: './logs/test-error.log',
          includeStack: true,
          maxSize: '5m',
          maxFiles: 2
        },
        debug: {
          enabled: false,
          namespace: 'test:*'
        },
        performance: {
          enabled: false,
          slowThreshold: 1000
        }
      },
      http: {
        enabled: false, // 测试环境禁用HTTP日志
        format: 'tiny',
        includeSensitive: false,
        maxBodySize: 0,
        maxResponseSize: 0,
        filters: [
          { path: '/health', method: 'GET', skip: true },
          { path: '/metrics', method: 'GET', skip: true }
        ]
      },
      error: {
        enabled: true,
        includeStack: true,
        includeRequestContext: false,
        levelMapping: {
          'ValidationError': LogLevel.WARN,
          'TestError': LogLevel.ERROR
        },
        sensitiveFields: ['password', 'token', 'secret', 'apiKey'],
        notifications: {
          enabled: false,
          channels: [] as ('email' | 'slack' | 'webhook')[],
          threshold: LogLevel.ERROR,
          cooldown: 0
        }
      },
      debug: {
        enabled: false,
        namespaces: ['test:*'],
        includeTimestamp: false,
        includeFileInfo: false,
        maxLevel: LogLevel.ERROR,
        targets: [TransportType.FILE]
      },
      performance: {
        enabled: false,
        slowThreshold: 1000,
        includeParams: false,
        includeResultCount: false,
        samplingRate: 0.1 // 10%采样
      }
    };
  }

  /**
   * 预发布环境策略
   */
  private static getStagingStrategy(): LogStrategyConfig {
    return {
      logging: {
        level: 'info',
        file: './logs/app.log',
        maxSize: '50m',
        maxFiles: 10,
        datePattern: 'YYYY-MM-DD',
        console: {
          enabled: true,
          level: 'info',
          colorize: false
        },
        fileRotation: {
          enabled: true,
          filename: './logs/app-%DATE%.log',
          datePattern: 'YYYY-MM-DD',
          maxSize: '50m',
          maxFiles: '14d',
          zippedArchive: true
        },
        http: {
          enabled: true,
          format: 'combined'
        },
        error: {
          enabled: true,
          filename: './logs/error-%DATE%.log',
          includeStack: true,
          maxSize: '50m',
          maxFiles: 30
        },
        debug: {
          enabled: false,
          namespace: 'petcare:error:*'
        },
        performance: {
          enabled: true,
          slowThreshold: 1000
        }
      },
      http: {
        enabled: true,
        format: 'combined',
        includeSensitive: false,
        maxBodySize: 10 * 1024, // 10KB
        maxResponseSize: 100 * 1024, // 100KB
        filters: [
          { path: '/health', method: 'GET', skip: true },
          { path: '/metrics', method: 'GET', skip: true },
          { path: '/favicon.ico', skip: true }
        ]
      },
      error: {
        enabled: true,
        includeStack: true,
        includeRequestContext: true,
        levelMapping: {
          'ValidationError': LogLevel.WARN,
          'CastError': LogLevel.WARN,
          'MongoError': LogLevel.ERROR,
          'JsonWebTokenError': LogLevel.WARN,
          'RateLimitError': LogLevel.WARN
        },
        sensitiveFields: ['password', 'token', 'secret', 'apiKey', 'authorization'],
        notifications: {
          enabled: true,
          channels: ['slack'],
          threshold: LogLevel.ERROR,
          cooldown: 300 // 5分钟冷却
        }
      },
      debug: {
        enabled: false,
        namespaces: ['petcare:error:*'],
        includeTimestamp: true,
        includeFileInfo: false,
        maxLevel: LogLevel.ERROR,
        targets: [TransportType.FILE]
      },
      performance: {
        enabled: true,
        slowThreshold: 1000,
        includeParams: false,
        includeResultCount: true,
        samplingRate: 0.3, // 30%采样
        profiler: {
          enabled: false,
          includeMemory: false,
          includeCpu: false
        }
      }
    };
  }

  /**
   * 生产环境策略 (最严格)
   */
  private static getProductionStrategy(): LogStrategyConfig {
    return {
      logging: {
        level: 'info',
        file: './logs/app.log',
        maxSize: '100m',
        maxFiles: 30,
        datePattern: 'YYYY-MM-DD',
        console: {
          enabled: false, // 生产环境不输出到控制台
          level: 'error',
          colorize: false
        },
        fileRotation: {
          enabled: true,
          filename: './logs/app-%DATE%.log',
          datePattern: 'YYYY-MM-DD-HH', // 按小时轮转
          maxSize: '100m',
          maxFiles: '30d',
          zippedArchive: true
        },
        http: {
          enabled: true,
          format: 'combined'
        },
        error: {
          enabled: true,
          filename: './logs/error-%DATE%.log',
          includeStack: false, // 生产环境不包含堆栈信息
          maxSize: '100m',
          maxFiles: 90
        },
        debug: {
          enabled: false, // 生产环境禁用调试
          namespace: ''
        },
        performance: {
          enabled: true,
          slowThreshold: 2000 // 生产环境更宽松的阈值
        }
      },
      http: {
        enabled: true,
        format: 'combined',
        includeSensitive: false,
        maxBodySize: 1024, // 1KB 生产环境最小化记录
        maxResponseSize: 10 * 1024, // 10KB
        skip: (req, res) => {
          // 跳过健康检查和静态资源
          return req.path === '/health' || 
                 req.path === '/metrics' || 
                 req.path.startsWith('/static/') ||
                 res.statusCode < 400;
        },
        filters: [
          { path: '/health', skip: true },
          { path: '/metrics', skip: true },
          { path: '/favicon.ico', skip: true },
          { path: '/robots.txt', skip: true },
          { path: '/sitemap.xml', skip: true }
        ]
      },
      error: {
        enabled: true,
        includeStack: false, // 生产环境安全考虑
        includeRequestContext: true,
        levelMapping: {
          'ValidationError': LogLevel.WARN,
          'CastError': LogLevel.WARN,
          'MongoError': LogLevel.ERROR,
          'JsonWebTokenError': LogLevel.WARN,
          'RateLimitError': LogLevel.INFO,
          'NotFoundError': LogLevel.WARN
        },
        sensitiveFields: [
          'password', 'token', 'secret', 'apiKey', 'authorization',
          'cookie', 'sessionId', 'jwt', 'bearer', 'auth',
          'creditCard', 'ssn', 'phoneNumber'
        ],
        notifications: {
          enabled: true,
          channels: ['email', 'slack', 'webhook'],
          threshold: LogLevel.ERROR,
          cooldown: 600 // 10分钟冷却
        }
      },
      debug: {
        enabled: false,
        namespaces: [],
        includeTimestamp: false,
        includeFileInfo: false,
        maxLevel: LogLevel.ERROR,
        targets: [] as TransportType[]
      },
      performance: {
        enabled: true,
        slowThreshold: 2000,
        includeParams: false,
        includeResultCount: false,
        samplingRate: 0.05, // 5%采样 - 生产环境最小化性能影响
        profiler: {
          enabled: false,
          includeMemory: false,
          includeCpu: false
        }
      }
    };
  }

  /**
   * 高性能生产环境策略
   */
  static getHighPerformanceProductionStrategy(): LogStrategyConfig {
    const baseStrategy = this.getProductionStrategy();
    
    return {
      ...baseStrategy,
      logging: {
        ...baseStrategy.logging,
        level: 'warn', // 更高的日志级别
        fileRotation: {
          ...baseStrategy.logging.fileRotation,
          datePattern: 'YYYY-MM-DD', // 按天轮转而不是按小时
          maxFiles: '7d' // 只保留7天日志
        },
        performance: {
          ...baseStrategy.logging.performance,
          enabled: false // 禁用性能监控以获得最大性能
        }
      },
      http: {
        ...baseStrategy.http,
        enabled: false, // 完全禁用HTTP日志
        format: 'tiny'
      },
      performance: {
        ...baseStrategy.performance,
        enabled: false,
        samplingRate: 0.01 // 1%采样
      }
    };
  }

  /**
   * 调试专用策略（用于生产环境临时调试）
   */
  static getDebugProductionStrategy(): LogStrategyConfig {
    const baseStrategy = this.getProductionStrategy();
    
    return {
      ...baseStrategy,
      logging: {
        ...baseStrategy.logging,
        level: 'debug',
        debug: {
          enabled: true,
          namespace: 'petcare:debug:*'
        }
      },
      debug: {
        ...baseStrategy.debug,
        enabled: true,
        namespaces: ['petcare:debug:*', 'app:critical:*'],
        includeTimestamp: true,
        includeFileInfo: true,
        maxLevel: LogLevel.DEBUG,
        targets: [TransportType.FILE]
      },
      performance: {
        ...baseStrategy.performance,
        enabled: true,
        slowThreshold: 500,
        samplingRate: 0.2 // 20%采样
      }
    };
  }

  /**
   * 安全审计策略
   */
  static getSecurityAuditStrategy(): LogStrategyConfig {
    const baseStrategy = this.getProductionStrategy();
    
    return {
      ...baseStrategy,
      logging: {
        ...baseStrategy.logging,
        level: 'info'
      },
      http: {
        ...baseStrategy.http,
        enabled: true,
        format: 'combined',
        includeSensitive: false,
        maxBodySize: 5 * 1024, // 5KB
        maxResponseSize: 20 * 1024, // 20KB
        skip: undefined, // 不跳过任何请求
        filters: [] // 记录所有请求
      },
      error: {
        ...baseStrategy.error,
        includeStack: true, // 审计时包含堆栈
        includeRequestContext: true,
        notifications: {
          ...baseStrategy.error.notifications!,
          threshold: LogLevel.WARN, // 更低的通知阈值
          cooldown: 180 // 3分钟冷却
        }
      }
    };
  }
}

/**
 * 日志策略管理器
 */
export class LogStrategyManager {
  private static currentStrategy: LogStrategyConfig | null = null;
  private static currentEnvironment: Environment | null = null;

  /**
   * 初始化日志策略
   */
  static initialize(environment: Environment): LogStrategyConfig {
    this.currentEnvironment = environment;
    this.currentStrategy = LogStrategyFactory.getStrategy(environment);
    
    console.log(`Initialized log strategy for environment: ${environment}`);
    this.logStrategyInfo();
    
    return this.currentStrategy;
  }

  /**
   * 获取当前策略
   */
  static getCurrentStrategy(): LogStrategyConfig {
    if (!this.currentStrategy) {
      throw new Error('Log strategy not initialized. Call initialize() first.');
    }
    return this.currentStrategy;
  }

  /**
   * 获取当前环境
   */
  static getCurrentEnvironment(): Environment {
    return this.currentEnvironment || 'development';
  }

  /**
   * 动态切换到高性能模式（用于高负载时期）
   */
  static switchToHighPerformance(): void {
    if (this.currentEnvironment !== 'production') {
      console.warn('High performance mode is only available in production');
      return;
    }

    this.currentStrategy = LogStrategyFactory.getHighPerformanceProductionStrategy();
    console.log('Switched to high performance logging strategy');
    this.logStrategyInfo();
  }

  /**
   * 动态切换到调试模式（用于生产环境临时调试）
   */
  static switchToDebugMode(): void {
    if (this.currentEnvironment !== 'production') {
      console.warn('Debug mode switch is only available in production');
      return;
    }

    this.currentStrategy = LogStrategyFactory.getDebugProductionStrategy();
    console.log('Switched to debug logging strategy');
    this.logStrategyInfo();

    // 10分钟后自动切回正常模式
    setTimeout(() => {
      this.switchToNormalMode();
    }, 10 * 60 * 1000);
  }

  /**
   * 切换到安全审计模式
   */
  static switchToSecurityAuditMode(): void {
    this.currentStrategy = LogStrategyFactory.getSecurityAuditStrategy();
    console.log('Switched to security audit logging strategy');
    this.logStrategyInfo();
  }

  /**
   * 恢复正常模式
   */
  static switchToNormalMode(): void {
    if (!this.currentEnvironment) return;
    
    this.currentStrategy = LogStrategyFactory.getStrategy(this.currentEnvironment);
    console.log('Switched back to normal logging strategy');
    this.logStrategyInfo();
  }

  /**
   * 记录策略信息
   */
  private static logStrategyInfo(): void {
    if (!this.currentStrategy) return;

    console.log('Current logging configuration:', {
      level: this.currentStrategy.logging.level,
      consoleEnabled: this.currentStrategy.logging.console.enabled,
      fileRotationEnabled: this.currentStrategy.logging.fileRotation.enabled,
      httpLoggingEnabled: this.currentStrategy.http.enabled,
      errorLoggingEnabled: this.currentStrategy.error.enabled,
      debugEnabled: this.currentStrategy.debug.enabled,
      performanceEnabled: this.currentStrategy.performance.enabled
    });
  }
}

// 导出便捷函数
export const logStrategies = {
  /**
   * 初始化日志策略
   */
  init: (environment: Environment) => LogStrategyManager.initialize(environment),

  /**
   * 获取当前策略
   */
  get: () => LogStrategyManager.getCurrentStrategy(),

  /**
   * 获取环境特定策略
   */
  getForEnvironment: (env: Environment) => LogStrategyFactory.getStrategy(env),

  /**
   * 性能模式切换
   */
  switchToHighPerformance: () => LogStrategyManager.switchToHighPerformance(),

  /**
   * 调试模式切换
   */
  switchToDebugMode: () => LogStrategyManager.switchToDebugMode(),

  /**
   * 安全审计模式
   */
  switchToSecurityAudit: () => LogStrategyManager.switchToSecurityAuditMode(),

  /**
   * 恢复正常模式
   */
  switchToNormal: () => LogStrategyManager.switchToNormalMode()
};

// 已在类声明时导出，移除重复export