/**
 * 核心日志器模块
 * 基于Winston实现统一的日志管理系统
 */

import winston from 'winston';
import DailyRotateFile from 'winston-daily-rotate-file';
import path from 'path';
import fs from 'fs';
import { LoggingConfig } from '../types/config';
import { LogMetadata, LogContext } from '../types/logger';

/**
 * 日志格式化器
 */
export class LogFormatter {
  /**
   * 创建时间戳格式化器
   */
  static timestamp(): winston.Logform.Format {
    return winston.format.timestamp({
      format: 'YYYY-MM-DD HH:mm:ss.SSS'
    });
  }

  /**
   * 创建错误格式化器
   */
  static errors(): winston.Logform.Format {
    return winston.format.errors({ stack: true });
  }

  /**
   * 创建JSON格式化器
   */
  static json(): winston.Logform.Format {
    return winston.format.json({
      space: 0,
      replacer: (key, value) => {
        // 过滤敏感信息
        if (typeof value === 'string' && this.isSensitive(key)) {
          return '***MASKED***';
        }
        return value;
      }
    });
  }

  /**
   * 创建控制台格式化器
   */
  static console(colorize: boolean = true): winston.Logform.Format {
    const formats: winston.Logform.Format[] = [
      this.timestamp(),
      this.errors(),
    ];

    if (colorize) {
      formats.push(winston.format.colorize({ all: true }));
    }

    formats.push(
      winston.format.printf(info => {
        const { timestamp, level, message, ...meta } = info;
        let output = `${timestamp} [${level}]: ${message}`;
        
        if (Object.keys(meta).length > 0) {
          output += ` ${JSON.stringify(meta, null, 2)}`;
        }
        
        return output;
      })
    );

    return winston.format.combine(...formats);
  }

  /**
   * 创建文件格式化器
   */
  static file(): winston.Logform.Format {
    return winston.format.combine(
      this.timestamp(),
      this.errors(),
      winston.format.metadata({
        fillExcept: ['message', 'level', 'timestamp']
      }),
      this.json()
    );
  }

  /**
   * 创建性能日志格式化器
   */
  static performance(): winston.Logform.Format {
    return winston.format.combine(
      this.timestamp(),
      winston.format.printf(info => {
        const { timestamp, level, message, duration, ...meta } = info;
        return JSON.stringify({
          timestamp,
          level,
          message,
          duration: `${duration}ms`,
          ...meta
        });
      })
    );
  }

  /**
   * 判断字段是否敏感
   */
  private static isSensitive(key: string): boolean {
    const sensitiveFields = [
      'password', 'token', 'secret', 'key', 'authorization',
      'cookie', 'session', 'credential', 'auth', 'bearer'
    ];
    
    return sensitiveFields.some(field => 
      key.toLowerCase().includes(field)
    );
  }
}

/**
 * 传输器工厂
 */
export class TransportFactory {
  /**
   * 创建控制台传输器
   */
  static createConsole(config: LoggingConfig): winston.transport {
    return new winston.transports.Console({
      level: config.console.level,
      format: LogFormatter.console(config.console.colorize),
      handleExceptions: true,
      handleRejections: true,
      stderrLevels: ['error', 'warn']
    });
  }

  /**
   * 创建文件传输器
   */
  static createFile(config: LoggingConfig): winston.transport {
    // 确保日志目录存在
    const logDir = path.dirname(config.file);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    return new winston.transports.File({
      level: config.level,
      filename: config.file,
      format: LogFormatter.file(),
      maxsize: this.parseSize(config.maxSize),
      maxFiles: config.maxFiles,
      handleExceptions: false,
      handleRejections: false
    });
  }

  /**
   * 创建轮转文件传输器
   */
  static createDailyRotateFile(config: LoggingConfig): DailyRotateFile {
    // 确保日志目录存在
    const logDir = path.dirname(config.fileRotation.filename);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    return new DailyRotateFile({
      level: config.level,
      filename: config.fileRotation.filename,
      datePattern: config.fileRotation.datePattern,
      maxSize: config.fileRotation.maxSize,
      maxFiles: config.fileRotation.maxFiles,
      zippedArchive: config.fileRotation.zippedArchive,
      format: LogFormatter.file(),
      handleExceptions: false,
      handleRejections: false,
      createSymlink: true,
      symlinkName: 'app-current.log'
    });
  }

  /**
   * 创建错误文件传输器
   */
  static createErrorFile(config: LoggingConfig): DailyRotateFile {
    if (!config.error.enabled) {
      return null as any;
    }

    // 确保错误日志目录存在
    const logDir = path.dirname(config.error.filename);
    if (!fs.existsSync(logDir)) {
      fs.mkdirSync(logDir, { recursive: true });
    }

    return new DailyRotateFile({
      level: 'error',
      filename: config.error.filename,
      datePattern: config.datePattern,
      maxSize: config.error.maxSize,
      maxFiles: config.error.maxFiles,
      zippedArchive: true,
      format: winston.format.combine(
        LogFormatter.timestamp(),
        LogFormatter.errors(),
        winston.format.metadata(),
        winston.format.json()
      ),
      handleExceptions: true,
      handleRejections: true,
      createSymlink: true,
      symlinkName: 'error-current.log'
    });
  }

  /**
   * 解析文件大小字符串为字节数
   */
  private static parseSize(sizeStr: string): number {
    const match = sizeStr.match(/^(\d+)([kmg]?)$/i);
    if (!match) {
      return 10 * 1024 * 1024; // 默认10MB
    }

    const [, size, unit] = match;
    const bytes = parseInt(size, 10);

    switch (unit.toLowerCase()) {
      case 'k': return bytes * 1024;
      case 'm': return bytes * 1024 * 1024;
      case 'g': return bytes * 1024 * 1024 * 1024;
      default: return bytes;
    }
  }
}

/**
 * 日志过滤器
 */
export class LogFilter {
  /**
   * 创建敏感信息过滤器
   */
  static createSensitiveFilter(): winston.Logform.Format {
    return winston.format((info) => {
      if (info.message && typeof info.message === 'string') {
        // 过滤常见的敏感信息模式
        info.message = info.message
          .replace(/password=[\w\d]+/gi, 'password=***')
          .replace(/token=[\w\d]+/gi, 'token=***')
          .replace(/authorization:\s*bearer\s+[\w\d\-\.]+/gi, 'authorization: bearer ***')
          .replace(/cookie:\s*[^;]+/gi, 'cookie: ***');
      }
      return info;
    })();
  }

  /**
   * 创建性能过滤器
   */
  static createPerformanceFilter(slowThreshold: number): winston.Logform.Format {
    return winston.format((info) => {
      if (info.duration && parseInt(String(info.duration), 10) < slowThreshold) {
        return false; // 跳过快速请求
      }
      return info;
    })();
  }

  /**
   * 创建环境过滤器
   */
  static createEnvironmentFilter(environment: string): winston.Logform.Format {
    return winston.format((info) => {
      // 在生产环境中过滤debug信息
      if (environment === 'production' && info.level === 'debug') {
        return false;
      }
      return info;
    })();
  }
}

/**
 * 核心日志器类
 */
export class CoreLogger {
  private logger: winston.Logger;
  private config: LoggingConfig;
  private context: LogContext;

  constructor(config: LoggingConfig, context?: LogContext) {
    this.config = config;
    this.context = context || {
      environment: process.env.NODE_ENV || 'development',
      service: 'petcare-blog',
      hostname: require('os').hostname()
    };

    this.logger = this.createLogger();
  }

  /**
   * 创建Winston日志器实例
   */
  private createLogger(): winston.Logger {
    const transports: winston.transport[] = [];

    // 控制台传输器
    if (this.config.console.enabled) {
      transports.push(TransportFactory.createConsole(this.config));
    }

    // 文件轮转传输器
    if (this.config.fileRotation.enabled) {
      transports.push(TransportFactory.createDailyRotateFile(this.config));
    }

    // 错误文件传输器
    if (this.config.error.enabled) {
      const errorTransport = TransportFactory.createErrorFile(this.config);
      if (errorTransport) {
        transports.push(errorTransport);
      }
    }

    // 创建日志器
    const logger = winston.createLogger({
      level: this.config.level,
      format: winston.format.combine(
        LogFilter.createSensitiveFilter(),
        LogFilter.createEnvironmentFilter(this.context.environment),
        winston.format.metadata()
      ),
      transports,
      defaultMeta: {
        service: this.context.service,
        environment: this.context.environment
      },
      exitOnError: false,
      handleExceptions: true,
      handleRejections: true
    });

    // 监听传输器事件
    this.setupTransportEventListeners(logger);

    return logger;
  }

  /**
   * 设置传输器事件监听
   */
  private setupTransportEventListeners(logger: winston.Logger): void {
    logger.on('error', (error) => {
      console.error('Logger error:', error);
    });

    // 监听轮转文件事件
    logger.transports.forEach((transport) => {
      if (transport instanceof DailyRotateFile) {
        transport.on('rotate', (oldFilename, newFilename) => {
          console.log(`Log rotated: ${oldFilename} -> ${newFilename}`);
        });

        transport.on('archive', (zipFilename) => {
          console.log(`Log archived: ${zipFilename}`);
        });

        transport.on('logRemoved', (removedFilename) => {
          console.log(`Old log removed: ${removedFilename}`);
        });
      }
    });
  }

  /**
   * 记录信息级别日志
   */
  info(message: string, meta?: LogMetadata): void {
    this.logger.info(message, meta);
  }

  /**
   * 记录警告级别日志
   */
  warn(message: string, meta?: LogMetadata): void {
    this.logger.warn(message, meta);
  }

  /**
   * 记录错误级别日志
   */
  error(message: string, error?: Error, meta?: LogMetadata): void {
    const logData: any = { ...meta };
    
    if (error) {
      logData.error = {
        name: error.name,
        message: error.message,
        stack: this.config.error.includeStack ? error.stack : undefined
      };
    }

    this.logger.error(message, logData);
  }

  /**
   * 记录调试级别日志
   */
  debug(message: string, meta?: LogMetadata): void {
    if (this.config.debug.enabled) {
      this.logger.debug(message, meta);
    }
  }

  /**
   * 记录性能日志
   */
  performance(message: string, duration: number, meta?: LogMetadata): void {
    if (!this.config.performance.enabled) {
      return;
    }

    const performanceMeta = {
      ...meta,
      duration,
      isSlowQuery: duration > this.config.performance.slowThreshold
    };

    this.logger.info(message, performanceMeta);
  }

  /**
   * 记录HTTP请求日志
   */
  http(message: string, requestData: any): void {
    this.logger.http(message, {
      ...requestData,
      type: 'http_request'
    });
  }

  /**
   * 获取原始Winston实例
   */
  getWinstonLogger(): winston.Logger {
    return this.logger;
  }

  /**
   * 创建子日志器
   */
  child(meta: LogMetadata): CoreLogger {
    const childLogger = new CoreLogger(this.config, {
      ...this.context,
      ...meta
    });
    
    return childLogger;
  }

  /**
   * 清理资源
   */
  close(): Promise<void> {
    return new Promise((resolve) => {
      this.logger.close();
      resolve();
    });
  }
}

/**
 * 日志器管理器
 */
export class LoggerManager {
  private static instance: LoggerManager;
  private loggers: Map<string, CoreLogger> = new Map();
  private defaultLogger: CoreLogger | null = null;

  private constructor() {}

  /**
   * 获取日志器管理器单例
   */
  static getInstance(): LoggerManager {
    if (!LoggerManager.instance) {
      LoggerManager.instance = new LoggerManager();
    }
    return LoggerManager.instance;
  }

  /**
   * 初始化默认日志器
   */
  initialize(config: LoggingConfig, context?: LogContext): CoreLogger {
    this.defaultLogger = new CoreLogger(config, context);
    this.loggers.set('default', this.defaultLogger);
    return this.defaultLogger;
  }

  /**
   * 获取默认日志器
   */
  getDefault(): CoreLogger {
    if (!this.defaultLogger) {
      throw new Error('Logger not initialized. Call initialize() first.');
    }
    return this.defaultLogger;
  }

  /**
   * 创建命名日志器
   */
  createLogger(name: string, config: LoggingConfig, context?: LogContext): CoreLogger {
    const logger = new CoreLogger(config, context || { 
      environment: process.env.NODE_ENV || 'development', 
      service: 'petcare-blog' 
    });
    
    this.loggers.set(name, logger);
    return logger;
  }

  /**
   * 获取命名日志器
   */
  getLogger(name: string): CoreLogger | undefined {
    return this.loggers.get(name);
  }

  /**
   * 关闭所有日志器
   */
  async closeAll(): Promise<void> {
    const promises = Array.from(this.loggers.values()).map(logger => logger.close());
    await Promise.all(promises);
    this.loggers.clear();
    this.defaultLogger = null;
  }
}

// 导出便捷函数
export const logger = {
  /**
   * 初始化日志系统
   */
  init: (config: LoggingConfig, context?: LogContext) => 
    LoggerManager.getInstance().initialize(config, context),

  /**
   * 获取默认日志器
   */
  get: () => LoggerManager.getInstance().getDefault(),

  /**
   * 创建子日志器
   */
  child: (meta: LogMetadata) => LoggerManager.getInstance().getDefault().child(meta),

  /**
   * 关闭日志系统
   */
  close: () => LoggerManager.getInstance().closeAll()
};

export default CoreLogger;