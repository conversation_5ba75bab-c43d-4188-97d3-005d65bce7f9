/**
 * 日志系统测试套件
 * 验证日志系统的所有功能和配置
 */

import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { LoggerManager } from './core';
import { LogFileManager } from './rotation';
import { PerformanceMonitor, RequestTracker } from './structured';
import { SecurityFilter, HttpLogFormatter } from './http';
import { ErrorHandler, Debugger, ErrorFingerprint } from './error';
import { LogStrategyFactory, LogStrategyManager, Environment } from './strategies';
import { LoggingConfig } from '../types/config';
import { HttpLogConfig, ErrorLogConfig, DebugLogConfig, PerformanceLogConfig, LogLevel, TransportType } from '../types/logger';

const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const unlink = promisify(fs.unlink);
const mkdir = promisify(fs.mkdir);

/**
 * 测试结果接口
 */
interface TestResult {
  name: string;
  passed: boolean;
  duration: number;
  error?: string;
  details?: any;
}

/**
 * 测试套件接口
 */
interface TestSuite {
  name: string;
  results: TestResult[];
  passed: number;
  failed: number;
  duration: number;
}

/**
 * 日志系统测试器
 */
export class LoggerTester {
  private testDir: string;
  private testResults: TestSuite[] = [];

  constructor() {
    this.testDir = path.join(process.cwd(), 'logs', 'tests');
    this.ensureTestDirectory();
  }

  /**
   * 确保测试目录存在
   */
  private async ensureTestDirectory(): Promise<void> {
    try {
      await mkdir(this.testDir, { recursive: true });
    } catch (error) {
      // 目录已存在
    }
  }

  /**
   * 运行所有测试
   */
  async runAllTests(): Promise<void> {
    console.log('🧪 开始运行日志系统测试套件...\n');

    const startTime = process.hrtime.bigint();

    try {
      // 清理测试环境
      await this.cleanup();

      // 运行各个测试套件
      await this.testCoreLogger();
      await this.testFileRotation();
      await this.testStructuredLogging();
      await this.testHttpLogging();
      await this.testErrorHandling();
      await this.testLogStrategies();
      await this.testPerformanceMonitoring();
      await this.testSecurityFiltering();
      await this.testIntegration();

    } catch (error) {
      console.error('测试套件执行失败:', error);
    } finally {
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000;

      // 生成测试报告
      this.generateTestReport(duration);
      
      // 清理测试文件
      await this.cleanup();
    }
  }

  /**
   * 测试核心日志器
   */
  private async testCoreLogger(): Promise<void> {
    const suite: TestSuite = {
      name: 'Core Logger Tests',
      results: [],
      passed: 0,
      failed: 0,
      duration: 0
    };

    const startTime = process.hrtime.bigint();

    // 测试1: 日志器初始化
    await this.runTest(suite, '日志器初始化', async () => {
      const config: LoggingConfig = {
        level: 'debug',
        file: path.join(this.testDir, 'test-core.log'),
        maxSize: '1m',
        maxFiles: 2,
        datePattern: 'YYYY-MM-DD',
        console: {
          enabled: false,
          level: 'debug',
          colorize: false
        },
        fileRotation: {
          enabled: false,
          filename: path.join(this.testDir, 'test-core-%DATE%.log'),
          datePattern: 'YYYY-MM-DD',
          maxSize: '1m',
          maxFiles: '2d',
          zippedArchive: false
        },
        http: { enabled: true, format: 'combined' },
        error: {
          enabled: true,
          filename: path.join(this.testDir, 'test-error.log'),
          includeStack: true,
          maxSize: '1m',
          maxFiles: 2
        },
        debug: { enabled: true, namespace: 'test:*' },
        performance: { enabled: true, slowThreshold: 100 }
      };

      const logger = LoggerManager.getInstance().initialize(config);
      
      if (!logger) {
        throw new Error('Logger initialization failed');
      }

      return { logger, config };
    });

    // 测试2: 日志级别功能
    await this.runTest(suite, '日志级别功能', async () => {
      const logger = LoggerManager.getInstance().getDefault();
      
      logger.debug('Debug message');
      logger.info('Info message');
      logger.warn('Warning message');
      logger.error('Error message', new Error('Test error'));

      // 等待日志写入
      await this.wait(100);

      // 验证日志文件存在
      const logFile = path.join(this.testDir, 'test-core.log');
      if (!fs.existsSync(logFile)) {
        throw new Error('Log file not created');
      }

      const logContent = await readFile(logFile, 'utf8');
      
      if (!logContent.includes('Info message') || 
          !logContent.includes('Warning message') || 
          !logContent.includes('Error message')) {
        throw new Error('Log messages not written correctly');
      }

      return { logContent };
    });

    // 测试3: 子日志器
    await this.runTest(suite, '子日志器功能', async () => {
      const logger = LoggerManager.getInstance().getDefault();
      const childLogger = logger.child({ module: 'test-module', requestId: 'req-123' });
      
      childLogger.info('Child logger message');
      
      await this.wait(100);

      return { childLogger };
    });

    const endTime = process.hrtime.bigint();
    suite.duration = Number(endTime - startTime) / 1000000;
    this.testResults.push(suite);
  }

  /**
   * 测试文件轮转
   */
  private async testFileRotation(): Promise<void> {
    const suite: TestSuite = {
      name: 'File Rotation Tests',
      results: [],
      passed: 0,
      failed: 0,
      duration: 0
    };

    const startTime = process.hrtime.bigint();

    // 测试1: 文件管理器初始化
    await this.runTest(suite, '文件管理器初始化', async () => {
      const config: LoggingConfig = {
        level: 'info',
        file: path.join(this.testDir, 'rotation-test.log'),
        maxSize: '1k', // 小尺寸便于测试
        maxFiles: 3,
        datePattern: 'YYYY-MM-DD',
        console: { enabled: false, level: 'info', colorize: false },
        fileRotation: {
          enabled: true,
          filename: path.join(this.testDir, 'rotation-%DATE%.log'),
          datePattern: 'YYYY-MM-DD',
          maxSize: '1k',
          maxFiles: '3d',
          zippedArchive: false
        },
        http: { enabled: false, format: 'combined' },
        error: { enabled: false, filename: '', includeStack: false, maxSize: '1m', maxFiles: 1 },
        debug: { enabled: false, namespace: '' },
        performance: { enabled: false, slowThreshold: 1000 }
      };

      const manager = new LogFileManager(config);
      
      return { manager, config };
    });

    // 测试2: 获取日志文件列表
    await this.runTest(suite, '获取日志文件列表', async () => {
      const config = this.testResults[this.testResults.length - 1]?.results[0]?.details?.config;
      const manager = new LogFileManager(config);
      
      // 创建一些测试文件
      await writeFile(path.join(this.testDir, 'test-1.log'), 'test content 1');
      await writeFile(path.join(this.testDir, 'test-2.log'), 'test content 2');
      
      const files = await manager.getLogFiles(/\.log$/);
      
      if (files.length < 2) {
        throw new Error('Failed to find test log files');
      }

      return { fileCount: files.length, files };
    });

    // 测试3: 文件压缩
    await this.runTest(suite, '文件压缩', async () => {
      const config = this.testResults[this.testResults.length - 1]?.results[0]?.details?.config;
      const manager = new LogFileManager(config);
      
      const testFile = path.join(this.testDir, 'compress-test.log');
      await writeFile(testFile, 'This is a test file for compression.');
      
      const compressedFile = await manager.compressFile(testFile);
      
      if (!fs.existsSync(compressedFile) || fs.existsSync(testFile)) {
        throw new Error('File compression failed');
      }

      return { compressedFile };
    });

    const endTime = process.hrtime.bigint();
    suite.duration = Number(endTime - startTime) / 1000000;
    this.testResults.push(suite);
  }

  /**
   * 测试结构化日志
   */
  private async testStructuredLogging(): Promise<void> {
    const suite: TestSuite = {
      name: 'Structured Logging Tests',
      results: [],
      passed: 0,
      failed: 0,
      duration: 0
    };

    const startTime = process.hrtime.bigint();

    // 测试1: 性能监控器
    await this.runTest(suite, '性能监控器', async () => {
      const config: PerformanceLogConfig = {
        enabled: true,
        slowThreshold: 100,
        includeParams: true,
        includeResultCount: true,
        samplingRate: 1.0
      };

      const logger = LoggerManager.getInstance().getDefault();
      const monitor = new PerformanceMonitor(config, logger);
      
      monitor.start();
      
      // 记录一些指标
      monitor.recordMetric({
        name: 'test.metric',
        value: 150,
        unit: 'ms',
        timestamp: new Date(),
        tags: { type: 'test' }
      });

      const metrics = monitor.getMetrics('test.metric');
      
      if (metrics.length === 0) {
        throw new Error('Performance metric not recorded');
      }

      monitor.stop();

      return { metricsCount: metrics.length };
    });

    // 测试2: 请求追踪器
    await this.runTest(suite, '请求追踪器', async () => {
      const logger = LoggerManager.getInstance().getDefault();
      const tracker = new RequestTracker(logger);
      
      const requestId = 'test-req-123';
      tracker.startRequest(requestId, 'GET', '/api/test');
      
      // 模拟一些数据库查询
      tracker.addDatabaseQuery(requestId, {
        query: 'SELECT * FROM users',
        duration: 50,
        rowCount: 10
      });

      tracker.recordCacheHit(requestId);
      tracker.recordCacheMiss(requestId);

      await this.wait(50);

      const completedRequest = tracker.endRequest(requestId, 200);
      
      if (!completedRequest || !completedRequest.duration) {
        throw new Error('Request tracking failed');
      }

      return { request: completedRequest };
    });

    const endTime = process.hrtime.bigint();
    suite.duration = Number(endTime - startTime) / 1000000;
    this.testResults.push(suite);
  }

  /**
   * 测试HTTP日志
   */
  private async testHttpLogging(): Promise<void> {
    const suite: TestSuite = {
      name: 'HTTP Logging Tests',
      results: [],
      passed: 0,
      failed: 0,
      duration: 0
    };

    const startTime = process.hrtime.bigint();

    // 测试1: HTTP日志格式化器
    await this.runTest(suite, 'HTTP日志格式化器', async () => {
      const config: HttpLogConfig = {
        enabled: true,
        format: 'combined',
        includeSensitive: false,
        maxBodySize: 1024,
        maxResponseSize: 1024
      };

      const formatter = new HttpLogFormatter(config);
      
      const logData = {
        requestId: 'req-123',
        method: 'GET',
        url: '/api/test',
        originalUrl: '/api/test?param=value',
        path: '/api/test',
        query: { param: 'value' },
        params: {},
        headers: { 'user-agent': 'test-agent' },
        userAgent: 'test-agent',
        ip: '127.0.0.1',
        statusCode: 200,
        responseTime: 150,
        responseSize: 1024,
        protocol: 'http',
        secure: false,
        xhr: false,
        timestamp: new Date()
      };

      const formatted = formatter.format(logData);
      
      if (!formatted.includes('GET') || !formatted.includes('/api/test') || !formatted.includes('200')) {
        throw new Error('HTTP log formatting failed');
      }

      return { formatted };
    });

    // 测试2: 安全过滤器
    await this.runTest(suite, '安全过滤器', async () => {
      const headers = {
        'authorization': 'Bearer secret-token',
        'cookie': 'session=abc123',
        'user-agent': 'test-agent',
        'accept': 'application/json'
      };

      const filtered = SecurityFilter.filterHeaders(headers);
      
      if (filtered.authorization !== '***FILTERED***' || 
          filtered.cookie !== '***FILTERED***' ||
          filtered['user-agent'] !== 'test-agent') {
        throw new Error('Security filtering failed');
      }

      return { filtered };
    });

    const endTime = process.hrtime.bigint();
    suite.duration = Number(endTime - startTime) / 1000000;
    this.testResults.push(suite);
  }

  /**
   * 测试错误处理
   */
  private async testErrorHandling(): Promise<void> {
    const suite: TestSuite = {
      name: 'Error Handling Tests',
      results: [],
      passed: 0,
      failed: 0,
      duration: 0
    };

    const startTime = process.hrtime.bigint();

    // 测试1: 错误处理器
    await this.runTest(suite, '错误处理器', async () => {
      const config: ErrorLogConfig = {
        enabled: true,
        includeStack: true,
        includeRequestContext: true,
        levelMapping: {
          'ValidationError': LogLevel.WARN,
          'CriticalError': LogLevel.ERROR
        },
        sensitiveFields: ['password', 'token']
      };

      const logger = LoggerManager.getInstance().getDefault();
      const handler = new ErrorHandler(config, logger);
      
      const testError = new Error('Test error message');
      const context = {
        requestId: 'req-123',
        userId: 'user-456',
        url: '/api/test',
        password: 'secret' // 应该被过滤
      };

      const errorInfo = handler.handleError(testError, context);
      
      if (!errorInfo.id || !errorInfo.fingerprint || errorInfo.context?.password !== '***SANITIZED***') {
        throw new Error('Error handling failed');
      }

      return { errorInfo };
    });

    // 测试2: 错误指纹生成
    await this.runTest(suite, '错误指纹生成', async () => {
      const error1 = new Error('Database connection failed');
      const error2 = new Error('Database connection failed');
      const error3 = new Error('Authentication failed');

      const fingerprint1 = ErrorFingerprint.generate(error1, { url: '/api/users' });
      const fingerprint2 = ErrorFingerprint.generate(error2, { url: '/api/users' });
      const fingerprint3 = ErrorFingerprint.generate(error3, { url: '/api/users' });

      if (fingerprint1 !== fingerprint2 || fingerprint1 === fingerprint3) {
        throw new Error('Error fingerprint generation failed');
      }

      return { fingerprint1, fingerprint2, fingerprint3 };
    });

    // 测试3: 调试器
    await this.runTest(suite, '调试器', async () => {
      const config: DebugLogConfig = {
        enabled: true,
        namespaces: ['test:*'],
        includeTimestamp: true,
        includeFileInfo: true,
        maxLevel: LogLevel.DEBUG,
        targets: [TransportType.CONSOLE]
      };

      const logger = LoggerManager.getInstance().getDefault();
      const debuggerInstance = new Debugger(config, logger);
      
      debuggerInstance.debug('test:module', 'Debug message', { data: 'test' });
      
      const timer = debuggerInstance.time('test:performance', 'test-operation');
      await this.wait(10);
      timer(); // 结束计时

      const stats = debuggerInstance.getDebugStats();
      
      if (stats.totalEntries === 0) {
        throw new Error('Debug logging failed');
      }

      return { stats };
    });

    const endTime = process.hrtime.bigint();
    suite.duration = Number(endTime - startTime) / 1000000;
    this.testResults.push(suite);
  }

  /**
   * 测试日志策略
   */
  private async testLogStrategies(): Promise<void> {
    const suite: TestSuite = {
      name: 'Log Strategy Tests',
      results: [],
      passed: 0,
      failed: 0,
      duration: 0
    };

    const startTime = process.hrtime.bigint();

    // 测试1: 环境策略
    await this.runTest(suite, '环境策略', async () => {
      const environments: Environment[] = ['development', 'test', 'staging', 'production'];
      const strategies: any = {};

      environments.forEach(env => {
        strategies[env] = LogStrategyFactory.getStrategy(env);
      });

      // 验证不同环境有不同配置
      if (strategies.development.logging.level === strategies.production.logging.level) {
        throw new Error('Environment strategies should be different');
      }

      if (strategies.production.logging.console.enabled === true) {
        throw new Error('Production should not enable console logging');
      }

      return { strategies };
    });

    // 测试2: 策略管理器
    await this.runTest(suite, '策略管理器', async () => {
      LogStrategyManager.initialize('test');
      
      const currentStrategy = LogStrategyManager.getCurrentStrategy();
      const currentEnv = LogStrategyManager.getCurrentEnvironment();
      
      if (currentEnv !== 'test' || !currentStrategy) {
        throw new Error('Strategy manager initialization failed');
      }

      return { currentEnv, strategy: currentStrategy };
    });

    const endTime = process.hrtime.bigint();
    suite.duration = Number(endTime - startTime) / 1000000;
    this.testResults.push(suite);
  }

  /**
   * 测试性能监控
   */
  private async testPerformanceMonitoring(): Promise<void> {
    const suite: TestSuite = {
      name: 'Performance Monitoring Tests',
      results: [],
      passed: 0,
      failed: 0,
      duration: 0
    };

    const startTime = process.hrtime.bigint();

    // 测试1: 性能指标收集
    await this.runTest(suite, '性能指标收集', async () => {
      const config: PerformanceLogConfig = {
        enabled: true,
        slowThreshold: 50,
        includeParams: true,
        includeResultCount: true,
        samplingRate: 1.0
      };

      const logger = LoggerManager.getInstance().getDefault();
      const monitor = new PerformanceMonitor(config, logger);
      
      monitor.start();
      
      // 生成一些测试指标
      for (let i = 0; i < 5; i++) {
        monitor.recordMetric({
          name: 'test.operation',
          value: Math.random() * 100,
          unit: 'ms',
          timestamp: new Date(),
          tags: { iteration: i.toString() }
        });
      }

      const metrics = monitor.getMetrics('test.operation');
      const summary = monitor.getMetricsSummary('test.operation');
      
      monitor.stop();

      if (metrics.length !== 5 || !summary) {
        throw new Error('Performance monitoring failed');
      }

      return { metricsCount: metrics.length, summary };
    });

    const endTime = process.hrtime.bigint();
    suite.duration = Number(endTime - startTime) / 1000000;
    this.testResults.push(suite);
  }

  /**
   * 测试安全过滤
   */
  private async testSecurityFiltering(): Promise<void> {
    const suite: TestSuite = {
      name: 'Security Filtering Tests',
      results: [],
      passed: 0,
      failed: 0,
      duration: 0
    };

    const startTime = process.hrtime.bigint();

    // 测试1: 查询参数过滤
    await this.runTest(suite, '查询参数过滤', async () => {
      const query = {
        username: 'testuser',
        password: 'secret123',
        token: 'abc123',
        search: 'query'
      };

      const filtered = SecurityFilter.filterQuery(query);
      
      if (filtered.password !== '***FILTERED***' || 
          filtered.token !== '***FILTERED***' ||
          filtered.username !== 'testuser') {
        throw new Error('Query parameter filtering failed');
      }

      return { filtered };
    });

    // 测试2: 请求体过滤
    await this.runTest(suite, '请求体过滤', async () => {
      const body = {
        user: {
          name: 'Test User',
          password: 'secret',
          profile: {
            email: '<EMAIL>',
            apiKey: 'key123'
          }
        },
        data: 'normal data'
      };

      const filtered = SecurityFilter.filterBody(body);
      
      if (filtered.user.password !== '***FILTERED***' ||
          filtered.user.profile.apiKey !== '***FILTERED***' ||
          filtered.user.name !== 'Test User') {
        throw new Error('Request body filtering failed');
      }

      return { filtered };
    });

    const endTime = process.hrtime.bigint();
    suite.duration = Number(endTime - startTime) / 1000000;
    this.testResults.push(suite);
  }

  /**
   * 测试集成功能
   */
  private async testIntegration(): Promise<void> {
    const suite: TestSuite = {
      name: 'Integration Tests',
      results: [],
      passed: 0,
      failed: 0,
      duration: 0
    };

    const startTime = process.hrtime.bigint();

    // 测试1: 完整日志工作流
    await this.runTest(suite, '完整日志工作流', async () => {
      // 初始化完整的日志系统
      const strategy = LogStrategyFactory.getStrategy('development');
      const logger = LoggerManager.getInstance().initialize(strategy.logging);
      
      // 创建各种组件
      const requestTracker = new RequestTracker(logger);
      const performanceMonitor = new PerformanceMonitor(strategy.performance, logger);
      const errorHandler = new ErrorHandler(strategy.error, logger);
      
      // 模拟完整的请求处理流程
      const requestId = 'integration-test-123';
      requestTracker.startRequest(requestId, 'POST', '/api/integration-test');
      
      performanceMonitor.start();
      
      // 模拟一些操作
      await this.wait(10);
      
      performanceMonitor.recordMetric({
        name: 'integration.test',
        value: 25,
        unit: 'ms',
        timestamp: new Date()
      });

      requestTracker.addDatabaseQuery(requestId, {
        query: 'INSERT INTO tests VALUES (?)',
        duration: 15,
        rowCount: 1
      });

      // 模拟错误处理
      try {
        throw new Error('Integration test error');
      } catch (error) {
        errorHandler.handleError(error as Error, {
          requestId,
          url: '/api/integration-test'
        });
      }

      const completedRequest = requestTracker.endRequest(requestId, 201);
      
      performanceMonitor.stop();

      if (!completedRequest || completedRequest.dbQueries?.length !== 1) {
        throw new Error('Integration test failed');
      }

      return { 
        completedRequest,
        dbQueries: completedRequest.dbQueries?.length,
        duration: completedRequest.duration
      };
    });

    const endTime = process.hrtime.bigint();
    suite.duration = Number(endTime - startTime) / 1000000;
    this.testResults.push(suite);
  }

  /**
   * 运行单个测试
   */
  private async runTest(
    suite: TestSuite, 
    testName: string, 
    testFn: () => Promise<any>
  ): Promise<void> {
    const startTime = process.hrtime.bigint();
    const result: TestResult = {
      name: testName,
      passed: false,
      duration: 0,
      details: undefined
    };

    try {
      result.details = await testFn();
      result.passed = true;
      suite.passed++;
      console.log(`  ✅ ${testName}`);
    } catch (error) {
      result.passed = false;
      result.error = (error as Error).message;
      suite.failed++;
      console.log(`  ❌ ${testName}: ${result.error}`);
    }

    const endTime = process.hrtime.bigint();
    result.duration = Number(endTime - startTime) / 1000000;

    suite.results.push(result);
  }

  /**
   * 生成测试报告
   */
  private generateTestReport(totalDuration: number): void {
    console.log('\n📊 日志系统测试报告');
    console.log('='.repeat(50));

    let totalTests = 0;
    let totalPassed = 0;
    let totalFailed = 0;

    this.testResults.forEach(suite => {
      totalTests += suite.results.length;
      totalPassed += suite.passed;
      totalFailed += suite.failed;

      console.log(`\n${suite.name}:`);
      console.log(`  ✅ 通过: ${suite.passed}`);
      console.log(`  ❌ 失败: ${suite.failed}`);
      console.log(`  ⏱️  耗时: ${suite.duration.toFixed(2)}ms`);

      if (suite.failed > 0) {
        suite.results.forEach(result => {
          if (!result.passed) {
            console.log(`    - ${result.name}: ${result.error}`);
          }
        });
      }
    });

    console.log('\n总体统计:');
    console.log(`  📋 总测试数: ${totalTests}`);
    console.log(`  ✅ 通过数量: ${totalPassed}`);
    console.log(`  ❌ 失败数量: ${totalFailed}`);
    console.log(`  📊 成功率: ${((totalPassed / totalTests) * 100).toFixed(1)}%`);
    console.log(`  ⏱️  总耗时: ${totalDuration.toFixed(2)}ms`);

    if (totalFailed === 0) {
      console.log('\n🎉 所有测试通过！日志系统工作正常。');
    } else {
      console.log('\n⚠️  部分测试失败，请检查日志系统配置。');
    }
  }

  /**
   * 等待指定毫秒数
   */
  private wait(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * 清理测试文件
   */
  private async cleanup(): Promise<void> {
    try {
      const files = fs.readdirSync(this.testDir);
      for (const file of files) {
        await unlink(path.join(this.testDir, file));
      }
    } catch (error) {
      // 忽略清理错误
    }
  }

  /**
   * 获取测试结果
   */
  getTestResults(): TestSuite[] {
    return this.testResults;
  }
}

// 便捷的测试运行函数
export async function runLoggerTests(): Promise<TestSuite[]> {
  const tester = new LoggerTester();
  await tester.runAllTests();
  return tester.getTestResults();
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  runLoggerTests().catch(console.error);
}

export default LoggerTester;