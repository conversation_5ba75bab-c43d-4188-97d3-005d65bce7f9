/**
 * 结构化日志和性能监控模块
 * 提供统一的日志结构、性能指标收集和监控功能
 */

import { performance, PerformanceObserver } from 'perf_hooks';
import { EventEmitter } from 'events';
import { LogMetadata, LogContext, PerformanceLogConfig } from '../types/logger';
import { CoreLogger } from './core';

/**
 * 性能指标接口
 */
export interface PerformanceMetric {
  name: string;
  value: number;
  unit: string;
  timestamp: Date;
  tags?: Record<string, string>;
  metadata?: Record<string, any>;
}

/**
 * 请求性能数据接口
 */
export interface RequestPerformance {
  requestId: string;
  method: string;
  url: string;
  statusCode?: number;
  startTime: number;
  endTime?: number;
  duration?: number;
  memoryUsage?: NodeJS.MemoryUsage;
  cpuUsage?: NodeJS.CpuUsage;
  dbQueries?: QueryPerformance[];
  cacheHits?: number;
  cacheMisses?: number;
}

/**
 * 数据库查询性能接口
 */
export interface QueryPerformance {
  query: string;
  duration: number;
  rowCount?: number;
  cached?: boolean;
  error?: string;
}

/**
 * 系统资源使用接口
 */
export interface SystemMetrics {
  timestamp: Date;
  memory: {
    rss: number;
    heapTotal: number;
    heapUsed: number;
    external: number;
    arrayBuffers: number;
    heapUsedPercentage: number;
  };
  cpu: {
    user: number;
    system: number;
    userPercentage: number;
    systemPercentage: number;
  };
  process: {
    pid: number;
    uptime: number;
    activeHandles: number;
    activeRequests: number;
  };
  eventLoop: {
    delay: number;
    utilization: number;
  };
}

/**
 * 结构化日志条目接口
 */
export interface StructuredLogEntry {
  timestamp: string;
  level: string;
  message: string;
  service: string;
  environment: string;
  version?: string;
  traceId?: string;
  spanId?: string;
  userId?: string;
  requestId?: string;
  sessionId?: string;
  module?: string;
  function?: string;
  metadata?: Record<string, any>;
  performance?: RequestPerformance;
  error?: {
    name: string;
    message: string;
    stack?: string;
    code?: string | number;
  };
  tags?: Record<string, string>;
}

/**
 * 性能监控器
 */
export class PerformanceMonitor extends EventEmitter {
  private config: PerformanceLogConfig;
  private logger: CoreLogger;
  private metrics: Map<string, PerformanceMetric[]> = new Map();
  private observer: PerformanceObserver | null = null;
  private cpuStartUsage: NodeJS.CpuUsage | null = null;
  private intervalId: NodeJS.Timeout | null = null;

  constructor(config: PerformanceLogConfig, logger: CoreLogger) {
    super();
    this.config = config;
    this.logger = logger;
    this.cpuStartUsage = process.cpuUsage();
    this.setupPerformanceObserver();
  }

  /**
   * 设置性能观察器
   */
  private setupPerformanceObserver(): void {
    if (!this.config.enabled) return;

    try {
      this.observer = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          this.recordMetric({
            name: entry.name,
            value: entry.duration,
            unit: 'ms',
            timestamp: new Date(entry.startTime + performance.timeOrigin),
            tags: {
              type: entry.entryType
            }
          });
        });
      });

      this.observer.observe({ type: 'measure' });
      // 注释掉不支持的类型
      // this.observer.observe({ type: 'navigation' });
    } catch (error) {
      console.warn('Failed to setup performance observer:', error);
    }
  }

  /**
   * 开始监控
   */
  start(): void {
    if (!this.config.enabled) return;

    // 定期收集系统指标
    this.intervalId = setInterval(() => {
      this.collectSystemMetrics();
    }, 30000); // 每30秒收集一次

    console.log('Performance monitoring started');
  }

  /**
   * 停止监控
   */
  stop(): void {
    if (this.observer) {
      this.observer.disconnect();
      this.observer = null;
    }

    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }

    console.log('Performance monitoring stopped');
  }

  /**
   * 记录性能指标
   */
  recordMetric(metric: PerformanceMetric): void {
    const metricList = this.metrics.get(metric.name) || [];
    metricList.push(metric);

    // 保持最近1000条记录
    if (metricList.length > 1000) {
      metricList.splice(0, metricList.length - 1000);
    }

    this.metrics.set(metric.name, metricList);

    // 发出事件
    this.emit('metric', metric);

    // 记录慢操作
    if (metric.value > this.config.slowThreshold) {
      this.logger.warn(`Slow operation detected: ${metric.name}`, {
        extra: {
          duration: `${metric.value}${metric.unit}`,
          threshold: `${this.config.slowThreshold}ms`,
          ...metric.metadata
        }
      });
    }
  }

  /**
   * 收集系统资源指标
   */
  private collectSystemMetrics(): void {
    const memoryUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage(this.cpuStartUsage || undefined);
    
    const systemMetrics: SystemMetrics = {
      timestamp: new Date(),
      memory: {
        ...memoryUsage,
        heapUsedPercentage: (memoryUsage.heapUsed / memoryUsage.heapTotal) * 100
      },
      cpu: {
        user: cpuUsage.user,
        system: cpuUsage.system,
        userPercentage: (cpuUsage.user / 1000000) * 100, // 转换为百分比
        systemPercentage: (cpuUsage.system / 1000000) * 100
      },
      process: {
        pid: process.pid,
        uptime: process.uptime(),
        activeHandles: (process as any)._getActiveHandles().length,
        activeRequests: (process as any)._getActiveRequests().length
      },
      eventLoop: {
        delay: this.measureEventLoopDelay(),
        utilization: this.measureEventLoopUtilization()
      }
    };

    // 记录系统指标
    this.recordMetric({
      name: 'system.memory.heap_used',
      value: systemMetrics.memory.heapUsed,
      unit: 'bytes',
      timestamp: systemMetrics.timestamp,
      tags: { type: 'memory' }
    });

    this.recordMetric({
      name: 'system.memory.heap_used_percentage',
      value: systemMetrics.memory.heapUsedPercentage,
      unit: 'percent',
      timestamp: systemMetrics.timestamp,
      tags: { type: 'memory' }
    });

    this.recordMetric({
      name: 'system.cpu.user_percentage',
      value: systemMetrics.cpu.userPercentage,
      unit: 'percent',
      timestamp: systemMetrics.timestamp,
      tags: { type: 'cpu' }
    });

    // 更新CPU使用基准
    this.cpuStartUsage = process.cpuUsage();

    // 发出系统指标事件
    this.emit('systemMetrics', systemMetrics);

    // 检查资源使用告警
    this.checkResourceAlerts(systemMetrics);
  }

  /**
   * 测量事件循环延迟
   */
  private measureEventLoopDelay(): number {
    const start = process.hrtime.bigint();
    setImmediate(() => {
      const delay = Number(process.hrtime.bigint() - start) / 1000000; // 转换为毫秒
      return delay;
    });
    return 0; // 简化实现，实际应该是异步测量
  }

  /**
   * 测量事件循环利用率
   */
  private measureEventLoopUtilization(): number {
    // 简化实现，实际应该使用 perf_hooks.eventLoopUtilization()
    return 0.5;
  }

  /**
   * 检查资源使用告警
   */
  private checkResourceAlerts(metrics: SystemMetrics): void {
    // 内存使用率告警
    if (metrics.memory.heapUsedPercentage > 85) {
      this.logger.warn('High memory usage detected', {
        extra: {
          heapUsedPercentage: metrics.memory.heapUsedPercentage,
          heapUsed: metrics.memory.heapUsed,
          heapTotal: metrics.memory.heapTotal
        }
      });
    }

    // CPU使用率告警
    if (metrics.cpu.userPercentage > 80) {
      this.logger.warn('High CPU usage detected', {
        extra: {
          userPercentage: metrics.cpu.userPercentage,
          systemPercentage: metrics.cpu.systemPercentage
        }
      });
    }

    // 活跃句柄数告警
    if (metrics.process.activeHandles > 1000) {
      this.logger.warn('High number of active handles detected', {
        extra: {
          activeHandles: metrics.process.activeHandles,
          activeRequests: metrics.process.activeRequests
        }
      });
    }
  }

  /**
   * 获取指标数据
   */
  getMetrics(name?: string): PerformanceMetric[] {
    if (name) {
      return this.metrics.get(name) || [];
    }
    
    const allMetrics: PerformanceMetric[] = [];
    this.metrics.forEach(metricList => {
      allMetrics.push(...metricList);
    });
    
    return allMetrics.sort((a, b) => b.timestamp.getTime() - a.timestamp.getTime());
  }

  /**
   * 获取统计摘要
   */
  getMetricsSummary(name: string): {
    count: number;
    min: number;
    max: number;
    avg: number;
    p50: number;
    p95: number;
    p99: number;
  } | null {
    const metrics = this.metrics.get(name);
    if (!metrics || metrics.length === 0) {
      return null;
    }

    const values = metrics.map(m => m.value).sort((a, b) => a - b);
    const count = values.length;

    return {
      count,
      min: values[0],
      max: values[count - 1],
      avg: values.reduce((sum, val) => sum + val, 0) / count,
      p50: values[Math.floor(count * 0.5)],
      p95: values[Math.floor(count * 0.95)],
      p99: values[Math.floor(count * 0.99)]
    };
  }
}

/**
 * 请求追踪器
 */
export class RequestTracker {
  private requests: Map<string, RequestPerformance> = new Map();
  private logger: CoreLogger;

  constructor(logger: CoreLogger) {
    this.logger = logger;
  }

  /**
   * 开始追踪请求
   */
  startRequest(requestId: string, method: string, url: string): RequestPerformance {
    const request: RequestPerformance = {
      requestId,
      method,
      url,
      startTime: performance.now(),
      memoryUsage: process.memoryUsage(),
      cpuUsage: process.cpuUsage(),
      dbQueries: [],
      cacheHits: 0,
      cacheMisses: 0
    };

    this.requests.set(requestId, request);
    performance.mark(`request-start-${requestId}`);
    
    return request;
  }

  /**
   * 结束追踪请求
   */
  endRequest(requestId: string, statusCode: number): RequestPerformance | null {
    const request = this.requests.get(requestId);
    if (!request) {
      return null;
    }

    request.endTime = performance.now();
    request.duration = request.endTime - request.startTime;
    request.statusCode = statusCode;

    // 性能标记
    performance.mark(`request-end-${requestId}`);
    performance.measure(`request-${requestId}`, `request-start-${requestId}`, `request-end-${requestId}`);

    // 记录请求日志
    this.logRequestCompletion(request);

    // 清理
    this.requests.delete(requestId);
    
    return request;
  }

  /**
   * 添加数据库查询记录
   */
  addDatabaseQuery(requestId: string, query: QueryPerformance): void {
    const request = this.requests.get(requestId);
    if (request) {
      request.dbQueries = request.dbQueries || [];
      request.dbQueries.push(query);
    }
  }

  /**
   * 记录缓存命中
   */
  recordCacheHit(requestId: string): void {
    const request = this.requests.get(requestId);
    if (request) {
      request.cacheHits = (request.cacheHits || 0) + 1;
    }
  }

  /**
   * 记录缓存未命中
   */
  recordCacheMiss(requestId: string): void {
    const request = this.requests.get(requestId);
    if (request) {
      request.cacheMisses = (request.cacheMisses || 0) + 1;
    }
  }

  /**
   * 记录请求完成日志
   */
  private logRequestCompletion(request: RequestPerformance): void {
    const logData: LogMetadata = {
      requestId: request.requestId,
      duration: request.duration,
      extra: {
        method: request.method,
        url: request.url,
        statusCode: request.statusCode,
        dbQueries: request.dbQueries?.length || 0,
        cacheHits: request.cacheHits || 0,
        cacheMisses: request.cacheMisses || 0,
        slowQueries: request.dbQueries?.filter(q => q.duration > 100).length || 0
      }
    };

    if (request.duration! > 1000) {
      this.logger.warn(`Slow request: ${request.method} ${request.url}`, logData);
    } else {
      this.logger.info(`Request completed: ${request.method} ${request.url}`, logData);
    }
  }

  /**
   * 获取活跃请求数量
   */
  getActiveRequestCount(): number {
    return this.requests.size;
  }

  /**
   * 获取活跃请求列表
   */
  getActiveRequests(): RequestPerformance[] {
    return Array.from(this.requests.values());
  }
}

/**
 * 结构化日志格式化器
 */
export class StructuredLogger {
  private context: LogContext;
  private logger: CoreLogger;

  constructor(logger: CoreLogger, context: LogContext) {
    this.logger = logger;
    this.context = context;
  }

  /**
   * 创建结构化日志条目
   */
  createLogEntry(
    level: string,
    message: string,
    metadata?: LogMetadata,
    error?: Error,
    performance?: RequestPerformance
  ): StructuredLogEntry {
    const entry: StructuredLogEntry = {
      timestamp: new Date().toISOString(),
      level,
      message,
      service: this.context.service,
      environment: this.context.environment,
      version: this.context.version,
      traceId: this.context.traceId,
      spanId: this.context.spanId,
      userId: metadata?.userId,
      requestId: metadata?.requestId,
      sessionId: metadata?.sessionId,
      module: metadata?.module,
      function: metadata?.function,
      metadata: metadata?.extra,
      performance,
      tags: {
        hostname: this.context.hostname || 'unknown'
      }
    };

    if (error) {
      entry.error = {
        name: error.name,
        message: error.message,
        stack: error.stack,
        code: (error as any).code
      };
    }

    return entry;
  }

  /**
   * 记录结构化日志
   */
  log(
    level: string,
    message: string,
    metadata?: LogMetadata,
    error?: Error,
    performance?: RequestPerformance
  ): void {
    const entry = this.createLogEntry(level, message, metadata, error, performance);
    
    // 使用结构化日志记录
    switch (level) {
      case 'error':
        this.logger.error(entry.message, entry.error, entry.metadata);
        break;
      case 'warn':
        this.logger.warn(entry.message, entry.metadata);
        break;
      case 'info':
        this.logger.info(entry.message, entry.metadata);
        break;
      case 'debug':
        this.logger.debug(entry.message, entry.metadata);
        break;
      default:
        this.logger.info(message, metadata);
    }
  }

  /**
   * 创建子结构化日志器
   */
  child(additionalContext: Partial<LogContext>): StructuredLogger {
    const childContext = { ...this.context, ...additionalContext };
    return new StructuredLogger(this.logger, childContext);
  }
}

// 导出便捷函数
export const structuredLogging = {
  /**
   * 创建性能监控器
   */
  createPerformanceMonitor: (config: PerformanceLogConfig, logger: CoreLogger) =>
    new PerformanceMonitor(config, logger),

  /**
   * 创建请求追踪器
   */
  createRequestTracker: (logger: CoreLogger) => new RequestTracker(logger),

  /**
   * 创建结构化日志器
   */
  createStructuredLogger: (logger: CoreLogger, context: LogContext) =>
    new StructuredLogger(logger, context)
};

// 已在类声明时导出，移除重复export