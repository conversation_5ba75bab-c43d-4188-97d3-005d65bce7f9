/**
 * 日志系统统一入口文件
 * 导出所有日志相关的模块和功能
 */

// 核心日志器
export {
  CoreLogger,
  LoggerManager,
  LogFormatter,
  TransportFactory,
  LogFilter,
  logger
} from './core';

// 文件轮转和管理
export {
  LogFileManager,
  RotationScheduler,
  LogDirectoryWatcher,
  logRotation,
  type FileInfo,
  type RotationStats,
  type DiskSpaceInfo
} from './rotation';

// 结构化日志和性能监控
export {
  PerformanceMonitor,
  RequestTracker,
  StructuredLogger,
  structuredLogging,
  type PerformanceMetric,
  type RequestPerformance,
  type QueryPerformance,
  type SystemMetrics,
  type StructuredLogEntry
} from './structured';

// HTTP请求日志中间件
export {
  HttpLogger,
  HttpLogFormatter,
  SecurityFilter,
  httpLogging,
  initializeHttpLogger,
  getHttpLogger,
  type HttpLogData,
  type LoggingRequest
} from './http';

// 错误日志和调试工具
export {
  ErrorHandler,
  Debugger,
  ErrorFingerprint,
  errorLogging,
  type ErrorInfo,
  type DebugInfo,
  type ErrorSummary
} from './error';

// 日志策略配置
export {
  LogStrategyFactory,
  LogStrategyManager,
  logStrategies,
  type LogStrategyConfig,
  type Environment
} from './strategies';

// 测试套件
export {
  LoggerTester,
  runLoggerTests
} from './test-logger';

// 类型定义
export * from '../types/logger';

import { CoreLogger, LoggerManager } from './core';
import { LogFileManager, RotationScheduler } from './rotation';
import { PerformanceMonitor, RequestTracker, StructuredLogger } from './structured';
import { HttpLogger, initializeHttpLogger } from './http';
import { ErrorHandler, Debugger } from './error';
import { LogStrategyManager, Environment } from './strategies';

/**
 * 完整的日志系统管理器
 */
export class LoggingSystem {
  private static instance: LoggingSystem;
  
  // 核心组件
  private coreLogger: CoreLogger | null = null;
  private fileManager: LogFileManager | null = null;
  private rotationScheduler: RotationScheduler | null = null;
  private performanceMonitor: PerformanceMonitor | null = null;
  private requestTracker: RequestTracker | null = null;
  private httpLogger: HttpLogger | null = null;
  private errorHandler: ErrorHandler | null = null;
  private debuggerInstance: Debugger | null = null;
  private structuredLogger: StructuredLogger | null = null;
  
  // 状态
  private isInitialized: boolean = false;
  private environment: Environment = 'development';

  private constructor() {}

  /**
   * 获取日志系统单例
   */
  static getInstance(): LoggingSystem {
    if (!LoggingSystem.instance) {
      LoggingSystem.instance = new LoggingSystem();
    }
    return LoggingSystem.instance;
  }

  /**
   * 初始化整个日志系统
   */
  async initialize(environment?: Environment): Promise<void> {
    if (this.isInitialized) {
      console.warn('Logging system already initialized');
      return;
    }

    try {
      console.log('🚀 Initializing logging system...');
      
      // 确定环境
      this.environment = environment || (process.env.NODE_ENV as Environment) || 'development';
      
      // 初始化配置策略
      const strategy = LogStrategyManager.initialize(this.environment);
      
      // 初始化核心日志器
      this.coreLogger = LoggerManager.getInstance().initialize(strategy.logging, {
        environment: this.environment,
        service: 'petcare-blog',
        version: process.env.npm_package_version,
        hostname: require('os').hostname()
      });

      // 初始化文件管理器
      this.fileManager = new LogFileManager(strategy.logging);
      
      // 初始化轮转调度器
      if (strategy.logging.fileRotation.enabled) {
        this.rotationScheduler = new RotationScheduler(strategy.logging);
        this.rotationScheduler.start();
      }

      // 初始化性能监控器
      if (strategy.performance.enabled) {
        this.performanceMonitor = new PerformanceMonitor(strategy.performance, this.coreLogger);
        this.performanceMonitor.start();
      }

      // 初始化请求追踪器
      this.requestTracker = new RequestTracker(this.coreLogger);

      // 初始化HTTP日志器
      if (strategy.http.enabled) {
        this.httpLogger = initializeHttpLogger(strategy.http, this.coreLogger, this.requestTracker);
      }

      // 初始化错误处理器
      if (strategy.error.enabled) {
        this.errorHandler = new ErrorHandler(strategy.error, this.coreLogger);
        
        // 设置全局错误处理
        this.setupGlobalErrorHandlers();
      }

      // 初始化调试器
      if (strategy.debug.enabled) {
        this.debuggerInstance = new Debugger(strategy.debug, this.coreLogger);
      }

      // 初始化结构化日志器
      this.structuredLogger = new StructuredLogger(this.coreLogger, {
        environment: this.environment,
        service: 'petcare-blog',
        version: process.env.npm_package_version,
        hostname: require('os').hostname()
      });

      this.isInitialized = true;
      
      console.log(`✅ Logging system initialized for ${this.environment} environment`);
      this.logSystemInfo();
      
    } catch (error) {
      console.error('❌ Failed to initialize logging system:', error);
      throw error;
    }
  }

  /**
   * 设置全局错误处理器
   */
  private setupGlobalErrorHandlers(): void {
    if (!this.errorHandler) return;

    // 处理未捕获的异常
    process.on('uncaughtException', (error) => {
      this.errorHandler!.handleError(error, {
        type: 'uncaughtException',
        fatal: true
      });
      
      // 优雅关闭
      setTimeout(() => {
        process.exit(1);
      }, 1000);
    });

    // 处理未处理的Promise拒绝
    process.on('unhandledRejection', (reason, promise) => {
      const error = reason instanceof Error ? reason : new Error(String(reason));
      this.errorHandler!.handleError(error, {
        type: 'unhandledRejection',
        promise: promise.toString()
      });
    });

    // 处理进程警告
    process.on('warning', (warning) => {
      this.coreLogger!.warn('Process warning', {
        extra: {
          name: warning.name,
          message: warning.message,
          stack: warning.stack
        }
      });
    });
  }

  /**
   * 记录系统信息
   */
  private logSystemInfo(): void {
    this.coreLogger!.info('Logging system components initialized', {
      extra: {
        environment: this.environment,
        coreLogger: !!this.coreLogger,
        fileManager: !!this.fileManager,
        rotationScheduler: !!this.rotationScheduler,
        performanceMonitor: !!this.performanceMonitor,
        requestTracker: !!this.requestTracker,
        httpLogger: !!this.httpLogger,
        errorHandler: !!this.errorHandler,
        debuggerInstance: !!this.debuggerInstance,
        structuredLogger: !!this.structuredLogger,
        nodeVersion: process.version,
        platform: process.platform,
        arch: process.arch,
        pid: process.pid
      }
    });
  }

  /**
   * 获取核心日志器
   */
  getCoreLogger(): CoreLogger {
    if (!this.coreLogger) {
      throw new Error('Logging system not initialized');
    }
    return this.coreLogger;
  }

  /**
   * 获取HTTP日志器
   */
  getHttpLogger(): HttpLogger | null {
    return this.httpLogger;
  }

  /**
   * 获取错误处理器
   */
  getErrorHandler(): ErrorHandler | null {
    return this.errorHandler;
  }

  /**
   * 获取性能监控器
   */
  getPerformanceMonitor(): PerformanceMonitor | null {
    return this.performanceMonitor;
  }

  /**
   * 获取请求追踪器
   */
  getRequestTracker(): RequestTracker | null {
    return this.requestTracker;
  }

  /**
   * 获取调试器
   */
  getDebugger(): Debugger | null {
    return this.debuggerInstance;
  }

  /**
   * 获取结构化日志器
   */
  getStructuredLogger(): StructuredLogger | null {
    return this.structuredLogger;
  }

  /**
   * 获取系统统计信息
   */
  getSystemStats(): {
    environment: Environment;
    initialized: boolean;
    uptime: number;
    components: Record<string, boolean>;
    performance?: any;
    errors?: any;
  } {
    return {
      environment: this.environment,
      initialized: this.isInitialized,
      uptime: process.uptime(),
      components: {
        coreLogger: !!this.coreLogger,
        fileManager: !!this.fileManager,
        rotationScheduler: !!this.rotationScheduler,
        performanceMonitor: !!this.performanceMonitor,
        requestTracker: !!this.requestTracker,
        httpLogger: !!this.httpLogger,
        errorHandler: !!this.errorHandler,
        debuggerInstance: !!this.debuggerInstance,
        structuredLogger: !!this.structuredLogger
      },
      performance: this.performanceMonitor?.getMetrics(),
      errors: this.errorHandler?.getErrorStats()
    };
  }

  /**
   * 健康检查
   */
  async healthCheck(): Promise<{
    status: 'healthy' | 'degraded' | 'unhealthy';
    components: Record<string, 'ok' | 'error'>;
    details: any;
  }> {
    const health: {
      status: 'healthy' | 'degraded' | 'unhealthy';
      components: Record<string, 'ok' | 'error'>;
      details: any;
    } = {
      status: 'healthy',
      components: {},
      details: {}
    };

    // 检查核心日志器
    try {
      this.coreLogger!.debug('Health check test log');
      health.components.coreLogger = 'ok';
    } catch (error) {
      health.components.coreLogger = 'error';
      health.status = 'degraded';
      health.details.coreLoggerError = (error as Error).message;
    }

    // 检查文件管理器
    if (this.fileManager) {
      try {
        await this.fileManager.getLogFiles();
        health.components.fileManager = 'ok';
      } catch (error) {
        health.components.fileManager = 'error';
        health.status = 'degraded';
        health.details.fileManagerError = (error as Error).message;
      }
    }

    // 检查轮转调度器
    if (this.rotationScheduler) {
      const status = this.rotationScheduler.getStatus();
      health.components.rotationScheduler = status.isRunning ? 'ok' : 'error';
      if (!status.isRunning) {
        health.status = 'degraded';
      }
    }

    // 如果有组件出错，标记为不健康
    const errorCount = Object.values(health.components).filter(status => status === 'error').length;
    if (errorCount > 0) {
      health.status = errorCount >= 2 ? 'unhealthy' : 'degraded';
    }

    return health;
  }

  /**
   * 优雅关闭日志系统
   */
  async shutdown(): Promise<void> {
    if (!this.isInitialized) {
      return;
    }

    console.log('🔄 Shutting down logging system...');

    try {
      // 停止轮转调度器
      if (this.rotationScheduler) {
        this.rotationScheduler.stop();
      }

      // 停止性能监控器
      if (this.performanceMonitor) {
        this.performanceMonitor.stop();
      }

      // 关闭所有日志器
      await LoggerManager.getInstance().closeAll();

      this.isInitialized = false;
      
      console.log('✅ Logging system shutdown complete');
    } catch (error) {
      console.error('❌ Error during logging system shutdown:', error);
      throw error;
    }
  }

  /**
   * 检查是否已初始化
   */
  isReady(): boolean {
    return this.isInitialized;
  }
}

// 导出便捷的全局函数
export const loggingSystem = LoggingSystem.getInstance();

/**
 * 便捷的初始化函数
 */
export async function initializeLogging(environment?: Environment): Promise<LoggingSystem> {
  await loggingSystem.initialize(environment);
  return loggingSystem;
}

/**
 * 便捷的获取日志器函数
 */
export function getLogger(): CoreLogger {
  return loggingSystem.getCoreLogger();
}

/**
 * 便捷的错误记录函数
 */
export function logError(error: Error, context?: any): void {
  const errorHandler = loggingSystem.getErrorHandler();
  if (errorHandler) {
    errorHandler.handleError(error, context);
  } else {
    console.error('Error (no error handler available):', error);
  }
}

/**
 * 便捷的性能记录函数
 */
export function recordPerformance(name: string, value: number, unit: string = 'ms', tags?: Record<string, string>): void {
  const monitor = loggingSystem.getPerformanceMonitor();
  if (monitor) {
    monitor.recordMetric({
      name,
      value,
      unit,
      timestamp: new Date(),
      tags
    });
  }
}

/**
 * 便捷的调试日志函数
 */
export function debug(namespace: string, message: string, data?: any): void {
  const debuggerInstance = loggingSystem.getDebugger();
  if (debuggerInstance) {
    debuggerInstance.debug(namespace, message, data);
  }
}

// 默认导出日志系统实例
export default loggingSystem;