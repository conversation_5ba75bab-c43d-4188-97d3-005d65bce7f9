/**
 * 错误日志和调试工具模块
 * 提供错误处理、堆栈跟踪、调试信息和异常处理功能
 */

import { Request, Response, NextFunction } from 'express';
import { EventEmitter } from 'events';
import path from 'path';
import { CoreLogger } from './core';
import { ErrorLogConfig, DebugLogConfig } from '../types/logger';

/**
 * 错误信息接口
 */
export interface ErrorInfo {
  id: string;
  timestamp: Date;
  level: 'error' | 'warning' | 'critical';
  name: string;
  message: string;
  stack?: string;
  code?: string | number;
  statusCode?: number;
  requestId?: string;
  userId?: string;
  url?: string;
  method?: string;
  userAgent?: string;
  ip?: string;
  context?: Record<string, any>;
  environment: string;
  service: string;
  version?: string;
  fingerprint?: string;
}

/**
 * 调试信息接口
 */
export interface DebugInfo {
  timestamp: Date;
  namespace: string;
  level: string;
  message: string;
  data?: any;
  file?: string;
  line?: number;
  function?: string;
  duration?: number;
  memoryUsage?: NodeJS.MemoryUsage;
}

/**
 * 错误聚合接口
 */
export interface ErrorSummary {
  fingerprint: string;
  firstSeen: Date;
  lastSeen: Date;
  count: number;
  error: ErrorInfo;
  affectedUsers: Set<string>;
  affectedRequests: string[];
}

/**
 * 错误指纹生成器
 */
export class ErrorFingerprint {
  /**
   * 生成错误指纹用于聚合相似错误
   */
  static generate(error: Error, context?: Record<string, any>): string {
    // 清理堆栈跟踪中的动态信息
    const cleanStack = this.cleanStackTrace(error.stack || '');
    
    // 创建指纹字符串
    const fingerprint = [
      error.name,
      error.message,
      cleanStack.split('\n').slice(0, 3).join('\n'), // 只取前3行堆栈
      context?.url || '',
      context?.method || ''
    ].join('|');

    // 生成哈希
    return this.simpleHash(fingerprint);
  }

  /**
   * 清理堆栈跟踪
   */
  private static cleanStackTrace(stack: string): string {
    return stack
      .replace(/:\d+:\d+/g, '') // 移除行号和列号
      .replace(/\(.+node_modules.+\)/g, '(node_modules)') // 简化node_modules路径
      .replace(/\(.+\/\)/g, '(/)'); // 简化绝对路径
  }

  /**
   * 简单哈希函数
   */
  private static simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // 转换为32位整数
    }
    return Math.abs(hash).toString(16);
  }
}

/**
 * 错误处理器
 */
export class ErrorHandler extends EventEmitter {
  private config: ErrorLogConfig;
  private logger: CoreLogger;
  private errorSummaries: Map<string, ErrorSummary> = new Map();

  constructor(config: ErrorLogConfig, logger: CoreLogger) {
    super();
    this.config = config;
    this.logger = logger;
  }

  /**
   * 处理错误
   */
  handleError(error: Error, context?: Record<string, any>): ErrorInfo {
    const errorInfo: ErrorInfo = {
      id: this.generateErrorId(),
      timestamp: new Date(),
      level: this.determineErrorLevel(error),
      name: error.name,
      message: error.message,
      stack: this.config.includeStack ? this.sanitizeStack(error.stack) : undefined,
      code: (error as any).code,
      statusCode: (error as any).statusCode,
      requestId: context?.requestId,
      userId: context?.userId,
      url: context?.url,
      method: context?.method,
      userAgent: context?.userAgent,
      ip: context?.ip,
      context: this.sanitizeContext(context),
      environment: process.env.NODE_ENV || 'development',
      service: 'petcare-blog',
      version: process.env.npm_package_version,
      fingerprint: ErrorFingerprint.generate(error, context)
    };

    // 聚合相似错误
    this.aggregateError(errorInfo);

    // 记录错误日志
    this.logError(errorInfo);

    // 只有在有监听器时才发出错误事件
    if (this.listenerCount('error') > 0) {
      this.emit('error', errorInfo);
    }

    // 检查是否需要发送通知
    this.checkNotifications(errorInfo);

    return errorInfo;
  }

  /**
   * 生成错误ID
   */
  private generateErrorId(): string {
    return `err_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  /**
   * 确定错误级别
   */
  private determineErrorLevel(error: Error): 'error' | 'warning' | 'critical' {
    // 致命错误
    if (error.name === 'OutOfMemoryError' || 
        error.name === 'MaxListenersExceededWarning' ||
        (error as any).code === 'ENOSPC') {
      return 'critical';
    }

    // 警告级别错误
    if (error.name === 'ValidationError' ||
        error.name === 'CastError' ||
        (error as any).statusCode < 500) {
      return 'warning';
    }

    return 'error';
  }

  /**
   * 清理堆栈跟踪中的敏感信息
   */
  private sanitizeStack(stack?: string): string | undefined {
    if (!stack) return undefined;

    return stack
      .replace(/password=[\w\d]+/gi, 'password=***')
      .replace(/token=[\w\d]+/gi, 'token=***')
      .replace(/api[_-]?key=[\w\d]+/gi, 'api_key=***');
  }

  /**
   * 清理上下文中的敏感信息
   */
  private sanitizeContext(context?: Record<string, any>): Record<string, any> | undefined {
    if (!context) return undefined;

    const sanitized = { ...context };
    
    this.config.sensitiveFields.forEach(field => {
      if (sanitized[field]) {
        sanitized[field] = '***SANITIZED***';
      }
    });

    return sanitized;
  }

  /**
   * 聚合相似错误
   */
  private aggregateError(errorInfo: ErrorInfo): void {
    const fingerprint = errorInfo.fingerprint!;
    const existing = this.errorSummaries.get(fingerprint);

    if (existing) {
      existing.count++;
      existing.lastSeen = errorInfo.timestamp;
      existing.affectedRequests.push(errorInfo.requestId!);
      
      if (errorInfo.userId) {
        existing.affectedUsers.add(errorInfo.userId);
      }
    } else {
      this.errorSummaries.set(fingerprint, {
        fingerprint,
        firstSeen: errorInfo.timestamp,
        lastSeen: errorInfo.timestamp,
        count: 1,
        error: errorInfo,
        affectedUsers: new Set(errorInfo.userId ? [errorInfo.userId] : []),
        affectedRequests: errorInfo.requestId ? [errorInfo.requestId] : []
      });
    }
  }

  /**
   * 记录错误日志
   */
  private logError(errorInfo: ErrorInfo): void {
    if (!this.config.enabled) return;

    const logData = {
      errorId: errorInfo.id,
      fingerprint: errorInfo.fingerprint,
      requestId: errorInfo.requestId,
      userId: errorInfo.userId,
      extra: {
        code: errorInfo.code,
        statusCode: errorInfo.statusCode,
        url: errorInfo.url,
        method: errorInfo.method,
        userAgent: errorInfo.userAgent,
        ip: errorInfo.ip,
        context: errorInfo.context
      }
    };

    switch (errorInfo.level) {
      case 'critical':
        this.logger.error(`CRITICAL ERROR: ${errorInfo.message}`, new Error(errorInfo.message), logData);
        break;
      case 'error':
        this.logger.error(`ERROR: ${errorInfo.message}`, new Error(errorInfo.message), logData);
        break;
      case 'warning':
        this.logger.warn(`WARNING: ${errorInfo.message}`, logData);
        break;
    }
  }

  /**
   * 检查通知条件
   */
  private checkNotifications(errorInfo: ErrorInfo): void {
    if (!this.config.notifications?.enabled) return;

    const shouldNotify = this.shouldSendNotification(errorInfo);
    if (shouldNotify) {
      this.emit('notification', {
        type: 'error',
        level: errorInfo.level,
        error: errorInfo,
        summary: this.errorSummaries.get(errorInfo.fingerprint!)
      });
    }
  }

  /**
   * 判断是否应该发送通知
   */
  private shouldSendNotification(errorInfo: ErrorInfo): boolean {
    if (!this.config.notifications) return false;

    const { threshold, cooldown } = this.config.notifications;
    
    // 检查错误级别
    const levelPriority: Record<string, number> = { warning: 1, error: 2, critical: 3 };
    const thresholdPriority: Record<string, number> = { warning: 1, error: 2, critical: 3 };
    
    if (levelPriority[errorInfo.level] < thresholdPriority[threshold]) {
      return false;
    }

    // 检查冷却时间
    const summary = this.errorSummaries.get(errorInfo.fingerprint!);
    if (summary && summary.count > 1) {
      const timeSinceFirstSeen = Date.now() - summary.firstSeen.getTime();
      if (timeSinceFirstSeen < cooldown * 1000) {
        return false;
      }
    }

    return true;
  }

  /**
   * 获取错误统计
   */
  getErrorStats(): {
    total: number;
    byLevel: Record<string, number>;
    byFingerprint: Array<{ fingerprint: string; count: number; lastSeen: Date }>;
    topErrors: Array<ErrorSummary>;
  } {
    const stats = {
      total: 0,
      byLevel: { warning: 0, error: 0, critical: 0 },
      byFingerprint: [] as Array<{ fingerprint: string; count: number; lastSeen: Date }>,
      topErrors: [] as Array<ErrorSummary>
    };

    this.errorSummaries.forEach(summary => {
      stats.total += summary.count;
      stats.byLevel[summary.error.level] += summary.count;
      stats.byFingerprint.push({
        fingerprint: summary.fingerprint,
        count: summary.count,
        lastSeen: summary.lastSeen
      });
    });

    // 按错误频率排序
    stats.topErrors = Array.from(this.errorSummaries.values())
      .sort((a, b) => b.count - a.count)
      .slice(0, 10);

    stats.byFingerprint.sort((a, b) => b.count - a.count);

    return stats;
  }

  /**
   * 创建Express错误处理中间件
   */
  createExpressMiddleware() {
    return (error: Error, req: Request, res: Response, _next: NextFunction) => {
      const context = {
        requestId: (req as any).requestId,
        userId: (req as any).user?.id,
        url: req.url,
        method: req.method,
        userAgent: req.get('User-Agent'),
        ip: req.ip,
        body: req.body,
        params: req.params,
        query: req.query
      };

      const errorInfo = this.handleError(error, context);

      // 设置响应状态码
      const statusCode = (error as any).statusCode || 500;
      res.status(statusCode);

      // 发送错误响应
      res.json({
        error: {
          id: errorInfo.id,
          message: process.env.NODE_ENV === 'production' 
            ? 'Internal Server Error' 
            : error.message,
          ...(process.env.NODE_ENV !== 'production' && {
            stack: error.stack,
            details: errorInfo
          })
        }
      });
    };
  }
}

/**
 * 调试器
 */
export class Debugger {
  private config: DebugLogConfig;
  private logger: CoreLogger;
  private debugInfo: DebugInfo[] = [];

  constructor(config: DebugLogConfig, logger: CoreLogger) {
    this.config = config;
    this.logger = logger;
  }

  /**
   * 记录调试信息
   */
  debug(namespace: string, message: string, data?: any): void {
    if (!this.config.enabled) return;

    // 检查命名空间
    if (!this.isNamespaceEnabled(namespace)) return;

    const debugInfo: DebugInfo = {
      timestamp: new Date(),
      namespace,
      level: 'debug',
      message,
      data: this.sanitizeDebugData(data)
    };

    // 获取调用栈信息
    if (this.config.includeFileInfo) {
      const stackInfo = this.getStackInfo();
      debugInfo.file = stackInfo.file;
      debugInfo.line = stackInfo.line;
      debugInfo.function = stackInfo.function;
    }

    // 记录内存使用
    debugInfo.memoryUsage = process.memoryUsage();

    // 保存调试信息
    this.debugInfo.push(debugInfo);

    // 限制调试信息数量
    if (this.debugInfo.length > 1000) {
      this.debugInfo = this.debugInfo.slice(-1000);
    }

    // 记录到日志
    this.logger.debug(`[${namespace}] ${message}`, {
      module: namespace,
      extra: debugInfo
    });
  }

  /**
   * 性能计时器
   */
  time(namespace: string, label: string): () => void {
    if (!this.config.enabled || !this.isNamespaceEnabled(namespace)) {
      return () => {};
    }

    const startTime = process.hrtime.bigint();
    const startMemory = process.memoryUsage();

    return () => {
      const endTime = process.hrtime.bigint();
      const duration = Number(endTime - startTime) / 1000000; // 转换为毫秒

      this.debug(namespace, `Timer: ${label}`, {
        duration: `${duration.toFixed(2)}ms`,
        memoryDelta: this.calculateMemoryDelta(startMemory, process.memoryUsage())
      });
    };
  }

  /**
   * 检查命名空间是否启用
   */
  private isNamespaceEnabled(namespace: string): boolean {
    return this.config.namespaces.some(pattern => {
      if (pattern.includes('*')) {
        const regex = new RegExp(pattern.replace(/\*/g, '.*'));
        return regex.test(namespace);
      }
      return pattern === namespace;
    });
  }

  /**
   * 清理调试数据
   */
  private sanitizeDebugData(data: any): any {
    if (!data) return data;

    if (typeof data === 'object') {
      const sanitized = Array.isArray(data) ? [] : {};
      
      for (const key in data) {
        if (typeof data[key] === 'string' && this.isSensitiveField(key)) {
          (sanitized as any)[key] = '***DEBUG_SANITIZED***';
        } else if (typeof data[key] === 'object') {
          (sanitized as any)[key] = this.sanitizeDebugData(data[key]);
        } else {
          (sanitized as any)[key] = data[key];
        }
      }
      
      return sanitized;
    }

    return data;
  }

  /**
   * 判断字段是否敏感
   */
  private isSensitiveField(key: string): boolean {
    const sensitiveFields = ['password', 'token', 'secret', 'key', 'auth'];
    return sensitiveFields.some(field => key.toLowerCase().includes(field));
  }

  /**
   * 获取调用栈信息
   */
  private getStackInfo(): { file?: string; line?: number; function?: string } {
    const stack = new Error().stack;
    if (!stack) return {};

    const stackLines = stack.split('\n');
    const callerLine = stackLines[3]; // 跳过Error、getStackInfo、debug调用

    if (!callerLine) return {};

    const match = callerLine.match(/at\s+([^(]+)\s*\(([^:]+):(\d+):\d+\)/);
    if (match) {
      const [, functionName, filePath, lineNumber] = match;
      return {
        function: functionName.trim(),
        file: path.basename(filePath),
        line: parseInt(lineNumber, 10)
      };
    }

    return {};
  }

  /**
   * 计算内存使用差值
   */
  private calculateMemoryDelta(
    start: NodeJS.MemoryUsage, 
    end: NodeJS.MemoryUsage
  ): Record<string, string> {
    return {
      rss: this.formatBytes(end.rss - start.rss),
      heapTotal: this.formatBytes(end.heapTotal - start.heapTotal),
      heapUsed: this.formatBytes(end.heapUsed - start.heapUsed),
      external: this.formatBytes(end.external - start.external)
    };
  }

  /**
   * 格式化字节数
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(Math.abs(bytes)) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 获取调试统计
   */
  getDebugStats(): {
    totalEntries: number;
    entriesByNamespace: Record<string, number>;
    recentEntries: DebugInfo[];
  } {
    const stats = {
      totalEntries: this.debugInfo.length,
      entriesByNamespace: {} as Record<string, number>,
      recentEntries: this.debugInfo.slice(-50) // 最近50条
    };

    this.debugInfo.forEach(info => {
      stats.entriesByNamespace[info.namespace] = 
        (stats.entriesByNamespace[info.namespace] || 0) + 1;
    });

    return stats;
  }
}

// 导出便捷函数
export const errorLogging = {
  /**
   * 创建错误处理器
   */
  createErrorHandler: (config: ErrorLogConfig, logger: CoreLogger) =>
    new ErrorHandler(config, logger),

  /**
   * 创建调试器
   */
  createDebugger: (config: DebugLogConfig, logger: CoreLogger) =>
    new Debugger(config, logger),

  /**
   * 生成错误指纹
   */
  generateFingerprint: ErrorFingerprint.generate
};

// 已在类声明时导出，移除重复export