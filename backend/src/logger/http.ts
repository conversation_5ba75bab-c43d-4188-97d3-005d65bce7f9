/**
 * HTTP请求日志中间件
 * 集成Express中间件，记录HTTP请求和响应的详细信息
 */

import { Request, Response, NextFunction } from 'express';
import { v4 as uuidv4 } from 'uuid';
import { performance } from 'perf_hooks';
import morgan from 'morgan';
import { CoreLogger } from './core';
import { RequestTracker } from './structured';
import { HttpLogConfig } from '../types/logger';

/**
 * 扩展Express请求对象
 */
export interface LoggingRequest extends Request {
  requestId?: string;
  startTime?: number;
  user?: {
    id: string;
    email?: string;
    role?: string;
  };
}

/**
 * HTTP请求日志数据接口
 */
export interface HttpLogData {
  requestId: string;
  method: string;
  url: string;
  originalUrl: string;
  path: string;
  query: Record<string, any>;
  params: Record<string, any>;
  headers: Record<string, string>;
  userAgent?: string;
  ip: string;
  userId?: string;
  sessionId?: string;
  statusCode?: number;
  responseTime?: number;
  responseSize?: number;
  requestSize?: number;
  referrer?: string;
  protocol: string;
  secure: boolean;
  xhr: boolean;
  timestamp: Date;
  error?: {
    message: string;
    stack?: string;
    code?: string | number;
  };
}

/**
 * 安全过滤器 - 移除敏感信息
 */
export class SecurityFilter {
  private static readonly SENSITIVE_HEADERS = [
    'authorization',
    'cookie',
    'set-cookie',
    'x-api-key',
    'x-auth-token'
  ];

  private static readonly SENSITIVE_QUERY_PARAMS = [
    'password',
    'token',
    'api_key',
    'secret',
    'auth'
  ];

  private static readonly SENSITIVE_BODY_FIELDS = [
    'password',
    'confirmPassword',
    'currentPassword',
    'newPassword',
    'token',
    'secret',
    'apiKey',
    'creditCard',
    'ssn'
  ];

  /**
   * 过滤敏感的请求头
   */
  static filterHeaders(headers: Record<string, string>): Record<string, string> {
    const filtered: Record<string, string> = {};
    
    Object.keys(headers).forEach(key => {
      const lowerKey = key.toLowerCase();
      if (this.SENSITIVE_HEADERS.includes(lowerKey)) {
        filtered[key] = '***FILTERED***';
      } else {
        filtered[key] = headers[key];
      }
    });

    return filtered;
  }

  /**
   * 过滤敏感的查询参数
   */
  static filterQuery(query: Record<string, any>): Record<string, any> {
    const filtered: Record<string, any> = {};
    
    Object.keys(query).forEach(key => {
      const lowerKey = key.toLowerCase();
      if (this.SENSITIVE_QUERY_PARAMS.some(param => lowerKey.includes(param))) {
        filtered[key] = '***FILTERED***';
      } else {
        filtered[key] = query[key];
      }
    });

    return filtered;
  }

  /**
   * 过滤敏感的请求体数据
   */
  static filterBody(body: any): any {
    if (!body || typeof body !== 'object') {
      return body;
    }

    if (Array.isArray(body)) {
      return body.map(item => this.filterBody(item));
    }

    const filtered: any = {};
    Object.keys(body).forEach(key => {
      const lowerKey = key.toLowerCase();
      if (this.SENSITIVE_BODY_FIELDS.some(field => lowerKey.includes(field))) {
        filtered[key] = '***FILTERED***';
      } else if (typeof body[key] === 'object') {
        filtered[key] = this.filterBody(body[key]);
      } else {
        filtered[key] = body[key];
      }
    });

    return filtered;
  }
}

/**
 * HTTP日志格式化器
 */
export class HttpLogFormatter {
  private config: HttpLogConfig;

  constructor(config: HttpLogConfig) {
    this.config = config;
  }

  /**
   * 格式化HTTP日志数据
   */
  format(logData: HttpLogData): string {
    switch (this.config.format) {
      case 'combined':
        return this.formatCombined(logData);
      case 'common':
        return this.formatCommon(logData);
      case 'dev':
        return this.formatDev(logData);
      case 'short':
        return this.formatShort(logData);
      case 'tiny':
        return this.formatTiny(logData);
      case 'custom':
        return this.formatCustom(logData);
      default:
        return this.formatCombined(logData);
    }
  }

  /**
   * Combined格式 (Apache combined log format)
   */
  private formatCombined(logData: HttpLogData): string {
    const userId = logData.userId || '-';
    const referrer = logData.referrer || '-';
    const userAgent = logData.userAgent || '-';
    const responseTime = logData.responseTime || 0;

    return `${logData.ip} ${userId} - [${logData.timestamp.toISOString()}] "${logData.method} ${logData.url} HTTP/1.1" ${logData.statusCode} ${logData.responseSize || 0} "${referrer}" "${userAgent}" ${responseTime}ms`;
  }

  /**
   * Common格式 (Apache common log format)
   */
  private formatCommon(logData: HttpLogData): string {
    const userId = logData.userId || '-';
    const responseSize = logData.responseSize || 0;

    return `${logData.ip} - ${userId} [${logData.timestamp.toISOString()}] "${logData.method} ${logData.url} HTTP/1.1" ${logData.statusCode} ${responseSize}`;
  }

  /**
   * 开发格式
   */
  private formatDev(logData: HttpLogData): string {
    const statusColor = this.getStatusColor(logData.statusCode || 0);
    const methodColor = this.getMethodColor(logData.method);
    
    return `${methodColor}${logData.method}\x1b[0m ${logData.url} ${statusColor}${logData.statusCode}\x1b[0m ${logData.responseTime}ms - ${logData.responseSize || 0}`;
  }

  /**
   * 短格式
   */
  private formatShort(logData: HttpLogData): string {
    const userId = logData.userId || '-';
    return `${logData.ip} ${userId} ${logData.method} ${logData.url} HTTP/1.1 ${logData.statusCode} ${logData.responseSize || 0} - ${logData.responseTime}ms`;
  }

  /**
   * 极简格式
   */
  private formatTiny(logData: HttpLogData): string {
    return `${logData.method} ${logData.url} ${logData.statusCode} ${logData.responseSize || 0} - ${logData.responseTime}ms`;
  }

  /**
   * 自定义格式
   */
  private formatCustom(logData: HttpLogData): string {
    if (this.config.customFormat) {
      return this.config.customFormat
        .replace(':method', logData.method)
        .replace(':url', logData.url)
        .replace(':status', String(logData.statusCode || 0))
        .replace(':response-time', String(logData.responseTime || 0))
        .replace(':response-size', String(logData.responseSize || 0))
        .replace(':ip', logData.ip)
        .replace(':user-id', logData.userId || '-')
        .replace(':date', logData.timestamp.toISOString())
        .replace(':user-agent', logData.userAgent || '-')
        .replace(':referrer', logData.referrer || '-');
    }
    return this.formatCombined(logData);
  }

  /**
   * 获取状态码颜色
   */
  private getStatusColor(status: number): string {
    if (status >= 500) return '\x1b[31m'; // 红色
    if (status >= 400) return '\x1b[33m'; // 黄色
    if (status >= 300) return '\x1b[36m'; // 青色
    if (status >= 200) return '\x1b[32m'; // 绿色
    return '\x1b[0m'; // 默认
  }

  /**
   * 获取请求方法颜色
   */
  private getMethodColor(method: string): string {
    switch (method) {
      case 'GET': return '\x1b[32m'; // 绿色
      case 'POST': return '\x1b[33m'; // 黄色
      case 'PUT': return '\x1b[34m'; // 蓝色
      case 'DELETE': return '\x1b[31m'; // 红色
      case 'PATCH': return '\x1b[35m'; // 紫色
      default: return '\x1b[0m'; // 默认
    }
  }
}

/**
 * HTTP日志中间件
 */
export class HttpLogger {
  private config: HttpLogConfig;
  private logger: CoreLogger;
  private requestTracker: RequestTracker;
  private formatter: HttpLogFormatter;

  constructor(config: HttpLogConfig, logger: CoreLogger, requestTracker: RequestTracker) {
    this.config = config;
    this.logger = logger;
    this.requestTracker = requestTracker;
    this.formatter = new HttpLogFormatter(config);
  }

  /**
   * 创建请求ID中间件
   */
  requestId() {
    return (req: LoggingRequest, res: Response, next: NextFunction): void => {
      req.requestId = uuidv4();
      res.setHeader('X-Request-ID', req.requestId);
      next();
    };
  }

  /**
   * 创建请求开始记录中间件
   */
  requestStart() {
    return (req: LoggingRequest, res: Response, next: NextFunction): void => {
      if (!req.requestId) {
        req.requestId = uuidv4();
        res.setHeader('X-Request-ID', req.requestId);
      }

      req.startTime = performance.now();
      
      // 开始请求追踪
      this.requestTracker.startRequest(req.requestId, req.method, req.url);

      next();
    };
  }

  /**
   * 创建请求完成记录中间件
   */
  requestEnd() {
    return (req: LoggingRequest, res: Response, next: NextFunction): void => {
      const originalSend = res.send;
      const originalJson = res.json;
      const originalEnd = res.end;

      // 重写响应方法以捕获响应数据
      res.send = function(body: any) {
        res.locals.responseBody = body;
        return originalSend.call(this, body);
      };

      res.json = function(body: any) {
        res.locals.responseBody = body;
        return originalJson.call(this, body);
      };

      res.end = function(chunk?: any, encoding?: any) {
        const responseTime = req.startTime ? performance.now() - req.startTime : 0;
        
        // 结束请求追踪
        if (req.requestId) {
          httpLogger.requestTracker.endRequest(req.requestId, res.statusCode);
        }

        // 记录HTTP日志
        httpLogger.logRequest(req as LoggingRequest, res, responseTime);
        
        return originalEnd.call(this, chunk, encoding);
      };

      next();
    };
  }

  /**
   * 记录HTTP请求日志
   */
  private logRequest(req: LoggingRequest, res: Response, responseTime: number): void {
    if (this.shouldSkipRequest(req, res)) {
      return;
    }

    const logData: HttpLogData = {
      requestId: req.requestId!,
      method: req.method,
      url: req.url,
      originalUrl: req.originalUrl,
      path: req.path,
      query: SecurityFilter.filterQuery(req.query),
      params: req.params,
      headers: SecurityFilter.filterHeaders(req.headers as Record<string, string>),
      userAgent: req.get('User-Agent'),
      ip: this.getClientIP(req),
      userId: req.user?.id,
      sessionId: (req as any).sessionID,
      statusCode: res.statusCode,
      responseTime: Math.round(responseTime * 100) / 100, // 保留2位小数
      responseSize: this.getResponseSize(res),
      requestSize: this.getRequestSize(req),
      referrer: req.get('Referrer'),
      protocol: req.protocol,
      secure: req.secure,
      xhr: req.xhr,
      timestamp: new Date()
    };

    // 检查是否有错误
    if (res.statusCode >= 400) {
      logData.error = {
        message: res.statusMessage || 'HTTP Error',
        code: res.statusCode
      };
    }

    // 记录日志
    const logMessage = this.formatter.format(logData);
    
    if (res.statusCode >= 500) {
      this.logger.error(`HTTP ${res.statusCode}: ${req.method} ${req.url}`, undefined, {
        requestId: req.requestId,
        duration: responseTime,
        extra: logData
      });
    } else if (res.statusCode >= 400) {
      this.logger.warn(`HTTP ${res.statusCode}: ${req.method} ${req.url}`, {
        requestId: req.requestId,
        duration: responseTime,
        extra: logData
      });
    } else {
      this.logger.http(logMessage, logData);
    }

    // 记录慢请求
    if (responseTime > 3000) { // 3秒
      this.logger.warn(`Slow HTTP request: ${req.method} ${req.url}`, {
        requestId: req.requestId,
        duration: responseTime,
        extra: {
          responseTime,
          url: req.url,
          method: req.method,
          statusCode: res.statusCode
        }
      });
    }
  }

  /**
   * 判断是否应该跳过记录
   */
  private shouldSkipRequest(req: LoggingRequest, res: Response): boolean {
    if (!this.config.enabled) {
      return true;
    }

    if (this.config.skip && this.config.skip(req, res)) {
      return true;
    }

    // 检查过滤器配置
    if (this.config.filters) {
      for (const filter of this.config.filters) {
        const pathMatches = req.path.match(new RegExp(filter.path));
        const methodMatches = !filter.method || req.method === filter.method.toUpperCase();
        
        if (pathMatches && methodMatches && filter.skip) {
          return true;
        }
      }
    }

    return false;
  }

  /**
   * 获取客户端IP地址
   */
  private getClientIP(req: Request): string {
    return (
      req.headers['x-forwarded-for'] as string ||
      req.headers['x-real-ip'] as string ||
      req.connection?.remoteAddress ||
      req.socket?.remoteAddress ||
      'unknown'
    );
  }

  /**
   * 获取响应大小
   */
  private getResponseSize(res: Response): number {
    const contentLength = res.get('Content-Length');
    if (contentLength) {
      return parseInt(contentLength, 10);
    }

    // 尝试从响应体估算大小
    if (res.locals.responseBody) {
      return Buffer.byteLength(JSON.stringify(res.locals.responseBody), 'utf8');
    }

    return 0;
  }

  /**
   * 获取请求大小
   */
  private getRequestSize(req: Request): number {
    const contentLength = req.get('Content-Length');
    if (contentLength) {
      return parseInt(contentLength, 10);
    }

    if (req.body) {
      return Buffer.byteLength(JSON.stringify(req.body), 'utf8');
    }

    return 0;
  }

  /**
   * 创建Morgan中间件（用于兼容性）
   */
  createMorganMiddleware(): any {
    if (!this.config.enabled) {
      return (_req: Request, _res: Response, next: NextFunction) => next();
    }

    return morgan(this.config.format, {
      skip: this.config.skip,
      stream: {
        write: (message: string) => {
          // 移除末尾换行符并记录
          this.logger.http(message.trim(), {});
        }
      }
    });
  }

  /**
   * 获取完整的Express中间件堆栈
   */
  getMiddlewareStack(): any[] {
    return [
      this.requestId(),
      this.requestStart(),
      this.requestEnd()
    ];
  }
}

// 全局HTTP日志器实例
let httpLogger: HttpLogger;

/**
 * 初始化HTTP日志器
 */
export function initializeHttpLogger(
  config: HttpLogConfig,
  logger: CoreLogger,
  requestTracker: RequestTracker
): HttpLogger {
  httpLogger = new HttpLogger(config, logger, requestTracker);
  return httpLogger;
}

/**
 * 获取HTTP日志器实例
 */
export function getHttpLogger(): HttpLogger {
  if (!httpLogger) {
    throw new Error('HTTP logger not initialized. Call initializeHttpLogger() first.');
  }
  return httpLogger;
}

// 导出便捷函数
export const httpLogging = {
  /**
   * 初始化HTTP日志器
   */
  init: initializeHttpLogger,

  /**
   * 获取HTTP日志器
   */
  get: getHttpLogger,

  /**
   * 创建安全过滤器
   */
  createSecurityFilter: () => SecurityFilter,

  /**
   * 创建日志格式化器
   */
  createFormatter: (config: HttpLogConfig) => new HttpLogFormatter(config)
};

// 已在类声明时导出，移除重复export