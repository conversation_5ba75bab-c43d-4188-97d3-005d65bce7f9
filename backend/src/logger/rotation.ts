/**
 * 日志轮转和文件管理模块
 * 处理日志文件轮转、压缩、清理和磁盘管理
 */

import fs from 'fs';
import path from 'path';
import { promisify } from 'util';
import { createGzip } from 'zlib';
import { createReadStream, createWriteStream } from 'fs';
import { pipeline } from 'stream';
import { LoggingConfig } from '../types/config';

const stat = promisify(fs.stat);
const readdir = promisify(fs.readdir);
const unlink = promisify(fs.unlink);
const pipelineAsync = promisify(pipeline);

/**
 * 文件信息接口
 */
export interface FileInfo {
  name: string;
  path: string;
  size: number;
  mtime: Date;
  isCompressed: boolean;
}

/**
 * 轮转统计接口
 */
export interface RotationStats {
  filesRotated: number;
  filesCompressed: number;
  filesRemoved: number;
  spaceFreed: number;
  totalSize: number;
  oldestFile: Date | null;
  newestFile: Date | null;
}

/**
 * 磁盘空间信息接口
 */
export interface DiskSpaceInfo {
  total: number;
  free: number;
  used: number;
  usedPercentage: number;
}

/**
 * 日志文件管理器
 */
export class LogFileManager {
  private logDirectory: string;

  constructor(config: LoggingConfig) {
    this.logDirectory = path.dirname(config.fileRotation.filename);
    this.ensureLogDirectory();
  }

  /**
   * 确保日志目录存在
   */
  private ensureLogDirectory(): void {
    if (!fs.existsSync(this.logDirectory)) {
      fs.mkdirSync(this.logDirectory, { recursive: true, mode: 0o755 });
    }
  }

  /**
   * 获取日志目录中的所有文件信息
   */
  async getLogFiles(pattern?: RegExp): Promise<FileInfo[]> {
    try {
      const files = await readdir(this.logDirectory);
      const fileInfos: FileInfo[] = [];

      for (const filename of files) {
        if (pattern && !pattern.test(filename)) {
          continue;
        }

        const filePath = path.join(this.logDirectory, filename);
        try {
          const stats = await stat(filePath);
          
          if (stats.isFile()) {
            fileInfos.push({
              name: filename,
              path: filePath,
              size: stats.size,
              mtime: stats.mtime,
              isCompressed: filename.endsWith('.gz')
            });
          }
        } catch (error) {
          console.warn(`Failed to get stats for ${filePath}:`, error);
        }
      }

      // 按修改时间排序（最新的在前）
      return fileInfos.sort((a, b) => b.mtime.getTime() - a.mtime.getTime());
    } catch (error) {
      console.error('Failed to read log directory:', error);
      return [];
    }
  }

  /**
   * 压缩日志文件
   */
  async compressFile(filePath: string): Promise<string> {
    const compressedPath = `${filePath}.gz`;
    
    try {
      const readStream = createReadStream(filePath);
      const writeStream = createWriteStream(compressedPath);
      const gzipStream = createGzip({
        level: 6, // 平衡压缩比和速度
        windowBits: 15,
        memLevel: 8
      });

      await pipelineAsync(readStream, gzipStream, writeStream);
      
      // 删除原文件
      await unlink(filePath);
      
      console.log(`Compressed log file: ${filePath} -> ${compressedPath}`);
      return compressedPath;
    } catch (error) {
      console.error(`Failed to compress ${filePath}:`, error);
      // 如果压缩失败且压缩文件存在，删除它
      if (fs.existsSync(compressedPath)) {
        try {
          await unlink(compressedPath);
        } catch (cleanupError) {
          console.error(`Failed to cleanup ${compressedPath}:`, cleanupError);
        }
      }
      throw error;
    }
  }

  /**
   * 删除过期的日志文件
   */
  async removeExpiredFiles(maxAge: number, maxFiles?: number): Promise<FileInfo[]> {
    const files = await this.getLogFiles(/\.(log|gz)$/);
    const cutoffDate = new Date(Date.now() - maxAge * 24 * 60 * 60 * 1000);
    const removedFiles: FileInfo[] = [];

    // 按时间过期删除
    for (const file of files) {
      if (file.mtime < cutoffDate) {
        try {
          await unlink(file.path);
          removedFiles.push(file);
          console.log(`Removed expired log file: ${file.path}`);
        } catch (error) {
          console.error(`Failed to remove ${file.path}:`, error);
        }
      }
    }

    // 按数量限制删除（保留最新的文件）
    if (maxFiles && files.length > maxFiles) {
      const filesToRemove = files.slice(maxFiles);
      for (const file of filesToRemove) {
        if (!removedFiles.includes(file)) {
          try {
            await unlink(file.path);
            removedFiles.push(file);
            console.log(`Removed excess log file: ${file.path}`);
          } catch (error) {
            console.error(`Failed to remove ${file.path}:`, error);
          }
        }
      }
    }

    return removedFiles;
  }

  /**
   * 获取磁盘空间信息
   */
  async getDiskSpaceInfo(): Promise<DiskSpaceInfo> {
    try {
      await stat(this.logDirectory); // 验证目录存在
      // Note: 这是一个简化的实现，实际生产环境中应该使用 statvfs 或类似的系统调用
      // 这里我们返回一个估算值
      const total = 100 * 1024 * 1024 * 1024; // 假设100GB磁盘
      const files = await this.getLogFiles();
      const used = files.reduce((sum, file) => sum + file.size, 0);
      const free = total - used;
      
      return {
        total,
        free,
        used,
        usedPercentage: (used / total) * 100
      };
    } catch (error) {
      console.error('Failed to get disk space info:', error);
      return {
        total: 0,
        free: 0,
        used: 0,
        usedPercentage: 0
      };
    }
  }

  /**
   * 清理日志目录以释放空间
   */
  async cleanupSpace(targetFreeSpacePercent: number = 20): Promise<RotationStats> {
    const stats: RotationStats = {
      filesRotated: 0,
      filesCompressed: 0,
      filesRemoved: 0,
      spaceFreed: 0,
      totalSize: 0,
      oldestFile: null,
      newestFile: null
    };

    try {
      const diskInfo = await this.getDiskSpaceInfo();
      const files = await this.getLogFiles(/\.(log|gz)$/);

      if (files.length === 0) {
        return stats;
      }

      // 更新统计信息
      stats.totalSize = files.reduce((sum, file) => sum + file.size, 0);
      stats.oldestFile = files[files.length - 1]?.mtime || null;
      stats.newestFile = files[0]?.mtime || null;

      // 如果磁盘空间足够，仅进行常规清理
      if (diskInfo.usedPercentage < (100 - targetFreeSpacePercent)) {
        const removedFiles = await this.removeExpiredFiles(30); // 30天过期
        stats.filesRemoved = removedFiles.length;
        stats.spaceFreed = removedFiles.reduce((sum, file) => sum + file.size, 0);
        return stats;
      }

      // 磁盘空间不足，进行更积极的清理
      console.log(`Disk usage ${diskInfo.usedPercentage.toFixed(1)}% exceeds threshold, starting aggressive cleanup...`);

      // 1. 压缩未压缩的文件（除了当前文件）
      const uncompressedFiles = files.filter(f => !f.isCompressed && !f.name.includes('current'));
      for (const file of uncompressedFiles.slice(1)) { // 保留最新的未压缩文件
        try {
          await this.compressFile(file.path);
          stats.filesCompressed++;
        } catch (error) {
          console.warn(`Failed to compress ${file.path}:`, error);
        }
      }

      // 2. 删除更多过期文件（缩短保留期）
      const removedFiles = await this.removeExpiredFiles(7, 10); // 7天或最多10个文件
      stats.filesRemoved = removedFiles.length;
      stats.spaceFreed = removedFiles.reduce((sum, file) => sum + file.size, 0);

      console.log(`Cleanup completed: compressed ${stats.filesCompressed} files, removed ${stats.filesRemoved} files, freed ${this.formatBytes(stats.spaceFreed)}`);
    } catch (error) {
      console.error('Failed to cleanup log space:', error);
    }

    return stats;
  }

  /**
   * 格式化字节数为可读格式
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 获取日志文件统计信息
   */
  async getStats(): Promise<RotationStats> {
    const files = await this.getLogFiles(/\.(log|gz)$/);
    
    return {
      filesRotated: 0,
      filesCompressed: files.filter(f => f.isCompressed).length,
      filesRemoved: 0,
      spaceFreed: 0,
      totalSize: files.reduce((sum, file) => sum + file.size, 0),
      oldestFile: files.length > 0 ? files[files.length - 1].mtime : null,
      newestFile: files.length > 0 ? files[0].mtime : null
    };
  }
}

/**
 * 日志轮转调度器
 */
export class RotationScheduler {
  private manager: LogFileManager;
  private intervalId: NodeJS.Timeout | null = null;
  private isRunning: boolean = false;

  constructor(config: LoggingConfig) {
    this.manager = new LogFileManager(config);
  }

  /**
   * 启动轮转调度
   */
  start(interval: number = 60 * 60 * 1000): void { // 默认每小时检查一次
    if (this.isRunning) {
      console.warn('Rotation scheduler is already running');
      return;
    }

    this.isRunning = true;
    
    // 立即执行一次检查
    this.runRotation().catch(error => {
      console.error('Initial rotation check failed:', error);
    });

    // 设置定期检查
    this.intervalId = setInterval(() => {
      this.runRotation().catch(error => {
        console.error('Scheduled rotation check failed:', error);
      });
    }, interval);

    console.log(`Log rotation scheduler started with ${interval}ms interval`);
  }

  /**
   * 停止轮转调度
   */
  stop(): void {
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
    
    this.isRunning = false;
    console.log('Log rotation scheduler stopped');
  }

  /**
   * 执行轮转检查
   */
  private async runRotation(): Promise<void> {
    try {
      console.log('Running log rotation check...');
      
      const stats = await this.manager.cleanupSpace(20); // 保持20%空闲空间
      
      if (stats.filesCompressed > 0 || stats.filesRemoved > 0) {
        console.log('Log rotation summary:', {
          filesCompressed: stats.filesCompressed,
          filesRemoved: stats.filesRemoved,
          spaceFreed: this.formatBytes(stats.spaceFreed),
          totalSize: this.formatBytes(stats.totalSize)
        });
      }
    } catch (error) {
      console.error('Log rotation failed:', error);
    }
  }

  /**
   * 格式化字节数
   */
  private formatBytes(bytes: number): string {
    if (bytes === 0) return '0 B';
    
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 手动触发轮转
   */
  async triggerRotation(): Promise<RotationStats> {
    console.log('Manually triggering log rotation...');
    return await this.manager.cleanupSpace(20);
  }

  /**
   * 获取调度器状态
   */
  getStatus(): { isRunning: boolean; interval: number | null } {
    return {
      isRunning: this.isRunning,
      interval: this.intervalId ? 60 * 60 * 1000 : null
    };
  }
}

/**
 * 日志目录监控器
 */
export class LogDirectoryWatcher {
  private config: LoggingConfig;
  private watcher: fs.FSWatcher | null = null;

  constructor(config: LoggingConfig) {
    this.config = config;
    // 初始化时不需要立即创建管理器
  }

  /**
   * 开始监控日志目录
   */
  start(): void {
    const logDirectory = path.dirname(this.config.fileRotation.filename);

    try {
      this.watcher = fs.watch(logDirectory, { recursive: false }, (eventType, filename) => {
        if (!filename) return;

        console.log(`Log directory event: ${eventType} - ${filename}`);
        
        if (eventType === 'rename' && filename.endsWith('.log')) {
          // 文件轮转事件
          this.handleRotationEvent(filename);
        }
      });

      console.log(`Started monitoring log directory: ${logDirectory}`);
    } catch (error) {
      console.error('Failed to start log directory watcher:', error);
    }
  }

  /**
   * 停止监控
   */
  stop(): void {
    if (this.watcher) {
      this.watcher.close();
      this.watcher = null;
      console.log('Stopped log directory monitoring');
    }
  }

  /**
   * 处理轮转事件
   */
  private handleRotationEvent(filename: string): void {
    console.log(`Log file rotation detected: ${filename}`);
    
    // 这里可以添加轮转后的处理逻辑
    // 例如：压缩文件、发送通知等
  }
}

// 导出便捷函数
export const logRotation = {
  /**
   * 创建文件管理器
   */
  createManager: (config: LoggingConfig) => new LogFileManager(config),

  /**
   * 创建轮转调度器
   */
  createScheduler: (config: LoggingConfig) => new RotationScheduler(config),

  /**
   * 创建目录监控器
   */
  createWatcher: (config: LoggingConfig) => new LogDirectoryWatcher(config)
};

// 已在类声明时导出，移除重复export