{"name": "petcare-blog-backend", "version": "1.0.0", "description": "Pet blog multilingual site backend API service", "main": "dist/index.js", "scripts": {"dev": "nodemon --exec ts-node src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "NODE_ENV=test jest", "test:watch": "NODE_ENV=test jest --watch", "test:coverage": "NODE_ENV=test jest --coverage", "test:unit": "NODE_ENV=test jest --testPathPattern=src/.*\\.test\\.ts", "test:integration": "NODE_ENV=test jest --testPathPattern=tests/.*\\.test\\.ts", "test:verbose": "NODE_ENV=test jest --verbose", "test:silent": "NODE_ENV=test jest --silent", "test:debug": "NODE_ENV=test node --inspect-brk node_modules/.bin/jest --runInBand", "lint": "eslint \"src/**/*.ts\" \"tests/**/*.ts\"", "lint:fix": "eslint \"src/**/*.ts\" \"tests/**/*.ts\" --fix", "format": "prettier --write \"src/**/*.ts\" \"tests/**/*.ts\"", "format:check": "prettier --check \"src/**/*.ts\" \"tests/**/*.ts\"", "typecheck": "tsc --noEmit", "typecheck:watch": "tsc --noEmit --watch", "db:migrate": "ts-node src/database/migrate.ts", "db:rollback": "ts-node src/database/rollback.ts", "db:seed": "ts-node src/database/seed.ts", "db:test:setup": "NODE_ENV=test npm run db:migrate && NODE_ENV=test npm run db:seed", "db:test:teardown": "NODE_ENV=test npm run db:rollback", "clean": "rm -rf dist coverage", "validate": "npm run typecheck && npm run lint && npm run test:coverage", "prepare": "npm run build"}, "keywords": ["pet", "blog", "multilingual", "api", "express", "typescript"], "author": "", "license": "ISC", "engines": {"node": ">=20.0.0", "npm": ">=10.0.0"}, "dependencies": {"@types/swagger-jsdoc": "^6.0.4", "@types/swagger-ui-express": "^4.1.8", "bcryptjs": "^3.0.2", "compression": "^1.8.1", "cors": "^2.8.5", "dotenv": "^17.2.1", "express": "^5.1.0", "express-rate-limit": "^8.0.1", "express-validator": "^7.2.1", "helmet": "^8.1.0", "jsonwebtoken": "^9.0.2", "morgan": "^1.10.1", "mysql2": "^3.14.3", "reflect-metadata": "^0.2.2", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "typeorm": "^0.3.25", "uuid": "^11.1.0", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/compression": "^1.8.1", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jest": "^30.0.0", "@types/jsonwebtoken": "^9.0.10", "@types/morgan": "^1.9.10", "@types/node": "^24.2.0", "@types/supertest": "^6.0.2", "@types/uuid": "^10.0.0", "@types/winston": "^2.4.4", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "eslint": "^9.32.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-jest": "^28.3.0", "jest": "^30.0.5", "jest-environment-node": "^30.0.5", "jest-extended": "^4.0.2", "nodemon": "^3.1.10", "prettier": "^3.6.2", "supertest": "^7.0.0", "ts-jest": "^29.4.1", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}