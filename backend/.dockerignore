# 后端Docker忽略文件
# 优化后端服务构建性能

# ===========================================
# 版本控制
# ===========================================
.git
.gitignore

# ===========================================
# 环境配置
# ===========================================
.env
.env.*
*.env

# ===========================================
# 依赖和缓存
# ===========================================
node_modules
npm-debug.log*
yarn-debug.log*
.npm
.yarn

# ===========================================
# 构建和测试输出
# ===========================================
coverage
.nyc_output
dist
build

# ===========================================
# 开发工具
# ===========================================
.vscode
.idea
nodemon.json

# ===========================================
# 测试文件
# ===========================================
tests
test
*.test.ts
*.test.js
*.spec.ts
*.spec.js
jest.config.js

# ===========================================
# 日志和临时文件
# ===========================================
logs
*.log
*.tmp
uploads
temp

# ===========================================
# Docker相关
# ===========================================
Dockerfile*
.dockerignore

# ===========================================
# 其他配置
# ===========================================
README.md
docs