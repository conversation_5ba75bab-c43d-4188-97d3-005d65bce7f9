# 宠物博客后端API服务 Dockerfile
# 基于 Node.js 20 LTS Alpine 镜像
FROM node:20-alpine

# 设置工作目录
WORKDIR /app

# 安装系统依赖
RUN apk add --no-cache \
    dumb-init \
    && addgroup -g 1001 -S nodejs \
    && adduser -S app -u 1001

# 复制package文件并安装依赖
COPY package*.json ./
RUN npm ci --only=production && npm cache clean --force

# 复制源代码
COPY . .

# 构建TypeScript代码
RUN npm run build

# 创建日志目录
RUN mkdir -p logs && chown -R app:nodejs logs

# 切换到非root用户
USER app

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD node healthcheck.js || exit 1

# 使用dumb-init作为初始化进程，启动应用
ENTRYPOINT ["dumb-init", "--"]
CMD ["node", "dist/index.js"]