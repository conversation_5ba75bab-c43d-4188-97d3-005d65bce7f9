# Server Configuration
NODE_ENV=development
PORT=3000

# Database Configuration
DB_HOST=************
DB_PORT=3306
DB_NAME=bengtai
DB_USER=bengtai
DB_PASSWORD=your_password_here
DB_CHARSET=utf8mb4
DB_COLLATION=utf8mb4_unicode_ci

# Redis Configuration
REDIS_HOST=localhost
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=0

# JWT Configuration
JWT_SECRET=your_jwt_secret_key_here
JWT_EXPIRES_IN=7d
JWT_REFRESH_SECRET=your_jwt_refresh_secret_here
JWT_REFRESH_EXPIRES_IN=30d

# Gemini AI API Configuration
GEMINI_API_ENDPOINT=https://ai.wanderintree.top
GEMINI_API_KEY=your_gemini_api_key_here
GEMINI_MODEL=gemini-2.5-pro
GEMINI_MAX_TOKENS=4000
GEMINI_TEMPERATURE=0.7

# CORS Configuration
CORS_ORIGIN=http://localhost:4321,http://localhost:4322,http://localhost:4323,http://localhost:4324

# Rate Limiting
RATE_LIMIT_WINDOW_MS=60000
RATE_LIMIT_MAX_REQUESTS=100

# File Upload
MAX_FILE_SIZE=10485760
ALLOWED_FILE_TYPES=image/jpeg,image/png,image/webp,image/gif

# Email Configuration (for notifications)
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your_email_password

# Logging
LOG_LEVEL=debug
LOG_DIR=logs

# API Documentation
SWAGGER_ENABLED=true
SWAGGER_PATH=/api-docs

# Swagger文档访问控制配置
# 生产环境下访问API文档的用户名和密码
SWAGGER_DOCS_USERNAME=admin
SWAGGER_DOCS_PASSWORD=your_secure_password_here

# IP白名单配置（可选）
# 允许访问API文档的IP地址列表，用逗号分隔
# SWAGGER_ALLOWED_IPS=127.0.0.1,*************,********

# 时间访问控制（可选）
# 允许访问API文档的时间窗口（24小时制）
# SWAGGER_ACCESS_HOURS=9-18

# Security
BCRYPT_ROUNDS=10
SESSION_SECRET=your_session_secret_here

# CDN Configuration (optional)
CDN_URL=
CDN_ENABLED=false

# Analytics (optional)
GOOGLE_ANALYTICS_ID=
FACEBOOK_PIXEL_ID=

# Admin User (for initial setup)
ADMIN_EMAIL=<EMAIL>
ADMIN_PASSWORD=change_this_password