-- =============================================
-- 宠物博客站群系统 - 核心表结构创建脚本
-- 版本: 1.0.0
-- 创建日期: 2025-01-06
-- 描述: 创建所有核心数据表
-- =============================================

USE bengtai;

-- 删除已存在的表（开发环境使用，生产环境请谨慎）
SET FOREIGN_KEY_CHECKS = 0;
DROP TABLE IF EXISTS audit_logs;
DROP TABLE IF EXISTS media_files;
DROP TABLE IF EXISTS translation_history;
DROP TABLE IF EXISTS ad_configs;
DROP TABLE IF EXISTS site_configs;
DROP TABLE IF EXISTS sites;
DROP TABLE IF EXISTS comments;
DROP TABLE IF EXISTS article_tags;
DROP TABLE IF EXISTS tags;
DROP TABLE IF EXISTS article_translations;
DROP TABLE IF EXISTS articles;
DROP TABLE IF EXISTS category_translations;
DROP TABLE IF EXISTS categories;
DROP TABLE IF EXISTS users;
SET FOREIGN_KEY_CHECKS = 1;

-- =============================================
-- 1. 用户表 (users)
-- =============================================
CREATE TABLE `users` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `username` VARCHAR(50) NOT NULL,
  `email` VARCHAR(100) NOT NULL,
  `password` VARCHAR(255) NOT NULL COMMENT 'bcrypt加密',
  `role` ENUM('super_admin', 'admin', 'editor') NOT NULL DEFAULT 'editor',
  `avatar` VARCHAR(255) DEFAULT NULL,
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `last_login_at` TIMESTAMP NULL DEFAULT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_users_username` (`username`),
  UNIQUE KEY `idx_users_email` (`email`),
  KEY `idx_users_role` (`role`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- =============================================
-- 2. 分类表 (categories)
-- =============================================
CREATE TABLE `categories` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `parent_id` INT UNSIGNED DEFAULT NULL,
  `slug` VARCHAR(100) NOT NULL,
  `icon` VARCHAR(100) DEFAULT NULL,
  `sort_order` INT NOT NULL DEFAULT 0,
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_categories_slug` (`slug`),
  KEY `idx_categories_parent` (`parent_id`),
  KEY `idx_categories_sort` (`sort_order`),
  CONSTRAINT `fk_categories_parent` FOREIGN KEY (`parent_id`)
    REFERENCES `categories` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类表';

-- =============================================
-- 3. 分类翻译表 (category_translations)
-- =============================================
CREATE TABLE `category_translations` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `category_id` INT UNSIGNED NOT NULL,
  `language_code` VARCHAR(5) NOT NULL,
  `name` VARCHAR(100) NOT NULL,
  `description` TEXT,
  `meta_title` VARCHAR(200),
  `meta_description` VARCHAR(300),
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_category_trans_unique` (`category_id`, `language_code`),
  KEY `idx_category_trans_lang` (`language_code`),
  CONSTRAINT `fk_category_trans_category` FOREIGN KEY (`category_id`)
    REFERENCES `categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='分类翻译表';

-- =============================================
-- 4. 文章表 (articles)
-- =============================================
CREATE TABLE `articles` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `author_id` INT UNSIGNED NOT NULL,
  `category_id` INT UNSIGNED NOT NULL,
  `featured_image` VARCHAR(255) DEFAULT NULL,
  `status` ENUM('draft', 'published', 'archived') NOT NULL DEFAULT 'draft',
  `view_count` INT UNSIGNED NOT NULL DEFAULT 0,
  `comment_count` INT UNSIGNED NOT NULL DEFAULT 0,
  `is_featured` BOOLEAN NOT NULL DEFAULT FALSE,
  `published_at` TIMESTAMP NULL DEFAULT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_articles_author` (`author_id`),
  KEY `idx_articles_category` (`category_id`),
  KEY `idx_articles_status` (`status`),
  KEY `idx_articles_published` (`published_at`),
  KEY `idx_articles_featured` (`is_featured`, `published_at`),
  CONSTRAINT `fk_articles_author` FOREIGN KEY (`author_id`)
    REFERENCES `users` (`id`) ON DELETE RESTRICT,
  CONSTRAINT `fk_articles_category` FOREIGN KEY (`category_id`)
    REFERENCES `categories` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章表';

-- =============================================
-- 5. 文章翻译表 (article_translations)
-- =============================================
CREATE TABLE `article_translations` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `article_id` INT UNSIGNED NOT NULL,
  `language_code` VARCHAR(5) NOT NULL DEFAULT 'zh-CN',
  `title` VARCHAR(200) NOT NULL,
  `slug` VARCHAR(200) NOT NULL,
  `content` LONGTEXT NOT NULL,
  `summary` TEXT,
  `meta_title` VARCHAR(200),
  `meta_description` VARCHAR(300),
  `meta_keywords` VARCHAR(200),
  `translation_status` ENUM('original', 'ai_translated', 'human_reviewed', 'published')
    NOT NULL DEFAULT 'original',
  `ai_translation_id` VARCHAR(100) DEFAULT NULL COMMENT 'AI翻译任务ID',
  `translator_id` INT UNSIGNED DEFAULT NULL COMMENT '人工审核者ID',
  `translated_at` TIMESTAMP NULL DEFAULT NULL,
  `reviewed_at` TIMESTAMP NULL DEFAULT NULL,
  `published_at` TIMESTAMP NULL DEFAULT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_article_trans_unique` (`article_id`, `language_code`),
  UNIQUE KEY `idx_article_trans_slug` (`language_code`, `slug`),
  KEY `idx_article_trans_status` (`translation_status`),
  KEY `idx_article_trans_published` (`language_code`, `published_at`),
  FULLTEXT KEY `idx_article_trans_fulltext` (`title`, `content`),
  CONSTRAINT `fk_article_trans_article` FOREIGN KEY (`article_id`)
    REFERENCES `articles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_article_trans_translator` FOREIGN KEY (`translator_id`)
    REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章翻译表';

-- =============================================
-- 6. 标签表 (tags)
-- =============================================
CREATE TABLE `tags` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `slug` VARCHAR(50) NOT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_tags_slug` (`slug`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='标签表';

-- =============================================
-- 7. 文章标签关联表 (article_tags)
-- =============================================
CREATE TABLE `article_tags` (
  `article_id` INT UNSIGNED NOT NULL,
  `tag_id` INT UNSIGNED NOT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`article_id`, `tag_id`),
  KEY `idx_article_tags_tag` (`tag_id`),
  CONSTRAINT `fk_article_tags_article` FOREIGN KEY (`article_id`)
    REFERENCES `articles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_article_tags_tag` FOREIGN KEY (`tag_id`)
    REFERENCES `tags` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='文章标签关联表';

-- =============================================
-- 8. 评论表 (comments)
-- =============================================
CREATE TABLE `comments` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `article_id` INT UNSIGNED NOT NULL,
  `parent_id` INT UNSIGNED DEFAULT NULL,
  `language_code` VARCHAR(5) NOT NULL,
  `author_name` VARCHAR(50) NOT NULL,
  `author_email` VARCHAR(100) NOT NULL,
  `author_url` VARCHAR(200) DEFAULT NULL,
  `content` TEXT NOT NULL,
  `status` ENUM('pending', 'approved', 'spam', 'trash') NOT NULL DEFAULT 'pending',
  `ip_address` VARCHAR(45) NOT NULL,
  `user_agent` VARCHAR(255),
  `approved_by` INT UNSIGNED DEFAULT NULL,
  `approved_at` TIMESTAMP NULL DEFAULT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_comments_article` (`article_id`, `status`),
  KEY `idx_comments_parent` (`parent_id`),
  KEY `idx_comments_status` (`status`, `created_at`),
  KEY `idx_comments_email` (`author_email`),
  CONSTRAINT `fk_comments_article` FOREIGN KEY (`article_id`)
    REFERENCES `articles` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_comments_parent` FOREIGN KEY (`parent_id`)
    REFERENCES `comments` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_comments_approver` FOREIGN KEY (`approved_by`)
    REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='评论表';

-- =============================================
-- 9. 站点配置表 (sites)
-- =============================================
CREATE TABLE `sites` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `domain` VARCHAR(100) NOT NULL,
  `language_code` VARCHAR(5) NOT NULL,
  `title` VARCHAR(200) NOT NULL,
  `description` TEXT,
  `logo` VARCHAR(255) DEFAULT NULL,
  `favicon` VARCHAR(255) DEFAULT NULL,
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `robots_txt` TEXT,
  `sitemap_url` VARCHAR(255),
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_sites_domain` (`domain`),
  KEY `idx_sites_language` (`language_code`),
  KEY `idx_sites_active` (`is_active`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='站点配置表';

-- =============================================
-- 10. 站点配置项表 (site_configs)
-- =============================================
CREATE TABLE `site_configs` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `site_id` INT UNSIGNED NOT NULL,
  `config_key` VARCHAR(100) NOT NULL,
  `config_value` TEXT,
  `config_type` ENUM('string', 'number', 'boolean', 'json') NOT NULL DEFAULT 'string',
  `description` VARCHAR(255),
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_site_configs_unique` (`site_id`, `config_key`),
  KEY `idx_site_configs_key` (`config_key`),
  CONSTRAINT `fk_site_configs_site` FOREIGN KEY (`site_id`)
    REFERENCES `sites` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='站点配置项表';

-- =============================================
-- 11. 广告配置表 (ad_configs)
-- =============================================
CREATE TABLE `ad_configs` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `site_id` INT UNSIGNED NOT NULL,
  `position` VARCHAR(50) NOT NULL COMMENT 'header, sidebar, footer, in_article',
  `ad_type` ENUM('adsense', 'custom') NOT NULL DEFAULT 'adsense',
  `ad_code` TEXT NOT NULL,
  `is_active` BOOLEAN NOT NULL DEFAULT TRUE,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  `updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  UNIQUE KEY `idx_ad_configs_unique` (`site_id`, `position`),
  KEY `idx_ad_configs_active` (`is_active`),
  CONSTRAINT `fk_ad_configs_site` FOREIGN KEY (`site_id`)
    REFERENCES `sites` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='广告配置表';

-- =============================================
-- 12. 翻译历史表 (translation_history)
-- =============================================
CREATE TABLE `translation_history` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `article_translation_id` INT UNSIGNED NOT NULL,
  `translator_id` INT UNSIGNED,
  `original_content` LONGTEXT,
  `translated_content` LONGTEXT,
  `action` ENUM('ai_translate', 'human_edit', 'publish') NOT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_trans_history_article` (`article_translation_id`),
  KEY `idx_trans_history_translator` (`translator_id`),
  KEY `idx_trans_history_action` (`action`, `created_at`),
  CONSTRAINT `fk_trans_history_article` FOREIGN KEY (`article_translation_id`)
    REFERENCES `article_translations` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_trans_history_translator` FOREIGN KEY (`translator_id`)
    REFERENCES `users` (`id`) ON DELETE SET NULL
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='翻译历史表';

-- =============================================
-- 13. 媒体文件表 (media_files)
-- =============================================
CREATE TABLE `media_files` (
  `id` INT UNSIGNED NOT NULL AUTO_INCREMENT,
  `file_name` VARCHAR(255) NOT NULL,
  `file_path` VARCHAR(500) NOT NULL,
  `file_type` VARCHAR(50) NOT NULL,
  `file_size` INT UNSIGNED NOT NULL COMMENT '字节',
  `mime_type` VARCHAR(100) NOT NULL,
  `width` INT UNSIGNED DEFAULT NULL COMMENT '图片宽度',
  `height` INT UNSIGNED DEFAULT NULL COMMENT '图片高度',
  `alt_text` VARCHAR(255) DEFAULT NULL,
  `uploaded_by` INT UNSIGNED NOT NULL,
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_media_files_type` (`file_type`),
  KEY `idx_media_files_uploader` (`uploaded_by`),
  KEY `idx_media_files_created` (`created_at`),
  CONSTRAINT `fk_media_files_uploader` FOREIGN KEY (`uploaded_by`)
    REFERENCES `users` (`id`) ON DELETE RESTRICT
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='媒体文件表';

-- =============================================
-- 14. 操作日志表 (audit_logs)
-- =============================================
CREATE TABLE `audit_logs` (
  `id` BIGINT UNSIGNED NOT NULL AUTO_INCREMENT,
  `user_id` INT UNSIGNED DEFAULT NULL,
  `action` VARCHAR(50) NOT NULL,
  `resource_type` VARCHAR(50) NOT NULL,
  `resource_id` INT UNSIGNED DEFAULT NULL,
  `old_value` JSON DEFAULT NULL,
  `new_value` JSON DEFAULT NULL,
  `ip_address` VARCHAR(45) NOT NULL,
  `user_agent` VARCHAR(255),
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`),
  KEY `idx_audit_logs_user` (`user_id`),
  KEY `idx_audit_logs_action` (`action`),
  KEY `idx_audit_logs_resource` (`resource_type`, `resource_id`),
  KEY `idx_audit_logs_created` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='操作日志表';

-- =============================================
-- 创建额外的性能优化索引
-- =============================================

-- 文章列表查询优化
CREATE INDEX idx_articles_list ON articles(status, published_at DESC, id);

-- 分类文章查询优化
CREATE INDEX idx_articles_category_list ON articles(category_id, status, published_at DESC);

-- 文章翻译查询优化
CREATE INDEX idx_article_trans_site ON article_translations(language_code, translation_status, published_at DESC);

-- 评论查询优化
CREATE INDEX idx_comments_article_tree ON comments(article_id, parent_id, status, created_at);

-- 标签文章查询优化
CREATE INDEX idx_article_tags_lookup ON article_tags(tag_id, article_id);

-- 热门文章查询优化
CREATE INDEX idx_articles_hot ON articles(status, view_count DESC, published_at DESC);

-- 用户登录优化
CREATE INDEX idx_users_login ON users(email, password, is_active);

-- 翻译工作流优化
CREATE INDEX idx_trans_workflow ON article_translations(translation_status, language_code, created_at);

-- =============================================
-- 创建触发器
-- =============================================

DELIMITER $$

-- 更新文章评论数（插入时）
CREATE TRIGGER update_comment_count_after_insert
AFTER INSERT ON comments
FOR EACH ROW
BEGIN
  IF NEW.status = 'approved' THEN
    UPDATE articles
    SET comment_count = comment_count + 1
    WHERE id = NEW.article_id;
  END IF;
END$$

-- 更新文章评论数（更新时）
CREATE TRIGGER update_comment_count_after_update
AFTER UPDATE ON comments
FOR EACH ROW
BEGIN
  IF OLD.status != 'approved' AND NEW.status = 'approved' THEN
    UPDATE articles
    SET comment_count = comment_count + 1
    WHERE id = NEW.article_id;
  ELSEIF OLD.status = 'approved' AND NEW.status != 'approved' THEN
    UPDATE articles
    SET comment_count = comment_count - 1
    WHERE id = NEW.article_id;
  END IF;
END$$

-- 更新文章评论数（删除时）
CREATE TRIGGER update_comment_count_after_delete
AFTER DELETE ON comments
FOR EACH ROW
BEGIN
  IF OLD.status = 'approved' THEN
    UPDATE articles
    SET comment_count = comment_count - 1
    WHERE id = OLD.article_id;
  END IF;
END$$

DELIMITER ;

-- =============================================
-- 输出成功信息
-- =============================================
SELECT 'All core tables created successfully' AS message;