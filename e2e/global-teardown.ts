/**
 * Playwright全局清理
 * 清理测试环境和临时文件
 */

import { FullConfig } from '@playwright/test';
import * as fs from 'fs';
import * as path from 'path';

async function globalTeardown(config: FullConfig) {
  console.log('🧹 Starting Playwright global teardown...');
  
  try {
    // 清理测试数据
    await cleanupTestData();
    
    // 清理认证状态文件
    await cleanupAuthStates();
    
    // 清理临时文件
    await cleanupTempFiles();
    
    console.log('✅ Playwright global teardown completed');
  } catch (error) {
    console.error('❌ Playwright global teardown failed:', error);
  }
}

/**
 * 清理测试数据
 */
async function cleanupTestData(): Promise<void> {
  try {
    const response = await fetch('http://localhost:3000/api/v1/test/clear-database', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
    });
    
    if (response.ok) {
      console.log('📊 Test database cleaned');
    } else {
      console.warn('⚠️  Failed to clean test database');
    }
  } catch (error) {
    console.warn('⚠️  Test database cleanup failed:', error);
  }
}

/**
 * 清理认证状态文件
 */
async function cleanupAuthStates(): Promise<void> {
  const authDir = path.join(__dirname, 'auth');
  
  try {
    if (fs.existsSync(authDir)) {
      const files = fs.readdirSync(authDir);
      
      for (const file of files) {
        if (file.endsWith('-state.json')) {
          const filePath = path.join(authDir, file);
          fs.unlinkSync(filePath);
          console.log(`🗑️  Removed auth state: ${file}`);
        }
      }
    }
  } catch (error) {
    console.warn('⚠️  Auth state cleanup failed:', error);
  }
}

/**
 * 清理临时文件
 */
async function cleanupTempFiles(): Promise<void> {
  const tempDirs = [
    'test-results',
    'screenshots',
    'videos',
    'traces',
  ];
  
  for (const dir of tempDirs) {
    try {
      if (fs.existsSync(dir)) {
        // 只清理测试生成的文件，保留目录结构
        const files = fs.readdirSync(dir);
        
        for (const file of files) {
          const filePath = path.join(dir, file);
          const stats = fs.statSync(filePath);
          
          if (stats.isFile() && isTestGeneratedFile(file)) {
            fs.unlinkSync(filePath);
          }
        }
        
        console.log(`🗑️  Cleaned temp directory: ${dir}`);
      }
    } catch (error) {
      console.warn(`⚠️  Failed to clean ${dir}:`, error);
    }
  }
}

/**
 * 判断是否为测试生成的文件
 */
function isTestGeneratedFile(filename: string): boolean {
  const testFilePatterns = [
    /\.png$/,
    /\.webm$/,
    /\.zip$/,
    /\.json$/,
    /\.html$/,
    /\.xml$/,
    /test-results-\d+/,
    /playwright-report/,
  ];
  
  return testFilePatterns.some(pattern => pattern.test(filename));
}

export default globalTeardown;