# 宠物博客站群系统 - Docker忽略文件
# 优化构建性能，排除不必要的文件

# ===========================================
# 版本控制
# ===========================================
.git
.gitignore
.gitattributes
.github

# ===========================================
# 环境配置文件
# ===========================================
.env
.env.local
.env.development
.env.production
.env.test
*.env

# ===========================================
# 依赖目录
# ===========================================
node_modules
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.npm
.yarn
.pnpm-debug.log*

# ===========================================
# 构建输出
# ===========================================
dist
build
coverage
.nyc_output

# ===========================================
# 开发工具
# ===========================================
.vscode
.idea
*.swp
*.swo
*~

# ===========================================
# 临时文件
# ===========================================
tmp
temp
*.tmp
*.log

# ===========================================
# 测试文件
# ===========================================
test
tests
spec
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# ===========================================
# 文档
# ===========================================
docs
*.md
!README.md

# ===========================================
# Docker相关
# ===========================================
Dockerfile*
docker-compose*.yml
.dockerignore

# ===========================================
# 上传文件
# ===========================================
uploads
public/uploads

# ===========================================
# 系统文件
# ===========================================
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# ===========================================
# 其他
# ===========================================
scripts
*.pid
*.seed
*.log