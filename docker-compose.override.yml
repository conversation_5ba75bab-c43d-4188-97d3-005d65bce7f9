# Docker Compose 开发环境覆盖配置
# 自动与 docker-compose.yml 合并使用
version: '3.8'

services:
  # Redis 开发环境优化
  redis:
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD} --maxmemory 256mb
    volumes:
      - redis_data:/data
      - ./redis.conf:/etc/redis/redis.conf:ro
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD:-dev_redis_pass}
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 后端开发环境配置
  backend:
    build:
      context: ./backend
      target: development
    command: npm run dev
    environment:
      - NODE_ENV=development
      - DEBUG=true
      - HOT_RELOAD=true
    volumes:
      # 支持热重载的卷映射
      - ./backend:/app:cached
      - /app/node_modules
      - backend_dev_logs:/app/logs
    ports:
      - "3000:3000"
      - "9229:9229"  # Node.js调试端口
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # 前端英文站点开发配置
  frontend-en:
    command: npm run dev
    environment:
      - NODE_ENV=development
      - ASTRO_TELEMETRY_DISABLED=1
    volumes:
      - ./frontend/en:/app/en:cached
      - /app/en/node_modules
      - /app/en/.astro
    ports:
      - "4321:4321"
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"

  # 前端德文站点开发配置
  frontend-de:
    command: npm run dev --port 4322
    environment:
      - NODE_ENV=development
      - ASTRO_TELEMETRY_DISABLED=1
    volumes:
      - ./frontend/de:/app/de:cached
      - /app/de/node_modules
      - /app/de/.astro
    ports:
      - "4322:4322"
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"

  # 前端俄文站点开发配置
  frontend-ru:
    command: npm run dev --port 4323
    environment:
      - NODE_ENV=development
      - ASTRO_TELEMETRY_DISABLED=1
    volumes:
      - ./frontend/ru:/app/ru:cached
      - /app/ru/node_modules
      - /app/ru/.astro
    ports:
      - "4323:4323"
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"

  # 管理后台开发配置
  frontend-admin:
    command: npm run dev --port 4324
    environment:
      - NODE_ENV=development
      - ASTRO_TELEMETRY_DISABLED=1
    volumes:
      - ./frontend/admin:/app/admin:cached
      - /app/admin/node_modules
      - /app/admin/.astro
    ports:
      - "4324:4324"
    logging:
      driver: "json-file"
      options:
        max-size: "5m"
        max-file: "2"

# 开发环境专用数据卷
volumes:
  backend_dev_logs:
    driver: local