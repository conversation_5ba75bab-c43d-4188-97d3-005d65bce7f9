# EditorConfig is awesome: https://EditorConfig.org

# Top-most EditorConfig file
root = true

# Unix-style newlines with a newline ending every file
[*]
charset = utf-8
end_of_line = lf
insert_final_newline = true
trim_trailing_whitespace = true
indent_style = space
indent_size = 2

# TypeScript and JavaScript files
[*.{ts,tsx,js,jsx}]
indent_size = 2
quote_type = single

# JSON files
[*.json]
indent_size = 2

# YAML files
[*.{yml,yaml}]
indent_size = 2

# Markdown files
[*.md]
trim_trailing_whitespace = false

# Astro files
[*.astro]
indent_size = 2

# CSS files
[*.{css,scss,sass}]
indent_size = 2

# HTML files
[*.html]
indent_size = 2

# Package files
[package.json]
indent_size = 2

# Makefile
[Makefile]
indent_style = tab

# Batch files
[*.bat]
end_of_line = crlf