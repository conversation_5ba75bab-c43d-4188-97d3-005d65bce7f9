# Redis 配置文件 - 宠物博客站群系统
# 优化开发环境使用

# 基础配置
bind 0.0.0.0
protected-mode yes
port 6379
timeout 300
keepalive 60

# 内存配置
maxmemory 512mb
maxmemory-policy allkeys-lru
maxmemory-samples 5

# 持久化配置
# 启用AOF持久化，更安全
appendonly yes
appendfsync everysec
auto-aof-rewrite-percentage 100
auto-aof-rewrite-min-size 64mb

# RDB备份配置（作为AOF的补充）
save 900 1
save 300 10  
save 60 10000
rdbcompression yes
rdbchecksum yes

# 日志配置
loglevel notice
logfile ""

# 客户端配置
tcp-keepalive 300
tcp-backlog 511

# 慢查询日志
slowlog-log-slower-than 10000
slowlog-max-len 128

# 安全配置
rename-command FLUSHDB ""
rename-command FLUSHALL ""
rename-command DEBUG ""

# 通知配置
notify-keyspace-events "Ex"

# 性能优化
hash-max-ziplist-entries 512
hash-max-ziplist-value 64
list-max-ziplist-size -2
list-compress-depth 0
set-max-intset-entries 512
zset-max-ziplist-entries 128
zset-max-ziplist-value 64