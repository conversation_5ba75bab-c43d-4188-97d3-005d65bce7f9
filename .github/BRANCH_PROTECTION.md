# 分支保护规则配置指南

本文档说明如何为宠物博客多语言站群系统配置GitHub分支保护规则，确保代码质量和部署安全。

## 分支策略

### 主要分支

- **main**: 生产分支，所有生产部署都来自此分支
- **develop**: 开发分支，用于集成功能开发
- **feature/***: 功能分支，用于开发新功能
- **hotfix/***: 热修复分支，用于紧急修复生产问题
- **release/***: 发布分支，用于版本发布准备

## 分支保护规则配置

### 1. main分支保护规则

在GitHub仓库设置中配置以下规则：

#### 基本设置
```yaml
分支名称规则: main
删除此规则: ❌ (禁用)
```

#### 推送限制
```yaml
限制推送: ✅ (启用)
- 限制谁可以推送到匹配的分支
- 允许用户和团队: 仅管理员和维护者
```

#### 合并前要求
```yaml
合并前需要PR审查: ✅ (启用)
- 需要审查的数量: 2
- 关闭过期的审查: ✅
- 需要代码所有者审查: ✅
- 限制可以关闭PR审查的用户: ✅

合并前需要状态检查: ✅ (启用)
- 在合并前必须是最新的: ✅
- 需要的状态检查:
  * CI 状态检查 (ci-status)
  * 代码质量检查 (code-quality)
  * 单元测试 (unit-tests)
  * 构建测试 (build-test)
  * 安全扫描 (security-scan)

合并前需要对话解决: ✅ (启用)
- 所有对话必须解决后才能合并

合并前需要签名提交: ✅ (启用)
- 需要提交签名验证
```

#### 其他限制
```yaml
限制强制推送: ✅ (启用)
- 禁止 git push --force

允许删除: ❌ (禁用)
- 防止意外删除主分支
```

### 2. develop分支保护规则

#### 基本设置
```yaml
分支名称规则: develop
删除此规则: ❌ (禁用)
```

#### 推送限制
```yaml
限制推送: ✅ (启用)
- 允许用户和团队: 开发团队成员
```

#### 合并前要求
```yaml
合并前需要PR审查: ✅ (启用)
- 需要审查的数量: 1
- 关闭过期的审查: ✅

合并前需要状态检查: ✅ (启用)
- 在合并前必须是最新的: ✅
- 需要的状态检查:
  * CI 状态检查 (ci-status)
  * 代码质量检查 (code-quality)
  * 单元测试 (unit-tests)

限制强制推送: ✅ (启用)
```

### 3. feature/*分支保护规则

#### 基本设置
```yaml
分支名称规则: feature/*
删除此规则: ❌ (禁用)
```

#### 推送限制
```yaml
限制推送: ❌ (禁用)
- 允许开发者自由推送到feature分支
```

#### 合并前要求
```yaml
合并前需要状态检查: ✅ (启用)
- 需要的状态检查:
  * 代码质量检查 (code-quality)
  * 构建测试 (build-test)
```

## PR模板配置

创建PR模板以确保信息完整：

### .github/pull_request_template.md

```markdown
## 变更描述
请简要描述这个PR的目的和变更内容。

## 变更类型
- [ ] 🚀 新功能 (feature)
- [ ] 🐛 问题修复 (bugfix)
- [ ] 📚 文档更新 (docs)
- [ ] 🎨 代码优化 (refactor)
- [ ] ⚡ 性能优化 (perf)
- [ ] 🧪 测试相关 (test)
- [ ] 🔧 构建相关 (build)

## 测试
- [ ] 单元测试通过
- [ ] 集成测试通过
- [ ] 手动测试通过
- [ ] 需要E2E测试 (添加 `needs-e2e` 标签)

## 影响范围
- [ ] 后端API
- [ ] 前端界面
- [ ] 数据库结构
- [ ] 配置文件
- [ ] 文档

## 破坏性变更
- [ ] 这个PR包含破坏性变更

如果是，请描述变更内容和迁移指南：

## 部署说明
- [ ] 需要数据库迁移
- [ ] 需要配置变更
- [ ] 需要特殊部署步骤

特殊说明：

## 检查清单
- [ ] 代码遵循项目规范
- [ ] 变更已经过测试
- [ ] 相关文档已更新
- [ ] 无安全问题
- [ ] 性能影响已评估
```

## 自动化规则设置

### 使用GitHub API设置分支保护 (可选)

```bash
#!/bin/bash
# 脚本：setup-branch-protection.sh
# 用途：自动设置分支保护规则

REPO_OWNER="your-username"
REPO_NAME="petcare-blog"
GITHUB_TOKEN="your-token"

# 设置main分支保护
curl -X PUT \
  -H "Authorization: token $GITHUB_TOKEN" \
  -H "Accept: application/vnd.github+json" \
  "https://api.github.com/repos/$REPO_OWNER/$REPO_NAME/branches/main/protection" \
  -d '{
    "required_status_checks": {
      "strict": true,
      "contexts": [
        "ci-status",
        "code-quality", 
        "unit-tests",
        "build-test",
        "security-scan"
      ]
    },
    "enforce_admins": true,
    "required_pull_request_reviews": {
      "required_approving_review_count": 2,
      "dismiss_stale_reviews": true,
      "require_code_owner_reviews": true
    },
    "restrictions": {
      "users": [],
      "teams": ["admin-team"]
    },
    "allow_force_pushes": false,
    "allow_deletions": false,
    "required_conversation_resolution": true,
    "required_signed_commits": true
  }'

# 设置develop分支保护  
curl -X PUT \
  -H "Authorization: token $GITHUB_TOKEN" \
  -H "Accept: application/vnd.github+json" \
  "https://api.github.com/repos/$REPO_OWNER/$REPO_NAME/branches/develop/protection" \
  -d '{
    "required_status_checks": {
      "strict": true,
      "contexts": [
        "ci-status",
        "code-quality",
        "unit-tests" 
      ]
    },
    "enforce_admins": false,
    "required_pull_request_reviews": {
      "required_approving_review_count": 1,
      "dismiss_stale_reviews": true
    },
    "restrictions": null,
    "allow_force_pushes": false,
    "allow_deletions": false
  }'

echo "分支保护规则设置完成"
```

## 权限管理

### 团队权限建议

```yaml
Admin Team (管理员):
  - 可以合并到main分支
  - 可以管理分支保护规则
  - 可以管理Secrets和环境变量
  - 可以强制合并紧急修复

Developer Team (开发者):
  - 可以创建feature分支
  - 可以提交PR到develop
  - 可以审查其他PR
  - 不能直接推送到main

Reviewer Team (审查者):
  - 专门负责代码审查
  - 具有approve权限
  - 包含技术负责人和资深开发者
```

### 外部贡献者

```yaml
外部贡献者规则:
  - 只能通过fork提交PR
  - PR需要内部团队成员审查
  - 首次贡献需要手动触发CI
  - 需要签署CLA协议
```

## 监控和报告

### 分支保护遵守情况

定期检查分支保护规则的遵守情况：

1. **审查绕过情况**：监控管理员绕过规则的频率
2. **失败的合并**：分析因为不满足条件被阻止的合并
3. **审查覆盖率**：确保所有PR都得到充分审查
4. **CI失败率**：监控CI检查的失败情况

### 报告自动化

可以设置定期报告，包含：
- 分支保护规则遵守统计
- PR审查时间统计  
- CI成功率统计
- 安全扫描结果汇总

## 故障排除

### 常见问题

1. **PR无法合并**
   - 检查所有required checks是否通过
   - 确认审查数量是否足够
   - 验证对话是否全部解决

2. **CI检查一直等待**
   - 检查workflow是否正确配置
   - 确认secrets是否正确设置
   - 查看Actions日志排查问题

3. **权限问题**
   - 确认团队成员权限配置
   - 检查分支保护规则设置
   - 验证token权限范围

## 最佳实践

1. **定期审查规则**：至少每季度检查一次分支保护规则
2. **自动化优先**：尽可能使用自动化检查而不是手动审查
3. **文档同步**：保持保护规则和文档的同步
4. **培训团队**：确保所有成员了解工作流程
5. **监控合规性**：建立监控机制确保规则得到遵守

---

*注意：分支保护规则的具体配置可能因为GitHub功能更新而有所变化，请以GitHub官方文档为准。*