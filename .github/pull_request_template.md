# Pull Request

## 📋 变更描述
请简要描述这个PR的目的和变更内容。

<!-- 请详细描述你的变更，包括背景、解决的问题、实现方案等 -->

## 🔄 变更类型
请选择相关的变更类型：

- [ ] 🚀 **新功能** (feature) - 添加新功能
- [ ] 🐛 **问题修复** (bugfix) - 修复现有功能的问题
- [ ] 📚 **文档更新** (docs) - 仅更新文档
- [ ] 🎨 **代码优化** (refactor) - 不改变功能的代码重构
- [ ] ⚡ **性能优化** (perf) - 提高性能的代码变更
- [ ] 🧪 **测试相关** (test) - 添加或修改测试
- [ ] 🔧 **构建相关** (build) - 影响构建系统或外部依赖的变更
- [ ] 🔒 **安全修复** (security) - 修复安全漏洞
- [ ] 🌐 **多语言** (i18n) - 国际化和本地化相关变更

## 🧪 测试情况
请确认以下测试状态：

- [ ] **单元测试通过** - 所有单元测试执行成功
- [ ] **集成测试通过** - API集成测试正常
- [ ] **前端测试通过** - 组件和E2E测试正常
- [ ] **手动测试完成** - 已进行充分的手动验证
- [ ] **需要E2E测试** - 如需要，请添加 `needs-e2e` 标签

### 测试说明
<!-- 如果有特殊的测试场景或需要关注的测试点，请在此说明 -->

## 📊 影响范围
此变更影响以下组件：

- [ ] **后端API** - Express/TypeScript后端服务
- [ ] **前端界面** - Astro/React前端组件
- [ ] **数据库结构** - MySQL数据库schema变更
- [ ] **配置文件** - 环境变量或配置变更
- [ ] **文档** - README、API文档等
- [ ] **CI/CD** - GitHub Actions工作流
- [ ] **部署脚本** - 部署相关脚本或配置

## ⚠️ 破坏性变更
- [ ] **此PR包含破坏性变更**

<!-- 如果包含破坏性变更，请详细描述变更内容和升级指南 -->
<details>
<summary>破坏性变更详情（如果有）</summary>

### 变更描述


### 影响范围


### 迁移指南


</details>

## 🚀 部署说明
部署时需要注意的事项：

- [ ] **需要数据库迁移** - 运行数据库迁移脚本
- [ ] **需要配置变更** - 更新环境变量或配置文件
- [ ] **需要重启服务** - 变更需要重启应用服务
- [ ] **需要清理缓存** - 清理Redis或CDN缓存
- [ ] **需要特殊部署步骤** - 有额外的部署要求

### 特殊部署说明
<!-- 如果有特殊的部署步骤或注意事项，请在此详细描述 -->

## 🔗 相关链接
<!-- 如果有相关的Issue、设计文档、讨论等，请在此提供链接 -->

- 关联Issue: #
- 设计文档: 
- 相关讨论: 

## 📸 截图/录屏
<!-- 如果是UI相关变更，请提供截图或录屏展示效果 -->

## ✅ 检查清单
在提交PR前，请确认以下事项：

### 代码质量
- [ ] **代码符合项目规范** - 遵循ESLint/Prettier配置
- [ ] **TypeScript类型正确** - 无类型错误，合理使用类型
- [ ] **代码已充分注释** - 关键逻辑有适当注释
- [ ] **无console.log等调试代码** - 清理了调试代码

### 功能完整性
- [ ] **功能已完整实现** - 按照需求完整实现功能
- [ ] **错误处理完善** - 有适当的错误处理和用户提示
- [ ] **边界条件考虑** - 考虑了各种边界情况
- [ ] **向后兼容** - 保持与现有功能的兼容性

### 测试覆盖
- [ ] **添加了相应测试** - 新功能有对应的单元测试
- [ ] **测试覆盖率达标** - 保持良好的测试覆盖率
- [ ] **所有测试通过** - 新旧测试都执行成功

### 文档更新
- [ ] **API文档已更新** - 如有API变更，更新了Swagger文档
- [ ] **README已更新** - 如需要，更新了项目README
- [ ] **变更日志已更新** - 在CHANGELOG.md中记录了变更

### 安全检查
- [ ] **无安全漏洞** - 代码无明显安全问题
- [ ] **敏感信息保护** - 无硬编码密码或密钥
- [ ] **输入验证完整** - 用户输入得到充分验证
- [ ] **权限控制正确** - 权限检查逻辑正确

### 性能考虑
- [ ] **性能影响已评估** - 评估了对性能的影响
- [ ] **数据库查询优化** - 优化了数据库查询
- [ ] **前端性能优化** - 考虑了前端加载性能
- [ ] **缓存策略合理** - 合理使用缓存机制

## 👥 审查者
请@相关团队成员进行代码审查：

- **技术审查**: @技术负责人
- **业务审查**: @产品负责人
- **安全审查**: @安全负责人（如涉及安全变更）

## 📝 其他说明
<!-- 任何其他需要审查者关注的信息 -->

---

**感谢你的贡献！** 🙏 
请确保所有检查项都已完成，这将帮助我们更快地审查和合并你的变更。