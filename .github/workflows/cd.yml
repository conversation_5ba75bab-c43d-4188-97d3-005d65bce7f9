# CD 工作流 - 持续部署
# 触发条件：push到main分支且CI检查通过
name: CD - 持续部署

on:
  push:
    branches: [ main ]
  workflow_dispatch: # 允许手动触发
    inputs:
      environment:
        description: '部署环境'
        required: true
        default: 'production'
        type: choice
        options:
        - production
        - staging

env:
  NODE_VERSION: '20'
  DEPLOY_PATH: '/www/wwwroot/petcare'

jobs:
  # 部署前置检查
  pre-deploy-check:
    name: 部署前置检查
    runs-on: ubuntu-latest
    timeout-minutes: 10

    outputs:
      should-deploy: ${{ steps.check.outputs.should-deploy }}
      
    steps:
      - name: 检出代码
        uses: actions/checkout@v4
        with:
          fetch-depth: 2

      - name: 检查是否需要部署
        id: check
        run: |
          # 检查最近提交是否包含跳过部署的标识
          if git log -1 --pretty=%B | grep -q "\[skip deploy\]"; then
            echo "should-deploy=false" >> $GITHUB_OUTPUT
            echo "检测到 [skip deploy] 标识，跳过部署"
          else
            echo "should-deploy=true" >> $GITHUB_OUTPUT
            echo "准备开始部署"
          fi

      - name: 验证环境变量
        run: |
          if [ -z "${{ secrets.SERVER_HOST }}" ]; then
            echo "❌ 缺少 SERVER_HOST 密钥"
            exit 1
          fi
          if [ -z "${{ secrets.SERVER_USER }}" ]; then
            echo "❌ 缺少 SERVER_USER 密钥"
            exit 1
          fi
          if [ -z "${{ secrets.SERVER_SSH_KEY }}" ]; then
            echo "❌ 缺少 SERVER_SSH_KEY 密钥"
            exit 1
          fi
          echo "✅ 环境变量验证通过"

  # 构建部署产物
  build-for-deploy:
    name: 构建部署版本
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: pre-deploy-check
    if: needs.pre-deploy-check.outputs.should-deploy == 'true'

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装依赖
        run: |
          npm ci
          cd backend && npm ci
          cd ../frontend/en && npm ci
          cd ../de && npm ci  
          cd ../ru && npm ci

      - name: 构建后端
        run: |
          cd backend
          npm run build
          # 复制必要文件
          cp package.json dist/
          cp package-lock.json dist/
          # 安装生产依赖
          cd dist && npm ci --only=production

      - name: 构建前端 (所有语言)
        run: |
          cd frontend/en && npm run build
          cd ../de && npm run build
          cd ../ru && npm run build

      - name: 准备部署包
        run: |
          mkdir -p deploy-package
          # 后端文件
          cp -r backend/dist deploy-package/backend
          cp backend/ecosystem.config.js deploy-package/backend/
          # 前端文件
          cp -r frontend/en/dist deploy-package/frontend-en
          cp -r frontend/de/dist deploy-package/frontend-de
          cp -r frontend/ru/dist deploy-package/frontend-ru

      - name: 上传部署包
        uses: actions/upload-artifact@v3
        with:
          name: deploy-package
          path: deploy-package/
          retention-days: 1

  # 部署到生产环境
  deploy-production:
    name: 部署到生产环境
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: [pre-deploy-check, build-for-deploy]
    if: needs.pre-deploy-check.outputs.should-deploy == 'true'
    environment:
      name: production
      url: https://www.petcare.com

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 下载部署包
        uses: actions/download-artifact@v3
        with:
          name: deploy-package
          path: deploy-package/

      - name: 部署到服务器
        uses: appleboy/ssh-action@v0.1.8
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          port: ${{ secrets.SERVER_PORT || 22 }}
          timeout: 300s
          script_stop: true
          script: |
            set -e
            
            # 设置变量
            DEPLOY_PATH="${{ env.DEPLOY_PATH }}"
            BACKUP_PATH="/www/backups/petcare-$(date +%Y%m%d_%H%M%S)"
            
            echo "=== 开始部署 $(date) ==="
            
            # 创建备份
            echo "创建备份..."
            mkdir -p /www/backups
            if [ -d "$DEPLOY_PATH" ]; then
              cp -r "$DEPLOY_PATH" "$BACKUP_PATH"
              echo "备份创建完成: $BACKUP_PATH"
            fi
            
            # 创建部署目录
            mkdir -p "$DEPLOY_PATH"/{backend,frontend,logs}
            
            # 停止现有服务
            echo "停止现有服务..."
            pm2 stop petcare-api || true
            
            # 更新代码
            echo "更新代码..."
            cd "$DEPLOY_PATH"
            
            # 拉取最新代码
            if [ -d .git ]; then
              git pull origin main
            else
              git clone https://github.com/yourrepo/petcare-blog.git .
            fi
            
            # 后端部署
            echo "部署后端..."
            cd backend
            npm install --production
            npm run build
            
            # 前端部署 (所有语言版本)
            echo "部署前端..."
            for lang in en de ru; do
              echo "部署前端 ($lang)..."
              cd "$DEPLOY_PATH/frontend/$lang"
              if [ ! -d .git ]; then
                git clone https://github.com/yourrepo/petcare-frontend.git .
              else
                git pull origin main
              fi
              npm install
              npm run build:$lang
              cp -r dist/* .
            done
            
            # 清理Redis缓存
            echo "清理缓存..."
            redis-cli -a ${{ secrets.REDIS_PASSWORD }} FLUSHDB
            
            # 重启服务
            echo "重启服务..."
            cd "$DEPLOY_PATH/backend"
            pm2 start ecosystem.config.js
            pm2 save
            
            # 重载Nginx
            echo "重载Nginx..."
            nginx -t && nginx -s reload
            
            # 健康检查
            echo "进行健康检查..."
            sleep 5
            if curl -f http://localhost:3000/api/v1/health; then
              echo "✅ 后端服务健康检查通过"
            else
              echo "❌ 后端服务健康检查失败"
              exit 1
            fi
            
            echo "=== 部署完成 $(date) ==="

      - name: 验证部署
        run: |
          echo "等待服务启动..."
          sleep 10
          
          # 检查主要端点
          echo "检查英文站点..."
          curl -f https://www.petcare.com/ || exit 1
          
          echo "检查API端点..."
          curl -f https://www.petcare.com/api/v1/health || exit 1
          
          echo "✅ 部署验证通过"

  # 部署后验证
  post-deploy-verification:
    name: 部署后验证
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: deploy-production
    if: always()

    steps:
      - name: 全面健康检查
        run: |
          echo "开始全面健康检查..."
          
          # 检查所有语言站点
          sites=(
            "https://www.petcare.com"
            "https://www.haustiere.de"
            "https://www.домашние-животные.рф"
          )
          
          for site in "${sites[@]}"; do
            echo "检查站点: $site"
            if curl -f -s -o /dev/null -w "%{http_code}" "$site" | grep -q "200"; then
              echo "✅ $site 正常"
            else
              echo "❌ $site 异常"
              exit 1
            fi
          done
          
          # 检查API端点
          echo "检查API端点..."
          if curl -f https://www.petcare.com/api/v1/health; then
            echo "✅ API服务正常"
          else
            echo "❌ API服务异常"
            exit 1
          fi

      - name: 性能基准测试
        run: |
          echo "运行性能基准测试..."
          # 使用curl测试响应时间
          response_time=$(curl -o /dev/null -s -w "%{time_total}" https://www.petcare.com)
          echo "首页响应时间: ${response_time}秒"
          
          # 检查响应时间是否在可接受范围内
          if (( $(echo "$response_time < 3.0" | bc -l) )); then
            echo "✅ 响应时间符合要求"
          else
            echo "⚠️  响应时间较慢，需要关注"
          fi

      - name: 发送通知
        if: always()
        run: |
          if [ "${{ needs.deploy-production.result }}" == "success" ]; then
            echo "📢 部署成功通知: 宠物博客系统已成功部署到生产环境"
            echo "- 部署时间: $(date)"
            echo "- 提交: ${{ github.sha }}"
            echo "- 分支: ${{ github.ref }}"
          else
            echo "🚨 部署失败通知: 宠物博客系统部署失败"
            echo "- 失败时间: $(date)"
            echo "- 提交: ${{ github.sha }}"
            echo "- 请检查部署日志并及时处理"
          fi

  # 回滚计划 (手动触发)
  rollback-plan:
    name: 回滚计划
    runs-on: ubuntu-latest
    timeout-minutes: 10
    if: failure() && github.event_name == 'workflow_dispatch'

    steps:
      - name: 执行回滚
        uses: appleboy/ssh-action@v0.1.8
        with:
          host: ${{ secrets.SERVER_HOST }}
          username: ${{ secrets.SERVER_USER }}
          key: ${{ secrets.SERVER_SSH_KEY }}
          port: ${{ secrets.SERVER_PORT || 22 }}
          script: |
            echo "开始执行回滚..."
            
            # 查找最新备份
            LATEST_BACKUP=$(ls -t /www/backups/petcare-* | head -n1)
            
            if [ -n "$LATEST_BACKUP" ]; then
              echo "找到备份: $LATEST_BACKUP"
              
              # 停止现有服务
              pm2 stop petcare-api || true
              
              # 恢复备份
              rm -rf ${{ env.DEPLOY_PATH }}
              cp -r "$LATEST_BACKUP" ${{ env.DEPLOY_PATH }}
              
              # 重启服务
              cd ${{ env.DEPLOY_PATH }}/backend
              pm2 start ecosystem.config.js
              
              echo "✅ 回滚完成"
            else
              echo "❌ 未找到可用备份"
              exit 1
            fi