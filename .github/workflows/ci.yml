# CI 工作流 - 持续集成
# 触发条件：PR到main和develop分支，以及push到feature分支
name: CI - 持续集成

on:
  pull_request:
    branches: [ main, develop ]
  push:
    branches: [ feature/**, hotfix/** ]
  workflow_dispatch: # 允许手动触发

env:
  NODE_VERSION: '20'
  CACHE_VERSION: v1

jobs:
  # 代码质量检查
  code-quality:
    name: 代码质量检查
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装根目录依赖
        run: npm ci

      - name: 安装后端依赖
        run: |
          cd backend
          npm ci

      - name: 安装前端依赖
        run: |
          cd frontend/en && npm ci
          cd ../de && npm ci  
          cd ../ru && npm ci

      - name: 代码格式检查
        run: npm run format:check

      - name: ESLint 代码检查
        run: npm run lint

      - name: TypeScript 类型检查 (后端)
        run: |
          cd backend
          npm run typecheck

      - name: 缓存代码质量结果
        uses: actions/cache@v3
        with:
          path: |
            ~/.npm
            node_modules
            backend/node_modules
            frontend/*/node_modules
          key: ${{ runner.os }}-code-quality-${{ env.CACHE_VERSION }}-${{ hashFiles('**/package-lock.json') }}

  # 单元测试
  unit-tests:
    name: 单元测试
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: code-quality

    strategy:
      matrix:
        test-group: [backend, frontend-en, frontend-de, frontend-ru]

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 恢复依赖缓存
        uses: actions/cache@v3
        with:
          path: |
            ~/.npm
            node_modules
            backend/node_modules
            frontend/*/node_modules
          key: ${{ runner.os }}-code-quality-${{ env.CACHE_VERSION }}-${{ hashFiles('**/package-lock.json') }}

      - name: 安装依赖 (如果缓存未命中)
        run: |
          npm ci
          cd backend && npm ci
          cd ../frontend/en && npm ci
          cd ../de && npm ci  
          cd ../ru && npm ci

      - name: 运行后端测试
        if: matrix.test-group == 'backend'
        run: |
          cd backend
          npm run test:coverage
        env:
          NODE_ENV: test

      - name: 运行前端测试 (英文)
        if: matrix.test-group == 'frontend-en'
        run: |
          cd frontend/en
          npm test -- --coverage

      - name: 运行前端测试 (德文)
        if: matrix.test-group == 'frontend-de'
        run: |
          cd frontend/de
          npm test -- --coverage

      - name: 运行前端测试 (俄文)
        if: matrix.test-group == 'frontend-ru'
        run: |
          cd frontend/ru
          npm test -- --coverage

      - name: 上传测试覆盖率报告
        uses: actions/upload-artifact@v3
        if: always()
        with:
          name: coverage-report-${{ matrix.test-group }}
          path: |
            backend/coverage/
            frontend/*/coverage/
          retention-days: 7

  # 构建测试
  build-test:
    name: 构建测试
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: code-quality

    strategy:
      matrix:
        component: [backend, frontend]

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 恢复依赖缓存
        uses: actions/cache@v3
        with:
          path: |
            ~/.npm
            node_modules
            backend/node_modules
            frontend/*/node_modules
          key: ${{ runner.os }}-code-quality-${{ env.CACHE_VERSION }}-${{ hashFiles('**/package-lock.json') }}

      - name: 安装依赖 (如果缓存未命中)
        run: |
          npm ci
          cd backend && npm ci
          cd ../frontend/en && npm ci
          cd ../de && npm ci  
          cd ../ru && npm ci

      - name: 构建后端
        if: matrix.component == 'backend'
        run: |
          cd backend
          npm run build
          # 验证构建产物
          test -f dist/index.js

      - name: 构建前端
        if: matrix.component == 'frontend'
        run: |
          # 构建所有语言版本
          cd frontend/en && npm run build
          cd ../de && npm run build
          cd ../ru && npm run build
          # 验证构建产物存在
          test -d en/dist
          test -d de/dist
          test -d ru/dist

      - name: 上传构建产物
        uses: actions/upload-artifact@v3
        with:
          name: build-${{ matrix.component }}
          path: |
            backend/dist/
            frontend/*/dist/
          retention-days: 1

  # E2E 测试 (可选，仅在需要时运行)
  e2e-tests:
    name: E2E 测试
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: [unit-tests, build-test]
    if: github.event_name == 'pull_request' && contains(github.event.pull_request.labels.*.name, 'needs-e2e')

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装依赖
        run: |
          npm ci
          cd backend && npm ci

      - name: 安装 Playwright
        run: npm run playwright:install

      - name: 下载构建产物
        uses: actions/download-artifact@v3
        with:
          name: build-backend
          path: backend/

      - name: 启动测试服务器
        run: |
          cd backend
          npm start &
          # 等待服务启动
          sleep 10
        env:
          NODE_ENV: test
          PORT: 3000

      - name: 运行 E2E 测试
        run: npm run test:e2e

      - name: 上传 E2E 测试结果
        uses: actions/upload-artifact@v3
        if: failure()
        with:
          name: e2e-test-results
          path: |
            e2e-results/
            test-results/
          retention-days: 7

  # 安全扫描
  security-scan:
    name: 安全扫描
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: code-quality

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 设置 Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: 安装依赖
        run: |
          npm ci
          cd backend && npm ci

      - name: 运行安全审计 (后端)
        run: |
          cd backend
          npm audit --audit-level=moderate

      - name: 运行安全审计 (前端)
        run: |
          cd frontend/en && npm audit --audit-level=moderate
          cd ../de && npm audit --audit-level=moderate  
          cd ../ru && npm audit --audit-level=moderate

  # 生成测试报告
  test-report:
    name: 生成测试报告
    runs-on: ubuntu-latest
    needs: [unit-tests, build-test]
    if: always()

    steps:
      - name: 检出代码
        uses: actions/checkout@v4

      - name: 下载所有覆盖率报告
        uses: actions/download-artifact@v3
        with:
          path: ./coverage-reports

      - name: 生成综合测试报告
        run: |
          echo "# 测试报告" > test-report.md
          echo "" >> test-report.md
          echo "## 构建状态" >> test-report.md
          echo "- 代码质量检查: ${{ needs.code-quality.result }}" >> test-report.md
          echo "- 单元测试: ${{ needs.unit-tests.result }}" >> test-report.md
          echo "- 构建测试: ${{ needs.build-test.result }}" >> test-report.md
          echo "- 安全扫描: ${{ needs.security-scan.result }}" >> test-report.md
          echo "" >> test-report.md
          echo "## 测试覆盖率" >> test-report.md
          echo "测试覆盖率报告已生成，请查看构建产物。" >> test-report.md

      - name: 上传测试报告
        uses: actions/upload-artifact@v3
        with:
          name: test-report
          path: test-report.md

  # CI 结果汇总
  ci-status:
    name: CI 状态检查
    runs-on: ubuntu-latest
    needs: [code-quality, unit-tests, build-test, security-scan]
    if: always()

    steps:
      - name: 检查所有 CI 作业状态
        run: |
          echo "代码质量检查: ${{ needs.code-quality.result }}"
          echo "单元测试: ${{ needs.unit-tests.result }}"
          echo "构建测试: ${{ needs.build-test.result }}"
          echo "安全扫描: ${{ needs.security-scan.result }}"
          
          if [[ "${{ needs.code-quality.result }}" == "failure" ]] || 
             [[ "${{ needs.unit-tests.result }}" == "failure" ]] || 
             [[ "${{ needs.build-test.result }}" == "failure" ]] || 
             [[ "${{ needs.security-scan.result }}" == "failure" ]]; then
            echo "❌ CI 检查失败，请修复问题后重试"
            exit 1
          else
            echo "✅ 所有 CI 检查通过"
          fi

      - name: PR 评论 (如果是 PR)
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v6
        with:
          script: |
            const { needs } = context.payload.workflow_run || {};
            const status = needs.code-quality?.result === 'success' &&
                          needs.unit-tests?.result === 'success' &&
                          needs.build-test?.result === 'success' &&
                          needs.security-scan?.result === 'success' ? '✅ 通过' : '❌ 失败';
            
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `## CI 检查结果 ${status}\n\n- 代码质量检查: ${{ needs.code-quality.result }}\n- 单元测试: ${{ needs.unit-tests.result }}\n- 构建测试: ${{ needs.build-test.result }}\n- 安全扫描: ${{ needs.security-scan.result }}`
            })