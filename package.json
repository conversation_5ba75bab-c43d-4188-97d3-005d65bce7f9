{"name": "petcare-blog", "version": "1.0.0", "description": "宠物博客多语言站群系统 - Pet Care Blog Multi-language System", "private": true, "scripts": {"test:mysql": "node scripts/test-mysql-connection.js", "check:env": "./scripts/check-dev-env.sh", "prepare": "husky install", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "lint:fix": "eslint . --ext .js,.jsx,.ts,.tsx --fix", "format": "prettier --write \"**/*.{js,jsx,ts,tsx,json,md,yml,yaml,css,scss,html}\"", "format:check": "prettier --check \"**/*.{js,jsx,ts,tsx,json,md,yml,yaml,css,scss,html}\"", "pre-commit": "lint-staged", "test": "npm run test:backend && npm run test:frontend", "test:backend": "cd backend && npm test", "test:frontend": "npm run test:frontend:en && npm run test:frontend:de && npm run test:frontend:ru", "test:frontend:en": "cd frontend/en && npm test", "test:frontend:de": "cd frontend/de && npm test", "test:frontend:ru": "cd frontend/ru && npm test", "test:e2e": "playwright test", "test:e2e:ui": "playwright test --ui", "test:e2e:headed": "playwright test --headed", "test:e2e:debug": "playwright test --debug", "test:coverage": "node scripts/coverage-report.js", "test:coverage:backend": "cd backend && npm test -- --coverage", "test:coverage:frontend": "npm run test:frontend:en -- --coverage && npm run test:frontend:de -- --coverage && npm run test:frontend:ru -- --coverage", "test:coverage:simple": "npm run test:backend -- --coverage && npm run test:frontend:en -- --coverage", "test:all": "npm run test && npm run test:e2e", "playwright:install": "playwright install", "playwright:install-deps": "playwright install-deps"}, "keywords": ["pet", "blog", "multi-language", "seo"], "author": "PetCare Team", "license": "MIT", "devDependencies": {"@eslint/js": "^9.32.0", "@playwright/test": "^1.49.0", "@typescript-eslint/eslint-plugin": "^8.39.0", "@typescript-eslint/parser": "^8.39.0", "eslint": "^9.32.0", "husky": "^8.0.0", "lint-staged": "^16.1.4", "mysql2": "^3.9.0", "prettier": "^3.6.2", "typescript": "^5.9.2"}}