# 宠物博客站群系统 Docker Compose 配置
# 支持开发环境的完整服务编排
version: '3.8'

services:
  # Redis 缓存服务
  redis:
    image: redis:7.2-alpine
    container_name: petcare-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    command: redis-server --appendonly yes --requirepass ${REDIS_PASSWORD}
    volumes:
      - redis_data:/data
      - ./redis.conf:/etc/redis/redis.conf:ro
    environment:
      - REDIS_PASSWORD=${REDIS_PASSWORD}
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "incr", "ping"]
      interval: 30s
      timeout: 3s
      retries: 5
    networks:
      - petcare-network

  # 后端 API 服务
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: petcare-backend
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=${NODE_ENV:-development}
      - PORT=3000
      - API_PREFIX=/api/v1
      # 数据库配置
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT:-3306}
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      # Redis 配置
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      # JWT 配置
      - JWT_SECRET=${JWT_SECRET}
      - JWT_EXPIRES_IN=${JWT_EXPIRES_IN:-7d}
      # AI 翻译配置
      - GEMINI_API_ENDPOINT=${GEMINI_API_ENDPOINT}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - GEMINI_MODEL=${GEMINI_MODEL:-gemini-2.5-pro}
      # 文件上传配置
      - UPLOAD_DIR=/app/uploads
      - MAX_FILE_SIZE=${MAX_FILE_SIZE:-10485760}
    volumes:
      - ./backend:/app:cached
      - /app/node_modules
      - backend_uploads:/app/uploads
      - backend_logs:/app/logs
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "node", "healthcheck.js"]
      interval: 30s
      timeout: 5s
      retries: 3
    networks:
      - petcare-network

  # 前端英文站点
  frontend-en:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        SITE_DIR: en
        BUILD_ENV: ${NODE_ENV:-development}
    container_name: petcare-frontend-en
    restart: unless-stopped
    ports:
      - "4321:80"
    environment:
      - SITE_LANG=en
      - API_BASE_URL=http://backend:3000/api/v1
    volumes:
      - ./frontend/en:/app/en:cached
      - /app/en/node_modules
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 3s
      retries: 3
    networks:
      - petcare-network

  # 前端德文站点
  frontend-de:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        SITE_DIR: de
        BUILD_ENV: ${NODE_ENV:-development}
    container_name: petcare-frontend-de
    restart: unless-stopped
    ports:
      - "4322:80"
    environment:
      - SITE_LANG=de
      - API_BASE_URL=http://backend:3000/api/v1
    volumes:
      - ./frontend/de:/app/de:cached
      - /app/de/node_modules
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 3s
      retries: 3
    networks:
      - petcare-network

  # 前端俄文站点
  frontend-ru:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        SITE_DIR: ru
        BUILD_ENV: ${NODE_ENV:-development}
    container_name: petcare-frontend-ru
    restart: unless-stopped
    ports:
      - "4323:80"
    environment:
      - SITE_LANG=ru
      - API_BASE_URL=http://backend:3000/api/v1
    volumes:
      - ./frontend/ru:/app/ru:cached
      - /app/ru/node_modules
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 3s
      retries: 3
    networks:
      - petcare-network

  # 管理后台
  frontend-admin:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      args:
        SITE_DIR: admin
        BUILD_ENV: ${NODE_ENV:-development}
    container_name: petcare-admin
    restart: unless-stopped
    ports:
      - "4324:80"
    environment:
      - SITE_LANG=admin
      - API_BASE_URL=http://backend:3000/api/v1
    volumes:
      - ./frontend/admin:/app/admin:cached
      - /app/admin/node_modules
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 3s
      retries: 3
    networks:
      - petcare-network

# 数据卷定义
volumes:
  redis_data:
    driver: local
  backend_uploads:
    driver: local
  backend_logs:
    driver: local

# 网络配置
networks:
  petcare-network:
    driver: bridge
    ipam:
      config:
        - subnet: **********/16